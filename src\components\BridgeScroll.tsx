import React from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useAnalytics } from "@/hooks/useAnalytics";

try {
  if (typeof window !== "undefined") {
    gsap.registerPlugin(ScrollTrigger);
  }
} catch {}

const BridgeScroll: React.FC = () => {
  const ref = React.useRef<HTMLDivElement>(null);
  const { trackEvent } = useAnalytics();

  React.useEffect(() => {
    if (!ref.current) return;
    // Respect reduced motion
    try {
      if (
        window.matchMedia &&
        window.matchMedia("(prefers-reduced-motion: reduce)").matches
      )
        return;
    } catch {}
    const container = ref.current;
    const s3 = document.querySelector("#s3") as HTMLElement | null;
    const s4 = document.querySelector("#s4") as HTMLElement | null;
    const s5 = document.querySelector("#s5") as HTMLElement | null;
    if (!s3 || !s4 || !s5) return;

    // Build a pinned, scrubbed timeline anchored at Section 4
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: s4,
        start: "top 80%",
        end: "bottom top",
        scrub: true,
        pin: false,
        invalidateOnRefresh: true,
      },
    });

    // Phase A: bring Section 4 in as Section 3 fades out
    tl.to(s3, { opacity: 0, duration: 0.3, ease: "power2.inOut" }, 0)
      .fromTo(
        s4,
        { opacity: 0, y: 16 },
        {
          opacity: 1,
          y: 0,
          duration: 0.5,
          ease: "power2.inOut",
          immediateRender: false,
        },
        0
      )
      // dwell window (nothing animates)
      .to({}, { duration: 0.4 })
      // Phase B: cross-fade Section 4 out and Section 5 in
      .to(s4, { opacity: 0, duration: 0.3, ease: "power2.inOut" })
      .fromTo(
        s5,
        { opacity: 0, y: 16 },
        {
          opacity: 1,
          y: 0,
          duration: 0.5,
          ease: "power2.inOut",
          immediateRender: false,
        },
        "<"
      );

    return () => {
      try {
        tl.scrollTrigger?.kill();
      } catch {}
      try {
        tl.kill();
      } catch {}
    };
  }, []);

  // Analytics triggers for bridge entries
  React.useEffect(() => {
    try {
      gsap.registerPlugin(ScrollTrigger);
    } catch {}
    const s4 = document.querySelector("#s4") as HTMLElement | null;
    const s5 = document.querySelector("#s5") as HTMLElement | null;
    if (!s4 || !s5) return;
    const t1 = ScrollTrigger.create({
      trigger: s4,
      start: "top top",
      onEnter: () => {
        try {
          trackEvent && trackEvent("bridge_enter_s4");
        } catch {}
      },
    });
    const t2 = ScrollTrigger.create({
      trigger: s5,
      start: "top bottom",
      onEnter: () => {
        try {
          trackEvent && trackEvent("bridge_enter_s5");
        } catch {}
      },
    });
    return () => {
      t1.kill();
      t2.kill();
    };
  }, [trackEvent]);

  // Invisible bridge element occupying small space to attach the trigger (non-invasive)
  return <div ref={ref} style={{ height: 0 }} />;
};

export default BridgeScroll;

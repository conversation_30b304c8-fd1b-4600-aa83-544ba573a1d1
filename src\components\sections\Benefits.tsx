import {
  Clock,
  Target,
  Globe,
  DollarSign,
  TrendingUp,
  Award,
} from "lucide-react";
import tumorBoardImage from "@/assets/tumor-board.jpg";

const Benefits = () => {
  const benefits = [
    {
      icon: <Clock className="h-6 w-6 text-primary" />,
      title: "Operational Efficiency",
      metric: "20-30%",
      description:
        "Reduction in tumor board preparation time with minimized errors from data silos.",
      detail: "Streamlined workflows save hours of manual data compilation",
    },
    {
      icon: <Target className="h-6 w-6 text-accent" />,
      title: "Clinical Outcomes",
      metric: "Faster",
      description:
        "Enables faster, informed decisions with improved trial matching and personalized care.",
      detail: "Enhanced precision in treatment planning and patient outcomes",
    },
    {
      icon: <Globe className="h-6 w-6 text-success" />,
      title: "Accessibility",
      metric: "Tier 2/3",
      description:
        "Empowers tier-2/3 hospitals with low-cost interoperability solutions.",
      detail: "Democratizing advanced healthcare technology across India",
    },
    {
      icon: <DollarSign className="h-6 w-6 text-primary" />,
      title: "Economic Impact",
      metric: "2-3x ROI",
      description:
        "Projected return on investment within one year of implementation.",
      detail:
        "Aligns with India's USD 10.7 billion connected health market by 2030",
    },
  ];

  const stats = [
    { label: "ABHA IDs Created", value: "73.98 Cr", growth: "+15%" },
    { label: "Health Facilities", value: "3.63 L", growth: "+22%" },
    { label: "Verified Professionals", value: "5.65 L", growth: "+18%" },
    { label: "Target by 2025", value: "500M+", growth: "Growing" },
  ];

  return (
    <section id="benefits" className="bg-background py-16">
      <div className="container mx-auto px-6">
        <div className="mb-16 text-center">
          <div className="mb-4 inline-flex items-center rounded-full bg-success-light px-4 py-2 text-sm font-medium text-success">
            📈 Impact & Benefits
          </div>
          <h2 className="mb-4 text-4xl font-bold text-foreground">
            Transforming Cancer Care Delivery
          </h2>
          <p className="mx-auto max-w-3xl text-xl text-muted-foreground">
            Our interoperability platform delivers measurable improvements in
            efficiency, outcomes, and accessibility across India's healthcare
            ecosystem.
          </p>
        </div>

        <div className="mb-20 grid items-center gap-16 lg:grid-cols-2">
          <div className="space-y-8">
            {benefits.map((benefit, index) => (
              <div
                key={index}
                className="group rounded-xl border bg-card p-6 transition-all duration-300 hover:shadow-soft"
              >
                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-lg bg-muted transition-transform group-hover:scale-105">
                    {benefit.icon}
                  </div>
                  <div className="flex-1">
                    <div className="mb-2 flex items-center gap-3">
                      <h3 className="text-xl font-semibold text-card-foreground">
                        {benefit.title}
                      </h3>
                      <span className="bg-gradient-primary rounded-full px-3 py-1 text-sm font-bold text-white">
                        {benefit.metric}
                      </span>
                    </div>
                    <p className="mb-2 text-muted-foreground">
                      {benefit.description}
                    </p>
                    <p className="text-sm italic text-muted-foreground/80">
                      {benefit.detail}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="relative">
            <img
              src={tumorBoardImage}
              alt="AI-powered tumor board meeting"
              className="shadow-strong w-full rounded-2xl"
            />
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-primary/20 to-transparent"></div>

            {/* Overlay stats */}
            <div className="absolute left-4 top-4 rounded-xl border bg-card/95 p-4 shadow-medium backdrop-blur-sm">
              <div className="text-sm text-muted-foreground">
                Preparation Time Saved
              </div>
              <div className="text-2xl font-bold text-success">20-30%</div>
            </div>

            <div className="absolute bottom-4 right-4 rounded-xl border bg-card/95 p-4 shadow-medium backdrop-blur-sm">
              <div className="text-sm text-muted-foreground">
                Clinical Accuracy
              </div>
              <div className="text-2xl font-bold text-accent">+85%</div>
            </div>
          </div>
        </div>

        {/* ABDM Alignment Stats */}
        <div className="relative">
          <div className="mb-12 text-center">
            <div className="mb-4 inline-flex items-center rounded-full bg-accent-light px-4 py-2 text-sm font-medium text-accent">
              🇮🇳 ABDM Ecosystem Growth
            </div>
            <h3 className="mb-4 text-3xl font-bold text-foreground">
              Aligned with India's Digital Health Mission
            </h3>
            <p className="mx-auto max-w-2xl text-lg text-muted-foreground">
              Our platform leverages the growing ABDM infrastructure to
              accelerate healthcare digitization across India.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-4">
            {stats.map((stat, index) => (
              <div
                key={index}
                className="bg-gradient-subtle rounded-xl border p-6 text-center transition-all duration-300 hover:shadow-soft"
              >
                <div className="mb-2 text-3xl font-bold text-foreground">
                  {stat.value}
                </div>
                <div className="mb-2 text-sm text-muted-foreground">
                  {stat.label}
                </div>
                <div className="inline-flex items-center rounded-full bg-success-light px-2 py-1 text-xs font-medium text-success">
                  <TrendingUp className="mr-1 h-3 w-3" />
                  {stat.growth}
                </div>
              </div>
            ))}
          </div>

          <div className="mt-12 rounded-2xl bg-gradient-hero p-8 text-center text-white">
            <div className="mb-4 flex items-center justify-center">
              <Award className="mr-3 h-8 w-8" />
              <h4 className="text-2xl font-bold">
                Regulatory Compliance Excellence
              </h4>
            </div>
            <p className="mb-6 text-lg opacity-90">
              Fully compliant with DPDP Act 2023, ABDM Guidelines, FHIR R4
              standards, and international regulations including GDPR, HIPAA,
              and FDA guidelines.
            </p>
            <div className="grid gap-4 text-sm md:grid-cols-3">
              <div className="rounded-lg bg-white/10 p-3">
                <div className="font-semibold">Local Compliance</div>
                <div className="opacity-90">DPDP • CDSCO • ICMR • NDHM</div>
              </div>
              <div className="rounded-lg bg-white/10 p-3">
                <div className="font-semibold">Global Standards</div>
                <div className="opacity-90">GDPR • HIPAA • FDA • EU AI Act</div>
              </div>
              <div className="rounded-lg bg-white/10 p-3">
                <div className="font-semibold">Technical Standards</div>
                <div className="opacity-90">FHIR R4 • HL7 • DICOM • IHE</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Benefits;

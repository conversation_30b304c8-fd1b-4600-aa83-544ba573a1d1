@layer utilities {
  @keyframes organic-float {
    0%,
    100% {
      transform: translateY(0) rotate(0deg) scale(1);
      opacity: 0.6;
    }
    25% {
      transform: translateY(-15px) rotate(3deg) scale(1.02);
      opacity: 0.8;
    }
    50% {
      transform: translateY(-25px) rotate(-2deg) scale(1.05);
      opacity: 1;
    }
    75% {
      transform: translateY(-10px) rotate(1deg) scale(1.02);
      opacity: 0.9;
    }
  }
  @keyframes organic-pulse {
    0%,
    100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.05);
      opacity: 0.8;
    }
  }
  @keyframes organic-breathe {
    0%,
    100% {
      transform: scale(1) rotate(0deg);
      filter: blur(0);
    }
    50% {
      transform: scale(1.1) rotate(2deg);
      filter: blur(1px);
    }
  }
  @keyframes liquid-morph {
    0% {
      border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    }
    25% {
      border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
    }
    50% {
      border-radius: 50% 60% 30% 60% / 30% 60% 70% 40%;
    }
    75% {
      border-radius: 60% 40% 60% 40% / 70% 30% 60% 40%;
    }
    100% {
      border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    }
  }
  @keyframes fade-up {
    from {
      opacity: 0;
      transform: translateY(40px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
  @keyframes fade-down {
    from {
      opacity: 0;
      transform: translateY(-40px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
  @keyframes fade-left {
    from {
      opacity: 0;
      transform: translateX(-40px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateX(0) scale(1);
    }
  }
  @keyframes fade-right {
    from {
      opacity: 0;
      transform: translateX(40px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateX(0) scale(1);
    }
  }
  @keyframes scale-in {
    from {
      opacity: 0;
      transform: scale(0.8);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
  @keyframes rotate-in {
    from {
      opacity: 0;
      transform: rotate(-10deg) scale(0.9);
    }
    to {
      opacity: 1;
      transform: rotate(0deg) scale(1);
    }
  }
  @keyframes button-press {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(0.95);
    }
    100% {
      transform: scale(1);
    }
  }
  @keyframes card-lift {
    from {
      transform: translateY(0);
      box-shadow: 0 4px 20px rgba(34, 60, 120, 0.08);
    }
    to {
      transform: translateY(-8px);
      box-shadow: 0 20px 60px rgba(34, 60, 120, 0.15);
    }
  }
  @keyframes input-focus {
    from {
      transform: scale(1);
      box-shadow: 0 0 0 0 hsl(var(--ring));
    }
    to {
      transform: scale(1.02);
      box-shadow: 0 0 0 3px hsl(var(--ring) / 0.3);
    }
  }
  @keyframes ripple {
    0% {
      transform: scale(0);
      opacity: 0.6;
    }
    100% {
      transform: scale(4);
      opacity: 0;
    }
  }
  @keyframes stagger-fade-in {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  @keyframes wave {
    0%,
    40%,
    100% {
      transform: scaleY(0.4);
    }
    20% {
      transform: scaleY(1);
    }
  }
  .animate-organic-float {
    animation: organic-float 8s ease-in-out infinite;
  }
  .animate-organic-pulse {
    animation: organic-pulse 3s ease-in-out infinite;
  }
  .animate-organic-breathe {
    animation: organic-breathe 4s ease-in-out infinite;
  }
  .animate-liquid-morph {
    animation: liquid-morph 10s ease-in-out infinite;
  }
  .animate-fade-up {
    animation: fade-up 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }
  .animate-fade-down {
    animation: fade-down 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }
  .animate-fade-left {
    animation: fade-left 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }
  .animate-fade-right {
    animation: fade-right 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }
  .animate-scale-in {
    animation: scale-in 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }
  .animate-rotate-in {
    animation: rotate-in 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }
  .animate-button-press {
    animation: button-press 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  .animate-card-lift {
    animation: card-lift 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }
  .animate-input-focus {
    animation: input-focus 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }
  .animate-ripple {
    animation: ripple 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  .animate-stagger-1 {
    animation: stagger-fade-in 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.1s
      both;
  }
  .animate-stagger-2 {
    animation: stagger-fade-in 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s
      both;
  }
  .animate-stagger-3 {
    animation: stagger-fade-in 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s
      both;
  }
  .animate-stagger-4 {
    animation: stagger-fade-in 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.4s
      both;
  }
  .animate-stagger-5 {
    animation: stagger-fade-in 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.5s
      both;
  }
  .animate-wave {
    animation: wave 1.2s infinite ease-in-out;
  }
  .hover-lift {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(34, 60, 120, 0.12);
  }
  .hover-scale {
    transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  .hover-scale:hover {
    transform: scale(1.05);
  }
  .hover-glow {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  .hover-glow:hover {
    box-shadow: 0 0 30px hsl(var(--primary) / 0.3);
  }
  .scroll-reveal {
    opacity: 0;
    transform: translateY(40px);
    transition: none;
  }
  .scroll-reveal.animate {
    opacity: 1;
    transform: translateY(0);
  }
  @media (prefers-reduced-motion: reduce) {
    .animate-organic-float,
    .animate-organic-pulse,
    .animate-organic-breathe,
    .animate-liquid-morph {
      animation: none;
    }
    .scroll-reveal {
      opacity: 1;
      transform: none;
    }
    .hover-lift:hover,
    .hover-scale:hover {
      transform: none;
    }
  }
}

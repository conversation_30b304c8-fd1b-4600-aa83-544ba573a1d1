"use client";

import {
  AnimatePresence,
  motion,
  useReducedMotion,
  useScroll,
  useTransform,
} from "framer-motion";
import React from "react";
import { useAnalytics } from "../../hooks/useAnalytics";
import TimelineView from "./TimelineView";

/**
 * Aura Branches Component
 *
 * Interactive branch visualization with scroll-triggered animations.
 * Features timeline views, analytics tracking, and responsive design.
 */

export interface Branch {
  id: string;
  title: string;
  subtitle: string;
  bgColor: string;
  accentColor: string;
}

const DEFAULT_BRANCHES: Branch[] = [
  {
    id: "auto-fetch",
    title: "AUTO-FETCH",
    subtitle: "& CONSENT",
    bgColor: "linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)",
    accentColor: "#06b6d4",
  },
  {
    id: "timeline",
    title: "ONE TIMELINE",
    subtitle: "ONE PATIENT",
    bgColor: "linear-gradient(135deg, #10b981 0%, #059669 100%)",
    accentColor: "#10b981",
  },
  {
    id: "board-ready",
    title: "BOARD-READY",
    subtitle: "SUMMARY",
    bgColor: "linear-gradient(135deg, #fef9c3 0%, #fef08a 50%, #fefce8 100%)",
    accentColor: "#fef9c3",
  },
  {
    id: "safer-actions",
    title: "SAFER ACTIONS",
    subtitle: "& FOLLOW-UPS",
    bgColor: "linear-gradient(135deg, #ec4899 0%, #db2777 100%)",
    accentColor: "#ec4899",
  },
];

// Minimal error boundary to prevent a hard blank if a module throws at runtime
class ModuleErrorBoundary extends React.Component<
  { fallback?: React.ReactNode; children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: {
    fallback?: React.ReactNode;
    children: React.ReactNode;
  }) {
    super(props);
    this.state = { hasError: false };
  }
  static getDerivedStateFromError() {
    return { hasError: true };
  }
  componentDidCatch() {
    /* noop, console already captures */
  }
  render() {
    if (this.state.hasError) return this.props.fallback ?? null;
    return this.props.children;
  }
}

// Component for AUTO-FETCH & CONSENT module
const AutoFetchView = () => {
  const prefersReduced = useReducedMotion();
  const { trackEvent } = useAnalytics();
  const [viewMode, setViewMode] = React.useState<"summary" | "full">(() => {
    if (typeof window === "undefined") return "summary";
    return (
      (localStorage.getItem("aura-view-mode") as "summary" | "full") ||
      "summary"
    );
  });
  React.useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("aura-view-mode", viewMode);
    }
  }, [viewMode]);

  return (
    <motion.div
      initial={false}
      animate={{ opacity: 1 }}
      transition={{ duration: 0 }}
      className="flex h-full flex-col"
    >
      {/* Mockup with overlay text */}
      <motion.div
        className="relative w-full flex-1"
        initial={false}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0 }}
      >
        {/* Header area above mockup for text */}
        <div className="relative px-8 pb-6 pt-24 md:px-16 md:pb-8 md:pt-32">
          <div className="flex flex-col justify-center md:flex-row md:items-start md:gap-20">
            <h2 className="max-w-[18ch] text-center text-6xl font-light leading-[1.05] text-white md:text-left md:text-7xl lg:text-8xl">
              <span className="whitespace-nowrap">Link once,</span>
              <br />
              <span className="whitespace-nowrap font-light italic">
                never chase again
              </span>
            </h2>
            <div className="mt-6 max-w-2xl md:mt-4">
              <p className="text-lg font-light text-white/90">
                Auto‑fetch records and honor consent with full provenance.
              </p>
              <div
                role="list"
                aria-label="Capabilities"
                className="mt-3 flex flex-wrap gap-2"
              >
                {[
                  "ABDM linking",
                  "OCR → FHIR",
                  "Audit trail",
                  "Outside PDFs",
                  "Imaging",
                  "Labs",
                ].map((cap) => (
                  <span
                    key={cap}
                    role="listitem"
                    className="inline-flex items-center gap-1 rounded-full border border-white/60 bg-white/90 px-3 py-1 text-xs font-medium text-slate-800 backdrop-blur-md focus:outline-none focus-visible:ring-2 focus-visible:ring-white/80"
                    aria-label={cap}
                  >
                    {cap}
                  </span>
                ))}
              </div>
            </div>
          </div>
          {/* Liquid-glass tab aligned to heading vertical midpoint */}
          <div className="absolute left-6 top-1/2 hidden -translate-y-1/2 md:left-12 md:block">
            <div className="inline-flex items-center gap-2 rounded-[26px] border border-white/70 bg-white/90 px-7 py-3.5 shadow-[0_10px_28px_rgba(0,0,0,0.12)] backdrop-blur-2xl">
              <div
                className="h-2 w-2 rounded-full"
                style={{ backgroundColor: "#06b6d4" }}
              />
              <div className="flex items-baseline gap-1">
                <span className="text-[12px] font-bold tracking-wider text-slate-800">
                  AUTO‑FETCH
                </span>
                <span className="text-[11px] font-medium tracking-wide text-slate-600">
                  & CONSENT
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="h-[65vh] w-full overflow-hidden rounded-t-2xl bg-white shadow-2xl md:h-[70vh] lg:h-[74vh]">
          {/* App Header */}
          <div className="border-b border-gray-200 bg-gray-50 px-6 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-6">
                <div className="flex items-center gap-2">
                  <img src="/logo.png" alt="Entheory" className="h-6 w-auto" />
                </div>
                <nav className="flex items-center gap-6">
                  <button className="text-sm font-medium text-cyan-600">
                    Records
                  </button>
                  <button className="text-sm text-gray-600 hover:text-gray-900">
                    Timeline
                  </button>
                  <button className="text-sm text-gray-600 hover:text-gray-900">
                    Summary
                  </button>
                  <button className="text-sm text-gray-600 hover:text-gray-900">
                    Actions
                  </button>
                </nav>
              </div>
              <div className="flex items-center gap-3">
                <button className="rounded-lg bg-cyan-500 px-4 py-1.5 text-sm font-medium text-white hover:bg-cyan-600 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/70">
                  Sync Now
                </button>
              </div>
            </div>
          </div>

          {/* Sub Navigation */}
          <div className="border-b border-gray-200 bg-white px-6 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-8">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500">Patient:</span>
                  <span className="text-sm font-medium text-gray-900">
                    Priya Sharma
                  </span>
                  <span className="rounded bg-green-100 px-2 py-0.5 text-xs font-medium text-green-700">
                    ABDM Linked
                  </span>
                  <span className="text-xs text-gray-500">17:24 IST</span>
                </div>
                <div className="flex items-center gap-4">
                  <button className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900">
                    <svg
                      className="h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
                      />
                    </svg>
                    All Records
                  </button>
                  <button className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900">
                    <svg
                      className="h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"
                      />
                    </svg>
                    Show Filters
                  </button>
                  <input
                    type="text"
                    placeholder="Search records..."
                    className="w-64 rounded-lg border border-gray-300 bg-gray-50 px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500"
                  />
                  {/* View mode toggle */}
                  <div
                    className="ml-2 inline-flex overflow-hidden rounded-full border border-gray-300"
                    role="group"
                    aria-label="View mode"
                  >
                    <button
                      className={`px-3 py-1 text-xs font-medium ${viewMode === "summary" ? "bg-gray-100 text-gray-900" : "bg-white text-gray-600"}`}
                      onClick={() => {
                        setViewMode("summary");
                        try {
                          trackEvent &&
                            trackEvent("view_mode_change", { mode: "summary" });
                        } catch {}
                      }}
                      aria-pressed={viewMode === "summary"}
                    >
                      Summary
                    </button>
                    <button
                      className={`px-3 py-1 text-xs font-medium ${viewMode === "full" ? "bg-gray-100 text-gray-900" : "bg-white text-gray-600"}`}
                      onClick={() => {
                        setViewMode("full");
                        try {
                          trackEvent &&
                            trackEvent("view_mode_change", { mode: "full" });
                        } catch {}
                      }}
                      aria-pressed={viewMode === "full"}
                    >
                      Full
                    </button>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <div className="hidden items-center gap-2 text-xs text-gray-600 md:flex">
                  <span className="rounded bg-gray-100 px-2 py-0.5">
                    Consent v1.2
                  </span>
                  <button
                    className="underline hover:text-gray-900"
                    onClick={() => {
                      try {
                        trackEvent && trackEvent("audit_trail_view_click");
                      } catch {}
                    }}
                  >
                    View audit
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="flex-1 bg-gray-50 px-10 py-8">
            {/* Stage Headers with counts and Conflicts lane */}
            <div className="mb-6 grid grid-cols-5 gap-8">
              <div className="text-center">
                <p className="text-sm font-bold uppercase tracking-wider text-gray-600">
                  Fetching (1)
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm font-bold uppercase tracking-wider text-gray-600">
                  Processing (1)
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm font-bold uppercase tracking-wider text-amber-600">
                  Conflicts (0)
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm font-bold uppercase tracking-wider text-gray-600">
                  Linked (1)
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm font-bold uppercase tracking-wider text-gray-600">
                  Complete (1)
                </p>
              </div>
            </div>

            {/* Record Cards with Conflicts column */}
            <div className="grid grid-cols-5 gap-8">
              {/* Card 1 - Fetching */}
              <motion.div
                className="flex h-[280px] flex-col rounded-xl border border-gray-200 bg-white p-6 transition-all hover:shadow-xl focus:outline-none focus-visible:ring-2 focus-visible:ring-cyan-500"
                tabIndex={0}
                role="group"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <div className="mb-4 flex items-start justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Apollo Hospital
                  </h3>
                  <span className="rounded-full bg-yellow-100 px-2.5 py-1 text-xs font-medium text-yellow-700">
                    Fetching
                  </span>
                </div>
                {/* Stage progress */}
                <div
                  className="mt-1 h-1.5 overflow-hidden rounded-full bg-gray-100"
                  aria-label="Progress across stages"
                  role="progressbar"
                  aria-valuenow={33}
                  aria-valuemin={0}
                  aria-valuemax={100}
                >
                  <div
                    className="h-full bg-gradient-to-r from-cyan-500 to-cyan-400"
                    style={{ width: "33%" }}
                  />
                </div>

                <div className="flex-1 space-y-3">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">Type:</span>
                    <span className="text-sm font-medium text-gray-700">
                      Discharge Summary
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">Date:</span>
                    <span className="text-sm font-medium text-gray-700">
                      Aug 15, 2025
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">Source:</span>
                    <div className="flex items-center gap-1">
                      <div className="flex h-5 w-5 items-center justify-center rounded bg-cyan-100">
                        <span className="text-[10px] font-bold text-cyan-600">
                          A
                        </span>
                      </div>
                      <span className="text-sm font-medium text-gray-700">
                        Apollo EMR
                      </span>
                    </div>
                  </div>
                </div>
                {/* Stage progress */}
                <div
                  className="mt-1 h-1.5 overflow-hidden rounded-full bg-gray-100"
                  aria-label="Progress across stages"
                  role="progressbar"
                  aria-valuenow={50}
                  aria-valuemin={0}
                  aria-valuemax={100}
                >
                  <div
                    className="h-full bg-gradient-to-r from-blue-500 to-blue-400"
                    style={{ width: "50%" }}
                  />
                </div>

                <div className="mt-auto flex items-center justify-between border-t border-gray-100 pt-4">
                  <span className="text-sm text-gray-500">
                    MRN: AP-2025-847
                  </span>
                  <div className="flex items-center gap-2">
                    <div className="hidden items-center gap-1 lg:flex">
                      <button
                        className="rounded-full border border-gray-200 px-2 py-1 text-[11px] text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                        onClick={() => {
                          try {
                            trackEvent &&
                              trackEvent("quick_action", {
                                action: "order_test",
                                stage: "fetching",
                              });
                          } catch {}
                        }}
                        aria-label="Order test"
                        title="Order test"
                      >
                        Order test
                      </button>
                      <button
                        className="rounded-full border border-gray-200 px-2 py-1 text-[11px] text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                        onClick={() => {
                          try {
                            trackEvent &&
                              trackEvent("quick_action", {
                                action: "request_record",
                                stage: "fetching",
                              });
                          } catch {}
                        }}
                        aria-label="Request record"
                        title="Request record"
                      >
                        Request record
                      </button>
                      <button
                        className="rounded-full border border-gray-200 px-2 py-1 text-[11px] text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                        onClick={() => {
                          try {
                            trackEvent &&
                              trackEvent("quick_action", {
                                action: "add_to_board",
                                stage: "fetching",
                              });
                          } catch {}
                        }}
                        aria-label="Add to board"
                        title="Add to board"
                      >
                        Add to board
                      </button>
                    </div>
                    <div className="h-5 w-5 animate-spin rounded-full border-2 border-yellow-400 border-t-transparent"></div>
                  </div>
                </div>
              </motion.div>

              {/* Card 3 - Conflicts */}
              <motion.div
                className="flex h-[280px] flex-col items-center justify-center rounded-xl border border-amber-200 bg-white p-6 text-amber-700/80 transition-all hover:shadow-xl focus:outline-none focus-visible:ring-2 focus-visible:ring-amber-400"
                tabIndex={0}
                role="group"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.55 }}
              >
                <div className="text-sm">No conflicts</div>
              </motion.div>

              {/* Card 2 - Processing */}
              <motion.div
                className="flex h-[280px] flex-col rounded-xl border border-gray-200 bg-white p-6 transition-all hover:shadow-xl focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500"
                tabIndex={0}
                role="group"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                <div className="mb-4 flex items-start justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">
                    SRL Diagnostics
                  </h3>
                  <span className="rounded-full bg-blue-100 px-2.5 py-1 text-xs font-medium text-blue-700">
                    Processing
                  </span>
                </div>
                {/* Stage progress */}
                <div
                  className="mt-1 h-1.5 overflow-hidden rounded-full bg-gray-100"
                  aria-label="Progress across stages"
                  role="progressbar"
                  aria-valuenow={83}
                  aria-valuemin={0}
                  aria-valuemax={100}
                >
                  <div
                    className="h-full bg-gradient-to-r from-green-500 to-green-400"
                    style={{ width: "83%" }}
                  />
                </div>

                <div className="flex-1 space-y-3">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">Type:</span>
                    <span className="text-sm font-medium text-gray-700">
                      Pathology Report
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">Date:</span>
                    <span className="text-sm font-medium text-gray-700">
                      Aug 20, 2025
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">Source:</span>
                    <div className="flex items-center gap-1">
                      <div className="flex h-5 w-5 items-center justify-center rounded bg-purple-100">
                        <span className="text-[10px] font-bold text-purple-600">
                          S
                        </span>
                      </div>
                      <span className="text-sm font-medium text-gray-700">
                        SRL LIS
                      </span>
                    </div>
                  </div>
                </div>
                {/* Stage progress */}
                <div
                  className="mt-1 h-1.5 overflow-hidden rounded-full bg-gray-100"
                  aria-label="Progress across stages"
                  role="progressbar"
                  aria-valuenow={100}
                  aria-valuemin={0}
                  aria-valuemax={100}
                >
                  <div className="h-full w-full bg-gradient-to-r from-emerald-500 to-emerald-400" />
                </div>

                <div className="mt-auto flex items-center justify-between border-t border-gray-100 pt-4">
                  <span className="text-sm text-gray-500">
                    <span className="rounded bg-cyan-100 px-2 py-1 text-xs font-medium text-cyan-700">
                      OCR
                    </span>
                  </span>
                  <div className="flex items-center gap-2">
                    <div className="hidden items-center gap-1 lg:flex">
                      <button
                        className="rounded-full border border-gray-200 px-2 py-1 text-[11px] text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                        onClick={() => {
                          try {
                            trackEvent &&
                              trackEvent("quick_action", {
                                action: "order_test",
                                stage: "processing",
                              });
                          } catch {}
                        }}
                        aria-label="Order test"
                        title="Order test"
                      >
                        Order test
                      </button>
                      <button
                        className="rounded-full border border-gray-200 px-2 py-1 text-[11px] text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                        onClick={() => {
                          try {
                            trackEvent &&
                              trackEvent("quick_action", {
                                action: "request_record",
                                stage: "processing",
                              });
                          } catch {}
                        }}
                        aria-label="Request record"
                        title="Request record"
                      >
                        Request record
                      </button>
                      <button
                        className="rounded-full border border-gray-200 px-2 py-1 text-[11px] text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                        onClick={() => {
                          try {
                            trackEvent &&
                              trackEvent("quick_action", {
                                action: "add_to_board",
                                stage: "processing",
                              });
                          } catch {}
                        }}
                        aria-label="Add to board"
                        title="Add to board"
                      >
                        Add to board
                      </button>
                    </div>
                    <span className="text-sm font-medium text-blue-600">
                      75%
                    </span>
                  </div>
                </div>
              </motion.div>

              {/* Card 3 - Linked */}
              <motion.div
                className="flex h-[280px] flex-col rounded-xl border border-green-200 bg-white p-6 transition-all hover:shadow-xl focus:outline-none focus-visible:ring-2 focus-visible:ring-green-500"
                tabIndex={0}
                role="group"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
              >
                <div className="mb-4 flex items-start justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Max Healthcare
                  </h3>
                  <span className="rounded-full bg-green-100 px-2.5 py-1 text-xs font-medium text-green-700">
                    Linked
                  </span>
                </div>

                <div className="flex-1 space-y-3">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">Type:</span>
                    <span className="text-sm font-medium text-gray-700">
                      CT Scan Report
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">Date:</span>
                    <span className="text-sm font-medium text-gray-700">
                      Aug 22, 2025
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">Source:</span>
                    <div className="flex items-center gap-1">
                      <div className="flex h-5 w-5 items-center justify-center rounded bg-orange-100">
                        <span className="text-[10px] font-bold text-orange-600">
                          M
                        </span>
                      </div>
                      <span className="text-sm font-medium text-gray-700">
                        Max PACS
                      </span>
                    </div>
                  </div>
                </div>

                <div className="mt-auto flex items-center justify-between border-t border-gray-100 pt-4">
                  <span className="text-sm text-gray-500">
                    ABDM ID: PS@abdm
                  </span>
                  <div className="flex items-center gap-2">
                    <div className="hidden items-center gap-1 lg:flex">
                      <button
                        className="rounded-full border border-gray-200 px-2 py-1 text-[11px] text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                        onClick={() => {
                          try {
                            trackEvent &&
                              trackEvent("quick_action", {
                                action: "order_test",
                                stage: "linked",
                              });
                          } catch {}
                        }}
                        aria-label="Order test"
                        title="Order test"
                      >
                        Order test
                      </button>
                      <button
                        className="rounded-full border border-gray-200 px-2 py-1 text-[11px] text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                        onClick={() => {
                          try {
                            trackEvent &&
                              trackEvent("quick_action", {
                                action: "request_record",
                                stage: "linked",
                              });
                          } catch {}
                        }}
                        aria-label="Request record"
                        title="Request record"
                      >
                        Request record
                      </button>
                      <button
                        className="rounded-full border border-gray-200 px-2 py-1 text-[11px] text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                        onClick={() => {
                          try {
                            trackEvent &&
                              trackEvent("quick_action", {
                                action: "add_to_board",
                                stage: "linked",
                              });
                          } catch {}
                        }}
                        aria-label="Add to board"
                        title="Add to board"
                      >
                        Add to board
                      </button>
                    </div>
                    <svg
                      className="h-5 w-5 text-green-500"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                </div>
              </motion.div>

              {/* Card 4 - Complete */}
              <motion.div
                className="flex h-[280px] flex-col rounded-xl border border-green-200 bg-white p-6 transition-all hover:shadow-xl focus:outline-none focus-visible:ring-2 focus-visible:ring-emerald-500"
                tabIndex={0}
                role="group"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 }}
              >
                <div className="mb-4 flex items-start justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Fortis Hospital
                  </h3>
                  <span className="rounded-full bg-green-100 px-2.5 py-1 text-xs font-medium text-green-700">
                    Complete
                  </span>
                </div>

                <div className="flex-1 space-y-3">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">Type:</span>
                    <span className="text-sm font-medium text-gray-700">
                      Oncology Consult
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">Date:</span>
                    <span className="text-sm font-medium text-gray-700">
                      Aug 24, 2025
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">Source:</span>
                    <div className="flex items-center gap-1">
                      <div className="flex h-5 w-5 items-center justify-center rounded bg-green-100">
                        <span className="text-[10px] font-bold text-green-600">
                          F
                        </span>
                      </div>
                      <span className="text-sm font-medium text-gray-700">
                        Fortis EMR
                      </span>
                    </div>
                  </div>
                </div>

                <div className="mt-auto flex items-center justify-between border-t border-gray-100 pt-4">
                  <span className="text-sm text-gray-500">Dr. R. Mehta</span>
                  <div className="flex items-center gap-2">
                    <div className="hidden items-center gap-1 lg:flex">
                      <button
                        className="rounded-full border border-gray-200 px-2 py-1 text-[11px] text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                        onClick={() => {
                          try {
                            trackEvent &&
                              trackEvent("quick_action", {
                                action: "order_test",
                                stage: "complete",
                              });
                          } catch {}
                        }}
                        aria-label="Order test"
                        title="Order test"
                      >
                        Order test
                      </button>
                      <button
                        className="rounded-full border border-gray-200 px-2 py-1 text-[11px] text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                        onClick={() => {
                          try {
                            trackEvent &&
                              trackEvent("quick_action", {
                                action: "request_record",
                                stage: "complete",
                              });
                          } catch {}
                        }}
                        aria-label="Request record"
                        title="Request record"
                      >
                        Request record
                      </button>
                      <button
                        className="rounded-full border border-gray-200 px-2 py-1 text-[11px] text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                        onClick={() => {
                          try {
                            trackEvent &&
                              trackEvent("quick_action", {
                                action: "add_to_board",
                                stage: "complete",
                              });
                          } catch {}
                        }}
                        aria-label="Add to board"
                        title="Add to board"
                      >
                        Add to board
                      </button>
                    </div>
                    <button className="text-sm font-medium text-cyan-600 hover:text-cyan-700">
                      View →
                    </button>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Pre-visit Readiness Mini-panel */}
            <div className="mt-6 rounded-xl border border-emerald-200 bg-emerald-50 p-4">
              <div className="flex items-start justify-between gap-4">
                <div className="flex items-center gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-emerald-100 text-emerald-700">
                    ✓
                  </div>
                  <div>
                    <p className="text-sm font-semibold text-emerald-900">
                      Pre-visit readiness
                    </p>
                    <p className="text-xs text-emerald-800/80">
                      2 gaps: recent imaging report, updated medication list
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    className="rounded-full border border-emerald-300 bg-white px-2.5 py-1 text-xs font-medium text-emerald-700 hover:bg-emerald-50 focus:outline-none focus:ring-2 focus:ring-emerald-500"
                    onClick={() => {
                      try {
                        trackEvent &&
                          trackEvent("readiness_action_click", {
                            action: "send_reminder",
                          });
                      } catch {}
                    }}
                  >
                    Send reminder
                  </button>
                  <button
                    className="rounded-full border border-emerald-300 bg-white px-2.5 py-1 text-xs font-medium text-emerald-700 hover:bg-emerald-50 focus:outline-none focus:ring-2 focus:ring-emerald-500"
                    onClick={() => {
                      try {
                        trackEvent &&
                          trackEvent("readiness_action_click", {
                            action: "view_details",
                          });
                      } catch {}
                    }}
                  >
                    View details
                  </button>
                </div>
              </div>
            </div>

            {/* Bottom Metrics */}
            <div className="mt-8 rounded-xl border border-cyan-200 bg-gradient-to-r from-cyan-50 to-cyan-100 p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-white shadow-sm">
                    <svg
                      className="h-6 w-6 text-cyan-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                      />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm font-semibold text-gray-900">
                      Record Gathering Time
                    </p>
                    <p className="text-xs text-gray-600">
                      Target: &lt;2 min per case (from 6-20 min baseline)
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="text-right">
                    <p className="text-3xl font-light text-cyan-600">1.8</p>
                    <p className="text-xs text-gray-600">minutes average</p>
                  </div>
                  <button
                    className="group relative inline-flex items-center gap-1 rounded-full border border-cyan-200 bg-white px-2.5 py-1 text-xs font-medium text-cyan-700 shadow-sm hover:bg-cyan-50 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                    aria-describedby="benchmark-tip"
                    onMouseEnter={() => {
                      try {
                        trackEvent &&
                          trackEvent("benchmark_tooltip_open", {
                            method: "hover",
                          });
                      } catch {}
                    }}
                    onFocus={() => {
                      try {
                        trackEvent &&
                          trackEvent("benchmark_tooltip_open", {
                            method: "focus",
                          });
                      } catch {}
                    }}
                  >
                    <span className="font-semibold">Benchmark</span>
                    <span className="text-[10px] text-cyan-600">2.8m</span>
                    <div
                      role="tooltip"
                      id="benchmark-tip"
                      className="pointer-events-none absolute bottom-full left-1/2 mb-2 w-64 -translate-x-1/2 rounded-md bg-slate-900 p-2 text-xs text-white opacity-0 transition-opacity duration-150 group-hover:opacity-100 group-focus:opacity-100"
                    >
                      <p className="leading-relaxed">
                        Typical clinics spend 6–20 min collecting records. Top
                        quartile achieves ~2.8 min. Entheory target:{" "}
                        <span className="font-semibold">&lt;2.0 min</span>.
                      </p>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

// Module 2 — ONE TIMELINE, ONE PATIENT (moved to its own file)

// Module 4 — Safer Actions & Follow‑ups
const SaferActionsView = () => {
  const prefersReduced = useReducedMotion();
  const { trackEvent } = useAnalytics();

  // Core state management
  const [viewMode, setViewMode] = React.useState<"summary" | "full">(() => {
    if (typeof window === "undefined") return "summary";
    return (
      (localStorage.getItem("safer-actions-view-mode") as "summary" | "full") ||
      "summary"
    );
  });

  // Outcome counters state
  const [safetyIssuesCaught, setSafetyIssuesCaught] = React.useState({
    today: 2,
    week: 8,
  });
  const [tasksScheduled, setTasksScheduled] = React.useState({ week: 4 });

  // Regimen and safety state
  const [regimenStatus, setRegimenStatus] = React.useState<
    "draft" | "accepted" | "edited"
  >("draft");
  const [showDrugExplainer, setShowDrugExplainer] = React.useState<
    string | null
  >(null);
  const [safetySignals, setSafetySignals] = React.useState({
    ddi: { active: true, severity: "moderate" as "low" | "moderate" | "high" },
    allergy: { active: true, severity: "low" as "low" | "moderate" | "high" },
    completeness: { active: true, missing: ["CBC", "LFT"] },
  });

  // Trial match state
  const [showTrialCriteria, setShowTrialCriteria] = React.useState(false);

  // Task rail state
  const [taskFilter, setTaskFilter] = React.useState<"mine" | "team" | "all">(
    "mine"
  );
  const [tasks, setTasks] = React.useState([
    {
      id: "1",
      title: "CBC (with diff)",
      due: "Tue",
      status: "scheduled" as const,
      assignee: "RN",
    },
    {
      id: "2",
      title: "Refill Tamoxifen",
      due: "30 days",
      status: "done" as const,
      assignee: "Pharmacy",
    },
    {
      id: "3",
      title: "Follow-up",
      due: "3 months",
      status: "overdue" as const,
      assignee: "MD",
    },
  ]);

  // Note composer state
  const [noteTemplate, setNoteTemplate] = React.useState<
    "assessment" | "regimen" | "toxicity"
  >("assessment");
  const [insertedOrders, setInsertedOrders] = React.useState<string[]>([
    "CBC with diff",
    "LFTs",
  ]);

  // Persist view mode preference
  React.useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("safer-actions-view-mode", viewMode);
    }
  }, [viewMode]);

  // Track component mount
  React.useEffect(() => {
    try {
      trackEvent && trackEvent("safer_actions_mount");
    } catch {}
  }, [trackEvent]);

  return (
    <motion.div
      initial={false}
      animate={{ opacity: 1 }}
      transition={{ duration: 0 }}
      className="flex h-full flex-col"
    >
      {/* Header area with title and description - matching exact pattern from other modules */}
      <motion.div
        className="relative px-8 pb-6 pt-24 md:px-16 md:pb-8 md:pt-32"
        initial={false}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0 }}
      >
        <div className="flex flex-col justify-center md:flex-row md:items-start md:gap-20">
          <h2 className="max-w-[18ch] text-center text-6xl font-light leading-[1.05] text-slate-900 md:text-left md:text-7xl lg:text-8xl">
            <span className="whitespace-nowrap">Safer Actions,</span>
            <br />
            <span className="whitespace-nowrap font-light italic">
              & Follow‑ups
            </span>
          </h2>
          <div className="mt-6 max-w-2xl md:mt-4">
            <p className="text-lg font-light text-white/90">
              Propose, check, and orchestrate—all from one screen.
            </p>
            <div
              role="list"
              aria-label="Capabilities"
              className="mt-3 flex flex-wrap gap-2"
            >
              {["DDI/allergy alerts", "Trial matches", "Status tracker"].map(
                (cap) => (
                  <span
                    key={cap}
                    role="listitem"
                    className="inline-flex items-center gap-1 rounded-full border border-white/60 bg-white/90 px-3 py-1 text-xs font-medium text-slate-800 backdrop-blur-md focus:outline-none focus-visible:ring-2 focus-visible:ring-white/80"
                    aria-label={cap}
                  >
                    {cap}
                  </span>
                )
              )}
            </div>
          </div>
        </div>

        {/* Pink liquid-glass tab - positioned exactly like other modules */}
        <div className="absolute left-6 top-1/2 hidden -translate-y-1/2 md:left-12 md:block">
          <div className="inline-flex items-center gap-2 rounded-[26px] border border-white/70 bg-white/90 px-7 py-3.5 shadow-[0_10px_28px_rgba(0,0,0,0.12)] backdrop-blur-2xl">
            <div
              className="h-2 w-2 rounded-full"
              style={{ backgroundColor: "#ec4899" }}
            />
            <div className="flex items-baseline gap-1">
              <span className="text-[12px] font-bold tracking-wider text-slate-800">
                SAFER ACTIONS
              </span>
              <span className="text-[11px] font-medium tracking-wide text-slate-600">
                & FOLLOW‑UPS
              </span>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main app container - matching TimelineView structure */}
      <motion.div
        className="relative min-h-0 w-full flex-1 overflow-hidden rounded-t-2xl bg-white/95 shadow-xl ring-1 ring-slate-200/60 backdrop-blur-sm"
        initial={false}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0 }}
      >
        {/* App Header with navigation - following exact pattern */}
        <div className="border-b border-gray-200 bg-gray-50 px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-2">
                <img src="/logo.png" alt="Entheory" className="h-6 w-auto" />
              </div>
              <nav className="flex items-center gap-6">
                <button
                  className="text-sm text-gray-600 hover:text-gray-900 focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500"
                  onClick={() => {
                    try {
                      trackEvent && trackEvent("nav_click", { tab: "records" });
                    } catch {}
                  }}
                >
                  Records
                </button>
                <button
                  className="text-sm text-gray-600 hover:text-gray-900 focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500"
                  onClick={() => {
                    try {
                      trackEvent &&
                        trackEvent("nav_click", { tab: "timeline" });
                    } catch {}
                  }}
                >
                  Timeline
                </button>
                <button
                  className="text-sm text-gray-600 hover:text-gray-900 focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500"
                  onClick={() => {
                    try {
                      trackEvent && trackEvent("nav_click", { tab: "summary" });
                    } catch {}
                  }}
                >
                  Summary
                </button>
                <button
                  className="text-sm font-medium text-pink-600 focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500"
                  aria-current="page"
                >
                  Actions
                </button>
              </nav>
            </div>

            {/* Outcome counters in header right */}
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-4 text-xs">
                <div className="flex items-center gap-2">
                  <div className="flex h-5 w-5 items-center justify-center rounded-full bg-pink-100">
                    <svg
                      className="h-3 w-3 text-pink-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      strokeWidth={2}
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <span className="text-gray-700">
                    Safety issues caught:{" "}
                    <span className="font-semibold text-pink-600">
                      {safetyIssuesCaught.today}
                    </span>{" "}
                    (today)
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="flex h-5 w-5 items-center justify-center rounded-full bg-emerald-100">
                    <svg
                      className="h-3 w-3 text-emerald-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      strokeWidth={2}
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                  <span className="text-gray-700">
                    Auto‑scheduled:{" "}
                    <span className="font-semibold text-emerald-600">
                      {tasksScheduled.week}
                    </span>{" "}
                    (this week)
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Sub Navigation with patient context */}
        <div className="border-b border-gray-200 bg-white px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-8">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-500">Patient:</span>
                <span className="text-sm font-medium text-gray-900">
                  Priya Sharma
                </span>
                <span className="rounded bg-pink-100 px-2 py-0.5 text-xs font-medium text-pink-700">
                  Active Plan
                </span>
                <span className="text-xs text-gray-500">MRN: PS-2025-847</span>
              </div>
              <div className="flex items-center gap-4">
                <button
                  className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900 focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500"
                  onClick={() => {
                    try {
                      trackEvent &&
                        trackEvent("filter_click", { filter: "all_actions" });
                    } catch {}
                  }}
                >
                  <svg
                    className="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                    />
                  </svg>
                  All Actions
                </button>
                <button
                  className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900 focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500"
                  onClick={() => {
                    try {
                      trackEvent &&
                        trackEvent("filter_click", { filter: "safety_checks" });
                    } catch {}
                  }}
                >
                  <svg
                    className="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                    />
                  </svg>
                  Safety Checks
                </button>

                {/* View mode toggle - matching other modules */}
                <div
                  className="ml-2 inline-flex overflow-hidden rounded-full border border-gray-300"
                  role="group"
                  aria-label="View mode"
                >
                  <button
                    className={`px-3 py-1 text-xs font-medium ${viewMode === "summary" ? "bg-gray-100 text-gray-900" : "bg-white text-gray-600"} focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500`}
                    onClick={() => {
                      setViewMode("summary");
                      try {
                        trackEvent &&
                          trackEvent("view_mode_change", { mode: "summary" });
                      } catch {}
                    }}
                    aria-pressed={viewMode === "summary"}
                  >
                    Summary
                  </button>
                  <button
                    className={`px-3 py-1 text-xs font-medium ${viewMode === "full" ? "bg-gray-100 text-gray-900" : "bg-white text-gray-600"} focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500`}
                    onClick={() => {
                      setViewMode("full");
                      try {
                        trackEvent &&
                          trackEvent("view_mode_change", { mode: "full" });
                      } catch {}
                    }}
                    aria-pressed={viewMode === "full"}
                  >
                    Full
                  </button>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="hidden items-center gap-2 text-xs text-gray-600 md:flex">
                <span className="rounded bg-gray-100 px-2 py-0.5">
                  Protocol v2.1
                </span>
                <button
                  className="underline hover:text-gray-900 focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500"
                  onClick={() => {
                    try {
                      trackEvent && trackEvent("safety_log_view_click");
                    } catch {}
                  }}
                >
                  View safety log
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area - Two column layout */}
        <div className="flex-1 overflow-hidden bg-gray-50 px-6 py-6">
          <div className="flex h-full max-h-[calc(100vh-250px)] gap-6">
            {/* Left Column (2/3 width) - Decisions & Safety */}
            <div className="scrollbar-thin scrollbar-thumb-slate-300 scrollbar-track-slate-100 w-2/3 space-y-4 overflow-y-auto pr-2">
              {/* Regimen Proposal Card */}
              <motion.div
                className="rounded-xl bg-white shadow-sm ring-1 ring-slate-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                {/* Card Header */}
                <div className="border-b border-slate-200 px-6 py-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-slate-900">
                      Suggested regimen
                    </h3>
                    <span
                      className={`rounded-full px-2.5 py-1 text-xs font-medium ${
                        regimenStatus === "accepted"
                          ? "bg-emerald-100 text-emerald-700"
                          : regimenStatus === "edited"
                            ? "bg-amber-100 text-amber-700"
                            : "bg-gray-100 text-gray-700"
                      }`}
                    >
                      {regimenStatus === "accepted"
                        ? "Accepted"
                        : regimenStatus === "edited"
                          ? "Modified"
                          : "Draft"}
                    </span>
                  </div>
                </div>

                {/* Regimen Content */}
                <div className="px-6 py-4">
                  <div
                    className={`rounded-lg border p-4 ${
                      viewMode === "full"
                        ? "border-slate-200 bg-slate-50"
                        : "border-transparent"
                    }`}
                  >
                    <p className="text-base leading-relaxed text-slate-900">
                      <span className="font-medium">
                        Endocrine therapy + CDK4/6 inhibitor:
                      </span>
                    </p>
                    <div className="mt-3 space-y-2">
                      {/* Drug 1: Palbociclib */}
                      <div className="flex items-start gap-3">
                        <div className="mt-1 h-1.5 w-1.5 shrink-0 rounded-full bg-pink-400" />
                        <div className="flex-1">
                          <span className="text-base text-slate-800">
                            Palbociclib 125 mg PO daily, days 1–21 of 28-day
                            cycle
                          </span>
                          <button
                            className="ml-2 inline-flex h-5 w-5 items-center justify-center rounded-full transition-colors hover:bg-slate-200"
                            onClick={() => {
                              setShowDrugExplainer(
                                showDrugExplainer === "palbociclib"
                                  ? null
                                  : "palbociclib"
                              );
                              try {
                                trackEvent &&
                                  trackEvent("drug_explainer_click", {
                                    drug: "palbociclib",
                                  });
                              } catch {}
                            }}
                            aria-label="Why palbociclib?"
                            aria-expanded={showDrugExplainer === "palbociclib"}
                          >
                            <span className="text-sm text-slate-500">⋯</span>
                          </button>

                          {/* Explainer dropdown */}
                          <AnimatePresence>
                            {showDrugExplainer === "palbociclib" && (
                              <motion.div
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: "auto" }}
                                exit={{ opacity: 0, height: 0 }}
                                transition={{ duration: 0.2 }}
                                className="mt-2 overflow-hidden"
                              >
                                <div className="rounded-md border border-pink-200 bg-pink-50 p-3">
                                  <p className="mb-1 text-xs font-medium text-pink-900">
                                    Why recommended:
                                  </p>
                                  <p className="text-xs text-pink-800">
                                    CDK4/6 inhibitor indicated for ER+/PR+,
                                    HER2– breast cancer. NCCN/ESMO class IIa
                                    evidence in this context.
                                  </p>
                                  <div className="mt-2 flex items-center gap-2">
                                    <span className="text-[11px] text-pink-700">
                                      Citations:
                                    </span>
                                    <button className="rounded-full border border-pink-300 bg-white px-2 py-0.5 text-[11px] text-pink-700 hover:bg-pink-100">
                                      MONARCH-3
                                    </button>
                                    <button className="rounded-full border border-pink-300 bg-white px-2 py-0.5 text-[11px] text-pink-700 hover:bg-pink-100">
                                      PALOMA-2
                                    </button>
                                  </div>
                                </div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </div>
                      </div>

                      {/* Drug 2: Letrozole */}
                      <div className="flex items-start gap-3">
                        <div className="mt-1 h-1.5 w-1.5 shrink-0 rounded-full bg-pink-400" />
                        <div className="flex-1">
                          <span className="text-base text-slate-800">
                            Letrozole 2.5 mg PO daily
                          </span>
                          <button
                            className="ml-2 inline-flex h-5 w-5 items-center justify-center rounded-full transition-colors hover:bg-slate-200"
                            onClick={() => {
                              setShowDrugExplainer(
                                showDrugExplainer === "letrozole"
                                  ? null
                                  : "letrozole"
                              );
                              try {
                                trackEvent &&
                                  trackEvent("drug_explainer_click", {
                                    drug: "letrozole",
                                  });
                              } catch {}
                            }}
                            aria-label="Why letrozole?"
                            aria-expanded={showDrugExplainer === "letrozole"}
                          >
                            <span className="text-sm text-slate-500">⋯</span>
                          </button>

                          {/* Explainer dropdown */}
                          <AnimatePresence>
                            {showDrugExplainer === "letrozole" && (
                              <motion.div
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: "auto" }}
                                exit={{ opacity: 0, height: 0 }}
                                transition={{ duration: 0.2 }}
                                className="mt-2 overflow-hidden"
                              >
                                <div className="rounded-md border border-pink-200 bg-pink-50 p-3">
                                  <p className="mb-1 text-xs font-medium text-pink-900">
                                    Why recommended:
                                  </p>
                                  <p className="text-xs text-pink-800">
                                    Aromatase inhibitor for postmenopausal ER+
                                    breast cancer. Prior response to endocrine
                                    therapy noted.
                                  </p>
                                  <div className="mt-2 flex items-center gap-2">
                                    <span className="text-[11px] text-pink-700">
                                      Citations:
                                    </span>
                                    <button className="rounded-full border border-pink-300 bg-white px-2 py-0.5 text-[11px] text-pink-700 hover:bg-pink-100">
                                      BIG 1-98
                                    </button>
                                  </div>
                                </div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="mt-4 flex items-center gap-2">
                    <button
                      className={`rounded-lg px-4 py-2 text-sm font-medium transition-all ${
                        safetySignals.ddi.active || safetySignals.allergy.active
                          ? "cursor-not-allowed bg-gray-100 text-gray-400"
                          : "bg-pink-600 text-white hover:bg-pink-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500"
                      }`}
                      disabled={
                        safetySignals.ddi.active || safetySignals.allergy.active
                      }
                      onClick={() => {
                        if (
                          !safetySignals.ddi.active &&
                          !safetySignals.allergy.active
                        ) {
                          setRegimenStatus("accepted");
                          try {
                            trackEvent && trackEvent("regimen_accepted");
                          } catch {}
                        }
                      }}
                    >
                      Accept
                    </button>
                    <button
                      className="rounded-lg border border-slate-300 px-4 py-2 text-sm font-medium text-slate-700 hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500"
                      onClick={() => {
                        setRegimenStatus("edited");
                        try {
                          trackEvent && trackEvent("regimen_edited");
                        } catch {}
                      }}
                    >
                      Edit
                    </button>
                    <button
                      className="rounded-lg border border-slate-300 px-4 py-2 text-sm font-medium text-slate-700 hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500"
                      onClick={() => {
                        try {
                          trackEvent && trackEvent("regimen_deferred");
                        } catch {}
                      }}
                    >
                      Defer
                    </button>
                  </div>

                  {/* Trust Row */}
                  <div className="mt-4 border-t border-slate-200 pt-4">
                    <p className="text-xs text-slate-600">
                      <span className="font-medium">Derived from:</span>{" "}
                      <button
                        className="underline hover:text-slate-900"
                        onClick={() => {
                          try {
                            trackEvent &&
                              trackEvent("trust_source_click", {
                                source: "biomarkers",
                              });
                          } catch {}
                        }}
                      >
                        ER+/PR+, HER2– (Obs: 2025-08-20)
                      </button>
                      {", "}
                      <button
                        className="underline hover:text-slate-900"
                        onClick={() => {
                          try {
                            trackEvent &&
                              trackEvent("trust_source_click", {
                                source: "prior_therapy",
                              });
                          } catch {}
                        }}
                      >
                        prior 1L AC+T (RX: 2025-04-02)
                      </button>
                    </p>
                  </div>
                </div>
              </motion.div>

              {/* Safety Signals Panel */}
              <motion.div
                className="rounded-xl bg-white p-6 shadow-sm ring-1 ring-slate-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.35 }}
              >
                <h3 className="mb-4 text-base font-semibold text-slate-900">
                  Safety Signals
                </h3>

                <div className="space-y-3">
                  {/* DDI Warning */}
                  <motion.div
                    className={`flex items-start gap-3 rounded-lg border p-3 transition-all ${
                      safetySignals.ddi.active
                        ? "border-amber-300 bg-amber-50 hover:bg-amber-100"
                        : "border-gray-200 bg-gray-50 opacity-60"
                    }`}
                    animate={
                      safetySignals.ddi.active && !prefersReduced
                        ? { scale: [1, 1.02, 1] }
                        : {}
                    }
                    transition={{ duration: 0.3 }}
                  >
                    <div className="mt-0.5">
                      <svg
                        className="h-5 w-5 text-amber-600"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                        />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="rounded-full bg-amber-200 px-2 py-0.5 text-xs font-medium text-amber-800">
                          DDI
                        </span>
                        <span className="text-sm font-medium text-amber-900">
                          Warfarin interaction
                        </span>
                        <span className="text-xs text-amber-700">
                          (↑INR risk)
                        </span>
                      </div>
                      <p className="mt-1 text-xs text-amber-800">
                        CDK4/6 inhibitor may increase INR via CYP3A4 interaction
                      </p>
                      {viewMode === "full" && (
                        <div className="mt-2 flex items-center gap-2">
                          <button
                            className="rounded-md border border-amber-300 bg-white px-2 py-1 text-xs text-amber-700 hover:bg-amber-50"
                            onClick={() => {
                              try {
                                trackEvent &&
                                  trackEvent("ddi_mitigation_click", {
                                    action: "switch_doac",
                                  });
                              } catch {}
                            }}
                          >
                            Switch to DOAC
                          </button>
                          <button
                            className="rounded-md border border-amber-300 bg-white px-2 py-1 text-xs text-amber-700 hover:bg-amber-50"
                            onClick={() => {
                              try {
                                trackEvent &&
                                  trackEvent("ddi_mitigation_click", {
                                    action: "increase_monitoring",
                                  });
                              } catch {}
                            }}
                          >
                            ↑ INR monitoring
                          </button>
                        </div>
                      )}
                    </div>
                    <button
                      className="text-xs text-amber-700 hover:text-amber-900"
                      onClick={() => {
                        try {
                          trackEvent &&
                            trackEvent("safety_signal_details_click", {
                              type: "ddi",
                            });
                        } catch {}
                      }}
                    >
                      Details →
                    </button>
                  </motion.div>

                  {/* Allergy Warning */}
                  <motion.div
                    className={`flex items-start gap-3 rounded-lg border p-3 transition-all ${
                      safetySignals.allergy.active
                        ? "border-red-300 bg-red-50 hover:bg-red-100"
                        : "border-gray-200 bg-gray-50 opacity-60"
                    }`}
                    animate={
                      safetySignals.allergy.active && !prefersReduced
                        ? { scale: [1, 1.02, 1] }
                        : {}
                    }
                    transition={{ duration: 0.3, delay: 0.1 }}
                  >
                    <div className="mt-0.5">
                      <svg
                        className="h-5 w-5 text-red-600"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="rounded-full bg-red-200 px-2 py-0.5 text-xs font-medium text-red-800">
                          Allergy
                        </span>
                        <span className="text-sm font-medium text-red-900">
                          Taxane allergy on file
                        </span>
                      </div>
                      <p className="mt-1 text-xs text-red-800">
                        Historical rash (grade 2) on docetaxel 2023-06-10
                      </p>
                      {viewMode === "full" && (
                        <div className="mt-2 text-xs text-red-700">
                          Note: Current regimen does not contain taxanes
                        </div>
                      )}
                    </div>
                    <button
                      className="text-xs text-red-700 hover:text-red-900"
                      onClick={() => {
                        try {
                          trackEvent &&
                            trackEvent("safety_signal_details_click", {
                              type: "allergy",
                            });
                        } catch {}
                      }}
                    >
                      Details →
                    </button>
                  </motion.div>

                  {/* Completeness Check */}
                  <motion.div
                    className={`flex items-start gap-3 rounded-lg border p-3 transition-all ${
                      safetySignals.completeness.active
                        ? "border-amber-300 bg-amber-50 hover:bg-amber-100"
                        : "border-gray-200 bg-gray-50 opacity-60"
                    }`}
                    animate={
                      safetySignals.completeness.active && !prefersReduced
                        ? { scale: [1, 1.02, 1] }
                        : {}
                    }
                    transition={{ duration: 0.3, delay: 0.2 }}
                  >
                    <div className="mt-0.5">
                      <svg
                        className="h-5 w-5 text-amber-600"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="rounded-full bg-amber-200 px-2 py-0.5 text-xs font-medium text-amber-800">
                          Completeness
                        </span>
                        <span className="text-sm font-medium text-amber-900">
                          Baseline labs needed
                        </span>
                      </div>
                      <p className="mt-1 text-xs text-amber-800">
                        {safetySignals.completeness.missing.join(", ")} required
                        within 7 days
                      </p>
                      <div className="mt-2 flex items-center gap-2">
                        {safetySignals.completeness.missing.map((lab) => (
                          <button
                            key={lab}
                            className="rounded-md border border-amber-300 bg-white px-2 py-1 text-xs text-amber-700 hover:bg-amber-50"
                            onClick={() => {
                              setSafetySignals((prev) => ({
                                ...prev,
                                completeness: {
                                  ...prev.completeness,
                                  missing: prev.completeness.missing.filter(
                                    (m) => m !== lab
                                  ),
                                },
                              }));
                              try {
                                trackEvent &&
                                  trackEvent("lab_order_click", { lab });
                              } catch {}
                            }}
                          >
                            Order {lab}
                          </button>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                </div>

                {/* Safety Summary */}
                {viewMode === "full" && (
                  <div className="mt-4 border-t border-slate-200 pt-4">
                    <p className="text-xs text-slate-600">
                      <span className="font-medium">Safety check engine:</span>{" "}
                      v2.1.0 • Last updated 2 min ago •{" "}
                      <button className="underline hover:text-slate-900">
                        View full log
                      </button>
                    </p>
                  </div>
                )}
              </motion.div>

              {/* Trial Match Card */}
              <motion.div
                className="rounded-xl bg-white shadow-sm ring-1 ring-slate-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <div className="border-b border-slate-200 px-6 py-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-slate-900">
                      Clinical Trial Match
                    </h3>
                    <span className="rounded-full bg-purple-100 px-2.5 py-1 text-xs font-medium text-purple-700">
                      Phase II • ER+ cohort
                    </span>
                  </div>
                </div>
                <div className="px-6 py-4">
                  <div className="mb-3 flex items-center justify-between">
                    <div>
                      <h4 className="text-base font-medium text-slate-900">
                        DESTINY-Breast04 Extension
                      </h4>
                      <p className="mt-1 text-sm text-slate-600">
                        T-DXd in HER2-low metastatic breast cancer
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-slate-500">Match score</p>
                      <p className="text-lg font-semibold text-purple-600">
                        87%
                      </p>
                    </div>
                  </div>

                  <div className="mb-4 rounded-lg border border-purple-200 bg-purple-50 p-3">
                    <div className="mb-2 flex items-center gap-2">
                      <svg
                        className="h-4 w-4 text-purple-600"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                      <span className="text-xs font-medium text-purple-900">
                        Key eligibility met
                      </span>
                    </div>
                    <ul className="space-y-1 text-xs text-purple-800">
                      <li className="flex items-center gap-1">
                        <span className="text-emerald-600">✓</span> ER+/PR+
                        confirmed
                      </li>
                      <li className="flex items-center gap-1">
                        <span className="text-emerald-600">✓</span> Prior
                        endocrine therapy
                      </li>
                      <li className="flex items-center gap-1">
                        <span className="text-amber-600">⚠</span> Genomic assay
                        pending
                      </li>
                    </ul>
                  </div>

                  <div className="flex items-center gap-2">
                    <button
                      className="rounded-lg bg-purple-600 px-4 py-2 text-sm font-medium text-white hover:bg-purple-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-purple-500"
                      onClick={() => {
                        setShowTrialCriteria(true);
                        try {
                          trackEvent && trackEvent("trial_criteria_view");
                        } catch {}
                      }}
                    >
                      View criteria
                    </button>
                    <button
                      className="rounded-lg border border-slate-300 px-4 py-2 text-sm font-medium text-slate-700 hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-purple-500"
                      onClick={() => {
                        try {
                          trackEvent && trackEvent("trial_share_patient");
                        } catch {}
                      }}
                    >
                      Share to patient
                    </button>
                  </div>

                  {viewMode === "full" && (
                    <div className="mt-4 border-t border-slate-200 pt-4">
                      <p className="text-xs text-slate-600">
                        <span className="font-medium">Location:</span> 12 miles
                        • Memorial Sloan Kettering
                      </p>
                      <p className="mt-1 text-xs text-slate-600">
                        <span className="font-medium">PI:</span> Dr. Sarah Chen
                        • NCT04539938
                      </p>
                    </div>
                  )}
                </div>
              </motion.div>
            </div>

            {/* Right Column (1/3 width) - Orchestration & Documentation */}
            <div className="scrollbar-thin scrollbar-thumb-slate-300 scrollbar-track-slate-100 w-1/3 space-y-4 overflow-y-auto border-l border-slate-200 pl-6">
              {/* Task Rail */}
              <motion.div
                className="rounded-lg bg-white ring-1 ring-slate-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.45 }}
              >
                <div className="border-b border-slate-200 px-4 py-3">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-semibold text-slate-900">
                      Tasks
                    </h3>
                    <button
                      className="text-xs font-medium text-pink-600 hover:text-pink-700"
                      onClick={() => {
                        try {
                          trackEvent && trackEvent("add_task_click");
                        } catch {}
                      }}
                    >
                      + Add task
                    </button>
                  </div>
                  <div
                    className="mt-2 inline-flex overflow-hidden rounded-full border border-gray-300"
                    role="group"
                  >
                    {(["mine", "team", "all"] as const).map((filter) => (
                      <button
                        key={filter}
                        className={`px-2 py-0.5 text-[11px] font-medium ${
                          taskFilter === filter
                            ? "bg-gray-100 text-gray-900"
                            : "bg-white text-gray-600"
                        }`}
                        onClick={() => {
                          setTaskFilter(filter);
                          try {
                            trackEvent &&
                              trackEvent("task_filter_change", { filter });
                          } catch {}
                        }}
                      >
                        {filter.charAt(0).toUpperCase() + filter.slice(1)}
                      </button>
                    ))}
                  </div>
                </div>

                <div className="divide-y divide-slate-200">
                  {tasks.map((task) => (
                    <div
                      key={task.id}
                      className="flex items-start gap-3 px-4 py-3"
                    >
                      <div className="mt-1">
                        <div
                          className={`h-2 w-2 rounded-full ${
                            task.status === "done"
                              ? "bg-emerald-500"
                              : task.status === "overdue"
                                ? "bg-red-500"
                                : "bg-amber-500"
                          }`}
                        />
                      </div>
                      <div className="min-w-0 flex-1">
                        <p
                          className={`text-sm text-slate-900 ${
                            task.status === "done"
                              ? "text-slate-500 line-through"
                              : ""
                          }`}
                        >
                          {task.title}
                        </p>
                        <p className="mt-0.5 text-xs text-slate-500">
                          {task.status === "overdue" ? (
                            <span className="font-medium text-red-600">
                              Overdue
                            </span>
                          ) : (
                            <>Due {task.due}</>
                          )}{" "}
                          • {task.assignee}
                        </p>
                      </div>
                      <button
                        className="rounded-md border border-slate-300 px-2 py-1 text-[11px] hover:bg-slate-50"
                        onClick={() => {
                          if (task.status !== "done") {
                            setTasks((prev) =>
                              prev.map((t) =>
                                t.id === task.id
                                  ? { ...t, status: "done" as const }
                                  : t
                              )
                            );
                            try {
                              trackEvent &&
                                trackEvent("task_complete", { id: task.id });
                            } catch {}
                          }
                        }}
                        disabled={task.status === "done"}
                      >
                        {task.status === "done" ? "Done" : "Complete"}
                      </button>
                    </div>
                  ))}
                </div>

                <div className="border-t border-slate-200 px-4 py-3">
                  <div className="flex items-center gap-2">
                    <button className="rounded-md border border-slate-300 px-2 py-1 text-xs hover:bg-slate-50">
                      Bulk complete
                    </button>
                    <button className="rounded-md border border-slate-300 px-2 py-1 text-xs hover:bg-slate-50">
                      Reschedule
                    </button>
                    <button className="rounded-md border border-slate-300 px-2 py-1 text-xs hover:bg-slate-50">
                      Assign
                    </button>
                  </div>
                </div>
              </motion.div>

              {/* Note Composer with Citations */}
              <motion.div
                className="rounded-lg bg-white ring-1 ring-slate-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                <div className="border-b border-slate-200 px-4 py-3">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-semibold text-slate-900">
                      Note Composer
                    </h3>
                    <select
                      className="rounded-md border border-slate-300 px-2 py-1 text-xs focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500"
                      onChange={(e) => {
                        try {
                          trackEvent &&
                            trackEvent("note_template_change", {
                              template: e.target.value,
                            });
                        } catch {}
                      }}
                    >
                      <option value="assessment">Assessment & Plan</option>
                      <option value="regimen">Regimen change</option>
                      <option value="toxicity">Toxicity visit</option>
                    </select>
                  </div>
                </div>

                <div className="p-4">
                  {/* Pre-filled note with citations */}
                  <div className="mb-3 rounded-md border border-slate-200 bg-slate-50 p-3">
                    <p className="text-sm leading-relaxed text-slate-900">
                      <span className="font-medium">Assessment:</span> Patient
                      with metastatic breast cancer,{" "}
                      <button
                        className="text-pink-600 underline hover:text-pink-700"
                        onClick={() => {
                          try {
                            trackEvent &&
                              trackEvent("citation_click", {
                                ref: "ER_status",
                              });
                          } catch {}
                        }}
                      >
                        ER+/PR+, HER2– (Obs#A16)
                      </button>
                      , presenting for treatment planning.
                    </p>
                    <p className="mt-2 text-sm leading-relaxed text-slate-900">
                      <span className="font-medium">Plan:</span> Initiate
                      endocrine therapy + CDK4/6 inhibitor per protocol.{" "}
                      <button
                        className="text-pink-600 underline hover:text-pink-700"
                        onClick={() => {
                          try {
                            trackEvent &&
                              trackEvent("citation_click", {
                                ref: "prior_response",
                              });
                          } catch {}
                        }}
                      >
                        Prior response to AI noted (RX#L02)
                      </button>
                      . Monitor CBC q2w × 2 cycles, then monthly.
                    </p>
                  </div>

                  {/* Insert orders section */}
                  <div className="mb-3">
                    <p className="mb-2 text-xs font-medium text-slate-700">
                      Orders added to note:
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <span className="inline-flex items-center gap-1 rounded-full border border-emerald-300 bg-emerald-50 px-2 py-1 text-xs text-emerald-700">
                        <svg
                          className="h-3 w-3"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        CBC with diff
                      </span>
                      <span className="inline-flex items-center gap-1 rounded-full border border-emerald-300 bg-emerald-50 px-2 py-1 text-xs text-emerald-700">
                        <svg
                          className="h-3 w-3"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        LFTs
                      </span>
                    </div>
                  </div>

                  {/* Export actions */}
                  <div className="flex items-center gap-2">
                    <button
                      className="flex-1 rounded-md bg-pink-600 px-3 py-1.5 text-xs font-medium text-white hover:bg-pink-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500"
                      onClick={() => {
                        try {
                          trackEvent &&
                            trackEvent("note_export", { type: "emr" });
                        } catch {}
                      }}
                    >
                      Copy to EMR
                    </button>
                    <button
                      className="flex-1 rounded-md border border-slate-300 px-3 py-1.5 text-xs font-medium text-slate-700 hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500"
                      onClick={() => {
                        try {
                          trackEvent &&
                            trackEvent("note_export", { type: "fhir" });
                        } catch {}
                      }}
                    >
                      Save as FHIR
                    </button>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

// Module 3 — Board‑Ready Summary (stabilized lite view)
const BoardSummaryLite = () => {
  const prefersReduced = useReducedMotion();
  const { trackEvent } = useAnalytics();
  React.useEffect(() => {
    try {
      trackEvent && trackEvent("board_summary_lite_mount");
    } catch {}
  }, [trackEvent]);
  return (
    <motion.div
      initial={false}
      animate={{ opacity: 1 }}
      transition={{ duration: 0 }}
      className="flex h-full flex-col"
    >
      <div className="relative px-8 pb-6 pt-24 md:px-16 md:pb-8 md:pt-32">
        <div className="flex flex-col justify-center md:flex-row md:items-start md:gap-20">
          <h2 className="max-w-[18ch] text-center text-6xl font-light leading-[1.05] text-teal-800 md:text-left md:text-7xl lg:text-8xl">
            Board‑Ready,
            <br />
            <span className="font-light italic">auditable in seconds</span>
          </h2>
          <div className="mt-6 max-w-2xl md:mt-4">
            <p className="text-lg font-light text-slate-700">
              Auditable draft for tumor boards—stage, biomarkers, lines &
              performance status with citations.
            </p>
            <div className="mt-3 flex flex-wrap gap-2">
              {["Board export", "Gaps highlighted", "Human‑in‑loop"].map(
                (c) => (
                  <span
                    key={c}
                    className="inline-flex items-center gap-1 rounded-full border border-white/60 bg-white/90 px-3 py-1 text-xs font-medium text-slate-800 backdrop-blur-md"
                  >
                    {c}
                  </span>
                )
              )}
            </div>
          </div>
        </div>
        <div className="absolute left-6 top-1/2 hidden -translate-y-1/2 md:left-12 md:block">
          <div className="inline-flex items-center gap-2 rounded-[26px] border border-white/70 bg-white/90 px-7 py-3.5 shadow-[0_10px_28px_rgba(0,0,0,0.12)] backdrop-blur-2xl">
            <div
              className="h-2 w-2 rounded-full"
              style={{ backgroundColor: "#fef9c3" }}
            />
            <div className="flex items-baseline gap-1">
              <span className="text-[12px] font-bold tracking-wider text-slate-800">
                BOARD‑READY
              </span>
              <span className="text-[11px] font-medium tracking-wide text-slate-600">
                SUMMARY
              </span>
            </div>
          </div>
        </div>
      </div>
      <div className="relative h-[65vh] w-full overflow-hidden rounded-t-2xl bg-white shadow-2xl md:h-[70vh] lg:h-[74vh]">
        <div className="h-full p-6">
          <div className="h-full rounded-xl bg-white p-4 shadow-sm ring-1 ring-slate-200">
            <div className="flex items-center justify-between border-b border-slate-200 pb-3">
              <div className="text-sm font-semibold text-slate-900">
                Draft for discussion
              </div>
              <div
                className="inline-flex overflow-hidden rounded-full border border-gray-300"
                role="group"
                aria-label="View mode"
              >
                <button className="bg-gray-100 px-3 py-1 text-xs text-gray-900">
                  Summary
                </button>
                <button className="bg-white px-3 py-1 text-xs text-gray-600">
                  Full
                </button>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 pt-4">
              <div className="space-y-3">
                <div className="text-xs font-semibold uppercase tracking-wider text-slate-700">
                  Diagnosis & Stage
                </div>
                <div className="flex flex-wrap gap-2 text-xs">
                  {[
                    "Primary site: Breast",
                    "TNM: T2N0M0",
                    "AJCC 8th",
                    "Staged 24 Aug 2025",
                  ].map((p) => (
                    <span
                      key={p}
                      className="rounded-full border border-slate-300 px-2 py-1 text-slate-700"
                    >
                      {p}
                    </span>
                  ))}
                </div>
                <div className="pt-2 text-xs font-semibold uppercase tracking-wider text-slate-700">
                  Biomarkers
                </div>
                <div className="flex flex-wrap gap-2 text-xs">
                  {["ER 90% (IHC)", "PR 80% (IHC)", "HER2 0 (IHC)"].map((p) => (
                    <span
                      key={p}
                      className="rounded-full border border-slate-300 px-2 py-1 text-slate-700"
                    >
                      {p}
                    </span>
                  ))}
                </div>
              </div>
              <div className="space-y-3">
                <div className="text-xs font-semibold uppercase tracking-wider text-slate-700">
                  Performance Status
                </div>
                <div className="text-sm text-slate-900">
                  ECOG: 1 · 24 Aug 2025
                </div>
                <div className="pt-2 text-xs font-semibold uppercase tracking-wider text-slate-700">
                  Imaging
                </div>
                <div className="text-sm text-slate-900">
                  CT Chest 22 Aug — No new lesions
                </div>
              </div>
            </div>
            <div className="mt-5 flex items-center justify-between border-t border-slate-200 pt-3">
              <div className="relative inline-flex">
                <button className="rounded-l-md bg-indigo-600 px-3 py-1.5 text-xs text-white">
                  Export
                </button>
                <button
                  className="rounded-r-md border-l border-white/30 bg-indigo-600 px-2 py-1.5 text-xs text-white"
                  aria-label="More export options"
                >
                  ▾
                </button>
              </div>
              <button className="rounded-md border border-emerald-300 bg-emerald-50 px-2 py-1 text-xs text-emerald-700">
                Mark reviewed ✓
              </button>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Module 3 — Full (rich) view (kept for later expansion)
const BoardSummaryView: React.FC<{ isActive?: boolean }> = ({
  isActive = false,
}) => {
  const { trackEvent } = useAnalytics();
  // Seed defaults so view always renders
  const stage = {
    disease: "Breast cancer",
    stage: "Stage IIA",
    tnm: "T2N0M0",
    range: "Apr–Aug 2025",
    system: "AJCC 8th",
    stagedOn: "24 Aug 2025",
  };
  const biomarkers = [
    {
      name: "ER",
      value: "90%",
      unit: "",
      method: "IHC",
      source: "P",
      specimen: "Biopsy",
      collectedAt: "20 Aug",
      reportedAt: "21 Aug",
    },
    {
      name: "PR",
      value: "80%",
      unit: "",
      method: "IHC",
      source: "P",
      specimen: "Biopsy",
      collectedAt: "20 Aug",
      reportedAt: "21 Aug",
    },
    {
      name: "HER2",
      value: "0",
      unit: " (IHC)",
      method: "IHC",
      source: "P",
      specimen: "Biopsy",
      collectedAt: "20 Aug",
      reportedAt: "21 Aug",
    },
  ];
  const lines = [
    {
      line: "1L",
      regimen: "AC → Paclitaxel",
      start: "Apr 2025",
      stop: "Aug 2025",
      response: "PR",
      evidence: ["Onc consult", "Orders", "Dispense"],
    },
    {
      line: "2L",
      regimen: "CDK4/6 + AI (Proposed)",
      start: "—",
      stop: "",
      response: "—",
      evidence: ["Plan"],
    },
  ];
  const imagingList = [
    {
      date: "22 Aug 2025",
      modality: "CT Chest",
      impression: "No new lesions; nodes stable",
      frames: ["Axial 1", "Axial 2", "Coronal 1", "Sagittal 1"],
    },
    {
      date: "10 Aug 2025",
      modality: "Mammogram",
      impression: "Right breast mass 3.1cm",
      frames: ["CC left", "MLO left", "CC right", "MLO right"],
    },
  ];
  const evidence = [
    {
      id: "path-biopsy",
      title: "Pathology report",
      source: "EMR",
      kind: "path",
      specimen: "Core biopsy",
      collectedAt: "20 Aug",
      reportedAt: "21 Aug",
      quality: "complete",
    },
    {
      id: "onc-consult",
      title: "Oncology consult note",
      source: "EMR",
      kind: "note",
      collectedAt: "24 Aug",
      reportedAt: "24 Aug",
      quality: "partial",
    },
    {
      id: "lab-erprher2",
      title: "ER/PR/HER2 panel",
      source: "LIS",
      kind: "lab",
      specimen: "Biopsy",
      collectedAt: "20 Aug",
      reportedAt: "21 Aug",
      quality: "complete",
    },
    {
      id: "img-ct",
      title: "CT report",
      source: "PACS",
      kind: "imaging",
      collectedAt: "22 Aug",
      reportedAt: "22 Aug",
      quality: "conflict",
    },
  ];

  // Local UI state
  const [display, setDisplay] = React.useState<"summary" | "full">("summary");
  const [provQuery, setProvQuery] = React.useState("");
  const [openImage, setOpenImage] = React.useState<any | null>(null);
  const [openReport, setOpenReport] = React.useState<any | null>(null);

  // Phase 1 — Analytics & metrics
  const ASSEMBLY_KEY = "m3_assembly_samples";
  const MAX_SAMPLES = 12;
  const [timerStart, setTimerStart] = React.useState<number | null>(null);
  const [assemblySamples, setAssemblySamples] = React.useState<number[]>([]);

  const loadSamples = React.useCallback((): number[] => {
    try {
      if (typeof window === "undefined") return [];
      const raw = localStorage.getItem(ASSEMBLY_KEY);
      if (!raw) return [];
      const arr = JSON.parse(raw);
      return Array.isArray(arr)
        ? arr.filter((n: any) => typeof n === "number")
        : [];
    } catch {
      return [];
    }
  }, []);

  const saveSamples = React.useCallback((arr: number[]) => {
    try {
      if (typeof window === "undefined") return;
      localStorage.setItem(
        ASSEMBLY_KEY,
        JSON.stringify(arr.slice(-MAX_SAMPLES))
      );
    } catch {}
  }, []);

  const recordAssemblyTime = React.useCallback(() => {
    if (timerStart == null) return;
    const durationSec = Math.max(
      1,
      Math.round((Date.now() - timerStart) / 1000)
    );
    setAssemblySamples((prev) => {
      const next = [...prev, durationSec].slice(-MAX_SAMPLES);
      saveSamples(next);
      try {
        trackEvent &&
          trackEvent("assembly_time_recorded", { seconds: durationSec });
      } catch {}
      return next;
    });
    setTimerStart(null);
  }, [timerStart, saveSamples, trackEvent]);

  React.useEffect(() => {
    if (!isActive) return;
    setTimerStart(Date.now());
    setAssemblySamples(loadSamples());
    try {
      trackEvent && trackEvent("board_summary_mount");
    } catch {}
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isActive]);

  const formatDuration = (sec: number) =>
    `${Math.floor(sec / 60)}m ${sec % 60}s`;
  const latestSec = assemblySamples.length
    ? assemblySamples[assemblySamples.length - 1]
    : null;

  const sparkPoints = React.useMemo(() => {
    const valsSrc = assemblySamples.length ? assemblySamples.slice(-9) : [];
    const fallback = [160, 140, 130, 125, 120, 115, 110, 108, 105];
    const vals = valsSrc.length >= 2 ? valsSrc : fallback;
    const width = 128;
    const yTop = 12;
    const yBottom = 26;
    const minSec = 30;
    const maxSec = 900; // 15 minutes baseline
    const n = vals.length;
    const step = n > 1 ? width / (n - 1) : width;
    const scaleY = (sec: number) => {
      const clamped = Math.max(minSec, Math.min(maxSec, sec));
      const t = (clamped - minSec) / (maxSec - minSec); // 0..1
      return yTop + t * (yBottom - yTop);
    };
    return vals
      .map((sec, i) => `${Math.round(i * step)},${Math.round(scaleY(sec))}`)
      .join(" ");
  }, [assemblySamples]);

  // Phase 3 — Deep-linking state and helpers
  const [selectedProvId, setSelectedProvId] = React.useState<string | null>(
    null
  );
  const provRefs = React.useRef<Record<string, HTMLDivElement | null>>({});
  const highlightMatch = (text: string): React.ReactNode => {
    if (!provQuery) return text;
    const idx = text.toLowerCase().indexOf(provQuery.toLowerCase());
    if (idx === -1) return text;
    const before = text.slice(0, idx);
    const match = text.slice(idx, idx + provQuery.length);
    const after = text.slice(idx + provQuery.length);
    return (
      <>
        {before}
        <mark className="rounded bg-yellow-200 px-0.5">{match}</mark>
        {after}
      </>
    );
  };

  // Debounced provenance query and memoized evidence list
  const [debouncedProvQuery, setDebouncedProvQuery] = React.useState(provQuery);
  React.useEffect(() => {
    const id = window.setTimeout(() => setDebouncedProvQuery(provQuery), 120);
    return () => window.clearTimeout(id);
  }, [provQuery]);

  const filteredEvidence = React.useMemo(() => {
    const q = debouncedProvQuery.trim().toLowerCase();
    if (!q) return evidence;
    return evidence.filter((e) =>
      [e.title, e.source, e.specimen || "", e.kind]
        .join(" ")
        .toLowerCase()
        .includes(q)
    );
  }, [debouncedProvQuery]);

  // A11y helpers: focus trap, keyboard shortcuts, focus restore
  const imageModalRef = React.useRef<HTMLDivElement>(null);
  const reportDrawerRef = React.useRef<HTMLDivElement>(null);
  const lastFocusedRef = React.useRef<HTMLElement | null>(null);

  const handleTrapKey = (
    e: React.KeyboardEvent,
    containerRef: React.RefObject<HTMLElement>
  ) => {
    if (e.key !== "Tab") return;
    const container = containerRef.current;
    if (!container) return;
    const focusableSelectors =
      'a[href], button, input, select, textarea, [tabindex]:not([tabindex="-1"])';
    const nodes = Array.from(
      container.querySelectorAll<HTMLElement>(focusableSelectors)
    ).filter((el) => !el.hasAttribute("disabled"));
    if (nodes.length === 0) return;
    const first = nodes[0];
    const last = nodes[nodes.length - 1];
    const current = document.activeElement as HTMLElement;
    if (e.shiftKey) {
      if (current === first || !container.contains(current)) {
        e.preventDefault();
        last.focus();
      }
    } else {
      if (current === last) {
        e.preventDefault();
        first.focus();
      }
    }
  };

  React.useEffect(() => {
    if (!isActive) return;
    const onKey = (e: KeyboardEvent) => {
      const key = e.key.toLowerCase();
      if (key === "f") {
        setDisplay((prev) => {
          const next = prev === "summary" ? "full" : "summary";
          try {
            trackEvent && trackEvent("view_toggle_key", { display: next });
          } catch {}
          return next;
        });
      }
      if (e.key === "Escape") {
        if (openImage) {
          setOpenImage(null);
          e.preventDefault();
        } else if (openReport) {
          setOpenReport(null);
          e.preventDefault();
        }
      }
    };
    window.addEventListener("keydown", onKey);
    return () => window.removeEventListener("keydown", onKey);
  }, [isActive, openImage, openReport, trackEvent]);

  React.useEffect(() => {
    if (openImage) {
      lastFocusedRef.current = (document.activeElement as HTMLElement) || null;
      const c = imageModalRef.current;
      setTimeout(() => {
        const first = c?.querySelector<HTMLElement>(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        first?.focus();
      }, 0);
    }
  }, [openImage]);

  React.useEffect(() => {
    if (openReport) {
      lastFocusedRef.current = (document.activeElement as HTMLElement) || null;
      const c = reportDrawerRef.current;
      setTimeout(() => {
        const first = c?.querySelector<HTMLElement>(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        first?.focus();
      }, 0);
    }
  }, [openReport]);

  React.useEffect(() => {
    if (!openImage && !openReport && lastFocusedRef.current) {
      lastFocusedRef.current.focus();
      lastFocusedRef.current = null;
    }
  }, [openImage, openReport]);

  return (
    <motion.div
      initial={false}
      animate={{ opacity: 1 }}
      transition={{ duration: 0 }}
      className="flex h-full flex-col"
    >
      <div className="relative px-8 pb-6 pt-24 md:px-16 md:pb-8 md:pt-32">
        <div className="flex flex-col justify-center md:flex-row md:items-start md:gap-20">
          <h2 className="max-w-[18ch] text-center text-6xl font-light leading-[1.05] text-teal-800 md:text-left md:text-7xl lg:text-8xl">
            Board‑Ready,
            <br />
            <span className="font-light italic">auditable in seconds</span>
          </h2>
          <div className="mt-6 max-w-2xl md:mt-4">
            <p className="text-lg font-light text-slate-700">
              Auditable draft for tumor boards—stage, biomarkers, past lines &
              performance status with citations.
            </p>
            <div
              role="list"
              aria-label="Capabilities"
              className="mt-3 flex flex-wrap gap-2"
            >
              {["Board export", "Gaps highlighted", "Human‑in‑loop"].map(
                (cap) => (
                  <span
                    key={cap}
                    role="listitem"
                    className="inline-flex items-center gap-1 rounded-full border border-white/60 bg-white/90 px-3 py-1 text-xs font-medium text-slate-800 backdrop-blur-md"
                  >
                    {cap}
                  </span>
                )
              )}
            </div>
          </div>
        </div>
        {/* Indigo liquid-glass tab */}
        <div className="absolute left-6 top-1/2 hidden -translate-y-1/2 md:left-12 md:block">
          <div className="inline-flex items-center gap-2 rounded-[26px] border border-white/70 bg-white/90 px-7 py-3.5 shadow-[0_10px_28px_rgba(0,0,0,0.12)] backdrop-blur-2xl">
            <div
              className="h-2 w-2 rounded-full"
              style={{ backgroundColor: "#fef9c3" }}
            />
            <div className="flex items-baseline gap-1">
              <span className="text-[12px] font-bold tracking-wider text-slate-800">
                BOARD‑READY
              </span>
              <span className="text-[11px] font-medium tracking-wide text-slate-600">
                SUMMARY
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* App container (same scaffold) */}
      <div className="relative h-[65vh] w-full overflow-hidden rounded-t-2xl bg-white shadow-2xl md:h-[70vh] lg:h-[74vh]">
        <div className="flex h-full">
          {/* Left (8) placeholder card */}
          <div className="w-2/3 space-y-4 p-6">
            <div className="h-full rounded-xl bg-white p-4 shadow-sm ring-1 ring-slate-200">
              {/* Header bar */}
              <div className="flex items-center justify-between border-b border-slate-200 pb-3">
                <div className="flex flex-col">
                  <div className="text-sm font-semibold text-slate-900">
                    Draft for discussion
                  </div>
                  <div className="mt-0.5 text-xs text-slate-600">
                    {stage.disease} • {stage.stage} ({stage.tnm}) •{" "}
                    {stage.range}
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div
                    className="inline-flex overflow-hidden rounded-full border border-gray-300"
                    role="group"
                    aria-label="View mode"
                  >
                    <button
                      className={`px-3 py-1 text-xs ${display === "summary" ? "bg-gray-100 text-gray-900" : "bg-white text-gray-600"} focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500`}
                      aria-pressed={display === "summary"}
                      onClick={() => {
                        setDisplay("summary");
                        try {
                          trackEvent &&
                            trackEvent("view_toggle", { display: "summary" });
                        } catch {}
                      }}
                    >
                      Summary
                    </button>
                    <button
                      className={`px-3 py-1 text-xs ${display === "full" ? "bg-gray-100 text-gray-900" : "bg-white text-gray-600"} focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500`}
                      aria-pressed={display === "full"}
                      onClick={() => {
                        setDisplay("full");
                        try {
                          trackEvent &&
                            trackEvent("view_toggle", { display: "full" });
                        } catch {}
                      }}
                    >
                      Full
                    </button>
                  </div>
                  <button
                    className="rounded-md border border-slate-300 px-2 py-1 text-xs hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                    onClick={() => {
                      try {
                        trackEvent && trackEvent("board_summary_resync");
                      } catch {}
                    }}
                  >
                    Re‑sync
                  </button>
                </div>
              </div>

              {/* Diagnosis & Stage */}
              <section className="pt-4">
                <div className="text-xs font-semibold uppercase tracking-wider text-slate-700">
                  Diagnosis & Stage
                </div>
                <div className="mt-2 flex flex-wrap gap-2">
                  <button
                    data-cite-id="primary_site"
                    className="rounded-full border border-slate-300 px-2 py-1 text-[11px] text-slate-700 hover:bg-slate-50"
                    onClick={() => {
                      const id = "path-biopsy";
                      const match = evidence.find((e) => e.id === id);
                      if (match) {
                        setOpenReport({
                          id: match.id,
                          title: match.title,
                          source: match.source,
                        });
                      }
                      try {
                        trackEvent &&
                          trackEvent("cite_click", { field: "primary_site" });
                      } catch {}
                    }}
                  >
                    Primary site: Breast
                  </button>
                  <button
                    data-cite-id="tnm"
                    className="rounded-full border border-slate-300 px-2 py-1 text-[11px] text-slate-700 hover:bg-slate-50"
                    onClick={() => {
                      const id = "onc-consult";
                      const match = evidence.find((e) => e.id === id);
                      if (match) {
                        setOpenReport({
                          id: match.id,
                          title: match.title,
                          source: match.source,
                        });
                      }
                      try {
                        trackEvent &&
                          trackEvent("cite_click", { field: "tnm" });
                      } catch {}
                    }}
                  >
                    TNM: {stage.tnm}
                  </button>
                  <button
                    data-cite-id="system"
                    className="rounded-full border border-slate-300 px-2 py-1 text-[11px] text-slate-700 hover:bg-slate-50"
                    onClick={() => {
                      const id = "onc-consult";
                      const match = evidence.find((e) => e.id === id);
                      if (match) {
                        setOpenReport({
                          id: match.id,
                          title: match.title,
                          source: match.source,
                        });
                      }
                      try {
                        trackEvent &&
                          trackEvent("cite_click", { field: "system" });
                      } catch {}
                    }}
                  >
                    {stage.system}
                  </button>
                  <button
                    data-cite-id="staged_on"
                    className="rounded-full border border-slate-300 px-2 py-1 text-[11px] text-slate-700 hover:bg-slate-50"
                    onClick={() => {
                      const id = "onc-consult";
                      const match = evidence.find((e) => e.id === id);
                      if (match) {
                        setOpenReport({
                          id: match.id,
                          title: match.title,
                          source: match.source,
                        });
                      }
                      try {
                        trackEvent &&
                          trackEvent("cite_click", { field: "staged_on" });
                      } catch {}
                    }}
                  >
                    Staged {stage.stagedOn}
                  </button>
                </div>

                {display === "full" && (
                  <div className="mt-3 rounded-lg bg-slate-50/70 p-3 text-xs text-slate-700 ring-1 ring-slate-200">
                    <div>
                      <span className="text-slate-500">System:</span>{" "}
                      {stage.system}
                    </div>
                    <div>
                      <span className="text-slate-500">Rationale:</span> Tumor
                      size 2–5 cm, no regional node involvement, no distant
                      mets.
                    </div>
                    <div>
                      <span className="text-slate-500">Source:</span> Pathology
                      20 Aug · Imaging 22 Aug · Onc Consult 24 Aug
                    </div>
                  </div>
                )}
              </section>

              {/* Biomarkers */}
              <section className="pt-5">
                <div className="text-xs font-semibold uppercase tracking-wider text-slate-700">
                  Biomarkers
                </div>
                <div className="mt-2 flex flex-wrap gap-2">
                  {biomarkers.map((bm) => (
                    <div key={bm.name} className="group relative">
                      <button
                        className="inline-flex items-center gap-1 rounded-full border border-slate-300 px-2 py-1 text-[11px] text-slate-700 hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                        data-cite-id={`biomarker_${bm.name}`}
                        onClick={() => {
                          const id = "lab-erprher2";
                          const match = evidence.find((e) => e.id === id);
                          if (match) {
                            setOpenReport({
                              id: match.id,
                              title: match.title,
                              source: match.source,
                            });
                          }
                          try {
                            trackEvent &&
                              trackEvent("cite_click", {
                                field: `biomarker_${bm.name}`,
                              });
                          } catch {}
                        }}
                        aria-label={`${bm.name} ${bm.value}${bm.unit ? " " + bm.unit : ""}`}
                        aria-describedby={`tip-bio-${bm.name}`}
                      >
                        <span
                          className="inline-flex h-3.5 w-3.5 items-center justify-center rounded-full text-[9px] font-bold text-white"
                          style={{
                            background:
                              bm.source === "P"
                                ? "#6366f1"
                                : bm.source === "L"
                                  ? "#10b981"
                                  : "#0ea5e9",
                          }}
                        >
                          {bm.source}
                        </span>
                        <span>
                          {bm.name} {bm.value}
                          {bm.unit ? bm.unit : ""}
                        </span>
                      </button>
                      {/* Tooltip */}
                      <div
                        id={`tip-bio-${bm.name}`}
                        role="tooltip"
                        className="pointer-events-none absolute left-1/2 mt-1 w-64 -translate-x-1/2 rounded-md bg-white p-2 text-[11px] text-slate-700 opacity-0 shadow-lg ring-1 ring-slate-200 transition-opacity duration-150 group-focus-within:opacity-100 group-hover:opacity-100"
                      >
                        <div>
                          <span className="text-slate-500">Method:</span>{" "}
                          {bm.method}
                        </div>
                        {bm.specimen && (
                          <div>
                            <span className="text-slate-500">Specimen:</span>{" "}
                            {bm.specimen}
                          </div>
                        )}
                        <div className="mt-1 grid grid-cols-2 gap-2">
                          <div>
                            <span className="text-slate-500">Collected:</span>{" "}
                            {bm.collectedAt}
                          </div>
                          <div>
                            <span className="text-slate-500">Reported:</span>{" "}
                            {bm.reportedAt}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Lines of Therapy */}
              <section className="pt-5">
                <div className="text-xs font-semibold uppercase tracking-wider text-slate-700">
                  Lines of Therapy
                </div>
                <div className="mt-2 space-y-2">
                  {lines.map((ln) => (
                    <div
                      key={ln.line}
                      className="flex items-start justify-between rounded-lg bg-white p-2 ring-1 ring-slate-200"
                    >
                      <div className="flex min-w-0 items-start gap-3">
                        <span className="shrink-0 rounded-full bg-slate-100 px-2 py-0.5 text-[11px] text-slate-700 ring-1 ring-slate-200">
                          {ln.line}
                        </span>
                        <div className="min-w-0">
                          <div className="truncate text-base text-slate-900">
                            {ln.regimen}
                          </div>
                          <div className="text-[13px] text-slate-600">
                            {ln.start}
                            {ln.stop ? ` – ${ln.stop}` : ""}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="text-[11px] text-emerald-700">
                          {ln.response}
                        </div>
                        <div className="hidden items-center gap-1 md:flex">
                          {ln.evidence.map((ev) => (
                            <button
                              key={ev}
                              className="rounded-full border border-slate-300 px-1.5 py-0.5 text-[10px] text-slate-700 hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                              onClick={() => {
                                try {
                                  trackEvent &&
                                    trackEvent("evidence_click", {
                                      line: ln.line,
                                      ev,
                                    });
                                } catch {}
                              }}
                            >
                              {ev}
                            </button>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Performance Status & Key Scores */}
              <section className="pt-5">
                <div className="text-xs font-semibold uppercase tracking-wider text-slate-700">
                  Performance Status
                </div>
                <div className="mt-2 flex items-center gap-2">
                  <div className="group relative">
                    <button
                      className="rounded-full border border-slate-300 px-2 py-1 text-[11px] text-slate-700 hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                      onClick={() => {
                        try {
                          trackEvent &&
                            trackEvent("cite_click", { field: "ecog" });
                        } catch {}
                      }}
                      aria-label="ECOG 1 (24 Aug 2025)"
                      aria-describedby="tip-ecog"
                    >
                      ECOG: 1
                    </button>
                    <div
                      id="tip-ecog"
                      role="tooltip"
                      className="pointer-events-none absolute left-0 mt-1 w-72 rounded-md bg-white p-2 text-[11px] text-slate-700 opacity-0 shadow-lg ring-1 ring-slate-200 transition-opacity duration-150 group-focus-within:opacity-100 group-hover:opacity-100"
                    >
                      <div>
                        <span className="text-slate-500">Observed:</span> 24 Aug
                        2025 · Dr. Mehta
                      </div>
                      <div className="mt-1">
                        <span className="text-slate-500">Note:</span>{" "}
                        "Ambulatory; light work"
                      </div>
                    </div>
                  </div>
                  <span className="text-[11px] text-slate-600">
                    24 Aug 2025 · Dr. Mehta
                  </span>
                </div>
              </section>

              {/* Imaging‑at‑a‑glance */}
              <section className="pt-5">
                <div className="text-xs font-semibold uppercase tracking-wider text-slate-700">
                  Imaging
                </div>
                <div className="mt-2 grid grid-cols-1 gap-3 md:grid-cols-2">
                  {imagingList.map((img) => (
                    <div
                      key={img.date + img.modality}
                      className="flex items-start justify-between rounded-lg bg-white p-3 ring-1 ring-slate-200"
                    >
                      <div className="min-w-0">
                        <div className="truncate text-base text-slate-900">
                          {img.modality} · {img.date}
                        </div>
                        <div className="mt-0.5 truncate text-[13px] text-slate-600">
                          {img.impression}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <button
                          className="rounded-md border border-slate-300 px-2 py-1 text-xs hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                          onClick={() => {
                            setOpenImage(img);
                            try {
                              trackEvent &&
                                trackEvent("imaging_viewer_open", {
                                  date: img.date,
                                  modality: img.modality,
                                });
                            } catch {}
                          }}
                        >
                          Open viewer
                        </button>
                        <button
                          className="rounded-md border border-slate-300 px-2 py-1 text-xs hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                          onClick={() => {
                            const id = "img-ct";
                            const match = evidence.find((e) => e.id === id);
                            if (match) {
                              setOpenReport({
                                id: match.id,
                                title: match.title,
                                source: match.source,
                              });
                            }
                            try {
                              trackEvent &&
                                trackEvent("full_report_open", {
                                  type: "imaging",
                                  date: img.date,
                                });
                            } catch {}
                          }}
                        >
                          Report →
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Gaps banner */}
              <section className="pt-5">
                <div className="flex items-start justify-between rounded-lg bg-amber-50 p-3 ring-1 ring-amber-200">
                  <div className="flex items-start gap-2">
                    <span
                      className="mt-0.5 inline-block h-2 w-2 rounded-full bg-amber-500"
                      aria-hidden
                    />
                    <div>
                      <div className="text-sm font-medium text-amber-800">
                        We found 2 missing items before board
                      </div>
                      <div className="mt-1 flex flex-wrap gap-2">
                        <div className="inline-flex items-center gap-2 rounded-full bg-white px-2 py-1 text-[11px] text-amber-800 ring-1 ring-amber-200">
                          Genomic assay pending
                          <div className="flex items-center gap-1">
                            <button
                              className="rounded-md px-1.5 py-0.5 ring-1 ring-amber-200 hover:bg-amber-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                              onClick={() => {
                                try {
                                  trackEvent &&
                                    trackEvent("gap_action", {
                                      gap: "genomic_assay",
                                      action: "order",
                                    });
                                } catch {}
                              }}
                            >
                              Order
                            </button>
                            <button
                              className="rounded-md px-1.5 py-0.5 ring-1 ring-amber-200 hover:bg-amber-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                              onClick={() => {
                                try {
                                  trackEvent &&
                                    trackEvent("gap_action", {
                                      gap: "genomic_assay",
                                      action: "request",
                                    });
                                } catch {}
                              }}
                            >
                              Request
                            </button>
                            <button
                              className="rounded-md px-1.5 py-0.5 ring-1 ring-amber-200 hover:bg-amber-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                              onClick={() => {
                                try {
                                  trackEvent &&
                                    trackEvent("gap_action", {
                                      gap: "genomic_assay",
                                      action: "assign",
                                    });
                                } catch {}
                              }}
                            >
                              Assign
                            </button>
                          </div>
                        </div>
                        <div className="inline-flex items-center gap-2 rounded-full bg-white px-2 py-1 text-[11px] text-amber-800 ring-1 ring-amber-200">
                          Echo due
                          <div className="flex items-center gap-1">
                            <button
                              className="rounded-md px-1.5 py-0.5 ring-1 ring-amber-200 hover:bg-amber-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                              onClick={() => {
                                try {
                                  trackEvent &&
                                    trackEvent("gap_action", {
                                      gap: "echo",
                                      action: "order",
                                    });
                                } catch {}
                              }}
                            >
                              Order
                            </button>
                            <button
                              className="rounded-md px-1.5 py-0.5 ring-1 ring-amber-200 hover:bg-amber-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                              onClick={() => {
                                try {
                                  trackEvent &&
                                    trackEvent("gap_action", {
                                      gap: "echo",
                                      action: "request",
                                    });
                                } catch {}
                              }}
                            >
                              Request
                            </button>
                            <button
                              className="rounded-md px-1.5 py-0.5 ring-1 ring-amber-200 hover:bg-amber-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                              onClick={() => {
                                try {
                                  trackEvent &&
                                    trackEvent("gap_action", {
                                      gap: "echo",
                                      action: "assign",
                                    });
                                } catch {}
                              }}
                            >
                              Assign
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    className="rounded-md px-2 py-1 text-xs text-amber-800 ring-1 ring-amber-200 hover:bg-amber-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                    onClick={() => {
                      try {
                        trackEvent && trackEvent("gap_dismiss");
                      } catch {}
                    }}
                  >
                    Dismiss
                  </button>
                </div>
              </section>

              {/* Placeholders for next phases */}
              <section className="pt-5 text-xs text-slate-500">
                More blocks and provenance linkage will follow.
              </section>

              {/* Actions footer */}
              <div className="mt-5 flex items-center justify-between border-t border-slate-200 pt-3">
                <div className="relative inline-flex">
                  <button
                    className="rounded-l-md bg-indigo-600 px-3 py-1.5 text-xs text-white hover:bg-indigo-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/70"
                    onClick={() => {
                      try {
                        trackEvent &&
                          trackEvent("export_click", { type: "default" });
                      } catch {}
                      recordAssemblyTime();
                    }}
                  >
                    Export
                  </button>
                  <button
                    className="rounded-r-md border-l border-white/30 bg-indigo-600 px-2 py-1.5 text-xs text-white hover:bg-indigo-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/70"
                    aria-label="More export options"
                    onClick={() => {
                      try {
                        trackEvent && trackEvent("export_menu_open");
                      } catch {}
                    }}
                  >
                    ▾
                  </button>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    className="rounded-md border border-slate-300 px-2 py-1 text-xs hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                    onClick={() => {
                      try {
                        trackEvent && trackEvent("send_to_board_agenda");
                      } catch {}
                    }}
                  >
                    Send to board agenda
                  </button>
                  <button
                    className="rounded-md border border-emerald-300 bg-emerald-50 px-2 py-1 text-xs text-emerald-700 hover:bg-emerald-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                    onClick={() => {
                      try {
                        trackEvent && trackEvent("mark_reviewed");
                      } catch {}
                      recordAssemblyTime();
                    }}
                  >
                    Mark reviewed ✓
                  </button>
                </div>
              </div>

              {/* Outcome meter removed for final polish */}
            </div>
          </div>
          {/* Right (4) provenance */}
          <div className="w-1/3 border-l border-slate-200 bg-slate-50/60 p-6">
            <div className="sticky top-6 space-y-3">
              <div className="text-sm font-semibold text-slate-900">
                Provenance & controls
              </div>
              <input
                value={provQuery}
                onChange={(e) => setProvQuery(e.target.value)}
                placeholder="Search evidence..."
                className="w-full rounded-md border border-slate-200 bg-white px-3 py-1.5 text-xs focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
              <div className="divide-y divide-slate-200 overflow-hidden rounded-lg bg-white ring-1 ring-slate-200">
                {filteredEvidence.length === 0 && (
                  <div className="p-3 text-xs text-slate-600">
                    No evidence matches your search.
                  </div>
                )}
                {filteredEvidence.map((ev) => (
                  <div
                    key={ev.id}
                    ref={(el) => (provRefs.current[ev.id] = el)}
                    className={`flex items-start justify-between p-3 text-xs text-slate-700 ${selectedProvId === ev.id ? "bg-indigo-50 ring-1 ring-indigo-300" : ""}`}
                  >
                    <div className="min-w-0">
                      <div className="flex items-center gap-2">
                        <span
                          className={`inline-flex h-4 w-4 items-center justify-center rounded-full text-[9px] font-bold text-white ${ev.kind === "path" ? "bg-indigo-500" : ev.kind === "lab" ? "bg-emerald-600" : ev.kind === "imaging" ? "bg-sky-500" : "bg-slate-500"}`}
                        >
                          {ev.kind === "path"
                            ? "P"
                            : ev.kind === "lab"
                              ? "L"
                              : ev.kind === "imaging"
                                ? "I"
                                : "N"}
                        </span>
                        <span className="truncate text-slate-900">
                          {highlightMatch(ev.title) as any}
                        </span>
                      </div>
                      <div className="mt-1 text-slate-500">
                        {highlightMatch(ev.source) as any}
                        {ev.specimen ? (
                          <> · {highlightMatch(ev.specimen) as any}</>
                        ) : (
                          ""
                        )}
                      </div>
                      {(ev.collectedAt || ev.reportedAt) && (
                        <div className="mt-0.5 text-slate-500">
                          {ev.collectedAt ? `Collected ${ev.collectedAt}` : ""}
                          {ev.reportedAt ? ` · Reported ${ev.reportedAt}` : ""}
                        </div>
                      )}
                      {ev.quality && (
                        <div className="mt-1 flex items-center gap-2">
                          <span
                            className={`inline-flex items-center rounded-full px-1.5 py-0.5 text-[10px] ring-1 ${ev.quality === "complete" ? "bg-emerald-50 text-emerald-700 ring-emerald-200" : ev.quality === "partial" ? "bg-amber-50 text-amber-800 ring-amber-200" : "bg-red-50 text-red-700 ring-red-200"}`}
                          >
                            {ev.quality === "complete"
                              ? "Complete"
                              : ev.quality === "partial"
                                ? "Partial"
                                : "Conflict"}
                          </span>
                          {(ev.quality === "partial" ||
                            ev.quality === "conflict") && (
                            <button
                              className="rounded-md border border-slate-300 px-1.5 py-0.5 text-[10px] hover:bg-slate-50"
                              onClick={() => {
                                try {
                                  trackEvent &&
                                    trackEvent("prov_resolve_click", {
                                      id: ev.id,
                                    });
                                } catch {}
                              }}
                            >
                              Resolve
                            </button>
                          )}
                        </div>
                      )}
                    </div>
                    <button
                      className="shrink-0 rounded-md border border-slate-300 px-2 py-1 text-[11px] hover:bg-slate-50"
                      style={{ alignSelf: "flex-start" }}
                      onClick={() => {
                        setSelectedProvId(ev.id);
                        setOpenReport({
                          id: ev.id,
                          title: ev.title,
                          source: ev.source,
                        });
                      }}
                    >
                      View full
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Local Drawer within mockup bounds */}
        {openReport && (
          <div
            role="dialog"
            aria-modal={true}
            aria-labelledby="full-report-title"
            className="absolute inset-0 z-40"
            onKeyDown={(e) => handleTrapKey(e, reportDrawerRef)}
          >
            <div
              className="absolute inset-0 bg-black/20"
              onClick={() => setOpenReport(null)}
            />
            <div
              ref={reportDrawerRef}
              className="absolute right-0 top-0 h-full w-[520px] max-w-[50%] bg-white shadow-2xl ring-1 ring-slate-200"
            >
              <div className="flex items-center justify-between border-b border-slate-200 px-4 py-3">
                <h3
                  id="full-report-title"
                  className="truncate text-sm font-semibold text-slate-900"
                >
                  {openReport.title}
                </h3>
                <button
                  className="text-slate-600 hover:text-slate-900 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                  onClick={() => setOpenReport(null)}
                  aria-label="Close"
                >
                  ✕
                </button>
              </div>
              <div className="grid h-[calc(100%-88px)] grid-cols-[1fr,220px] gap-3 p-4 text-sm">
                <div className="h-full overflow-auto rounded-md bg-slate-50 p-3 ring-1 ring-slate-200">
                  Raw report content placeholder...
                </div>
                <div className="h-full overflow-auto rounded-md bg-white p-3 text-xs ring-1 ring-slate-200">
                  <div className="mb-2 font-semibold text-slate-900">
                    Highlights
                  </div>
                  <ul className="space-y-2">
                    <li>TNM: T2N0M0</li>
                    <li>ER 90% (IHC)</li>
                    <li>CT Chest: No new lesions</li>
                  </ul>
                </div>
              </div>
              <div className="flex items-center justify-between border-t border-slate-200 px-4 py-3">
                <div className="text-xs text-slate-500">
                  Source: {openReport.source}
                </div>
                <div className="flex items-center gap-2">
                  <button className="rounded-md border border-slate-300 px-2 py-1 text-xs hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500">
                    Copy with citations
                  </button>
                  <button className="rounded-md border border-slate-300 px-2 py-1 text-xs hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500">
                    Add to slides
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Imaging modal (simple) */}
      {openImage && (
        <div
          role="dialog"
          aria-modal={true}
          aria-labelledby="image-modal-title"
          ref={imageModalRef}
          onKeyDown={(e) => handleTrapKey(e, imageModalRef)}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
        >
          <div className="w-[560px] max-w-[90vw] rounded-xl bg-white shadow-2xl ring-1 ring-slate-200">
            <div className="flex items-center justify-between border-b border-slate-200 px-4 py-2">
              <h3
                id="image-modal-title"
                className="text-sm font-semibold text-slate-900"
              >
                {openImage.modality} · {openImage.date}
              </h3>
              <button
                className="text-slate-600 hover:text-slate-900 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                onClick={() => setOpenImage(null)}
                aria-label="Close"
              >
                ✕
              </button>
            </div>
            <div className="space-y-2 p-4 text-sm text-slate-800">
              <div className="rounded-md bg-slate-50 p-3 ring-1 ring-slate-200">
                {openImage.impression}
              </div>
              <div className="grid grid-cols-2 gap-2">
                {openImage.frames.map((f, i) => (
                  <div
                    key={i}
                    className="flex h-28 items-center justify-center rounded-md bg-slate-100 text-xs text-slate-500 ring-1 ring-slate-200"
                  >
                    {f}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Full Report Drawer (now rendered inside mockup above) */}
    </motion.div>
  );
};

const AuraBranches: React.FC<{ branches?: Branch[] }> = ({
  branches = DEFAULT_BRANCHES,
}) => {
  const [activeTab, setActiveTab] = React.useState(0);
  const { trackEvent } = useAnalytics();
  const containerRef = React.useRef<HTMLDivElement>(null);
  // Persisted view mode preference (Summary/Full)
  const [viewMode, setViewMode] = React.useState<"summary" | "full">(() => {
    if (typeof window === "undefined") return "summary";
    return (
      (localStorage.getItem("aura-view-mode") as "summary" | "full") ||
      "summary"
    );
  });
  React.useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("aura-view-mode", viewMode);
    }
  }, [viewMode]);

  // Scroll-based tab switching
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end end"],
  });

  // Add tail buffer so the last module fully settles before the next section appears
  const tailBufferVh = 100;
  const sectionHeightVh = branches.length * 200 + tailBufferVh; // Match Section5Combined timing
  // Portion of the scroll before the tail buffer begins
  const tailStart = (branches.length * 200) / sectionHeightVh; // Updated to match new timing
  // Normalize progress to exclude the tail buffer from tab index math
  const normalizedProgress = useTransform(scrollYProgress, (p: number) => {
    const clamped = Math.max(0.0001, Math.min(0.9999, p));
    return Math.min(1, clamped / tailStart);
  });

  // Transform scroll progress to tab index with smoother transitions
  // Adjusted thresholds for better timing like Section5Combined
  const tabIndex = useTransform(normalizedProgress, (p: number) => {
    const t = Math.max(0, Math.min(0.9999, p));
    const thresholds = [0.0, 0.25, 0.5, 0.75, 1.0];
    let idx = 0;
    for (let i = 0; i < thresholds.length - 1; i++) {
      const start = thresholds[i];
      const end = thresholds[i + 1];
      // Smoother transition with larger hold zone (85% of segment like Section5)
      const transitionPoint = start + (end - start) * 0.85;
      if (t >= start && t < transitionPoint) {
        idx = i;
        break;
      }
      if (t >= transitionPoint) {
        idx = Math.min(i + 1, branches.length - 1);
      }
    }
    return Math.max(0, Math.min(branches.length - 1, idx));
  });

  React.useEffect(() => {
    const unsubscribe = tabIndex.on("change", (latest) => {
      const newIndex = Math.max(
        0,
        Math.min(branches.length - 1, Math.floor(latest))
      );
      if (newIndex !== activeTab) setActiveTab(newIndex);
    });
    return () => unsubscribe();
  }, [tabIndex, activeTab, branches.length]);

  const activeBranch = branches[activeTab];

  // Get the appropriate view component based on active tab
  const getActiveView = () => {
    switch (activeBranch.id) {
      case "auto-fetch":
        return <AutoFetchView />;
      case "timeline":
        return <TimelineView />;
      case "board-ready":
        return <BoardSummaryView />;
      case "safer-actions":
        return <SaferActionsView />;
      default:
        return <AutoFetchView />;
    }
  };

  return (
    <section
      ref={containerRef}
      className="relative"
      style={{
        height: `${sectionHeightVh}vh`,
        background: activeBranch?.bgColor || undefined,
      }}
    >
      <div className="sticky top-0 h-screen w-full overflow-hidden">
        {/* Full screen gradient background */}
        <motion.div
          className="absolute inset-0"
          style={{ backgroundImage: activeBranch.bgColor }}
        />

        {/* Subtle overlay pattern */}
        <div className="absolute inset-0 opacity-10">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `repeating-linear-gradient(45deg, transparent, transparent 35px, rgba(255,255,255,.05) 35px, rgba(255,255,255,.05) 70px)`,
            }}
          />
        </div>

        {/* Content Container */}
        <div className="relative z-10 flex h-full flex-col">
          {/* Tab is rendered within the header area above; keep hidden here to avoid duplication */}
          <div className="hidden"></div>

          {/* Main Content Area */}
          <div
            className={`relative z-10 flex-1 px-6 md:px-10 ${activeBranch.id === "timeline" || activeBranch.id === "board-ready" || activeBranch.id === "safer-actions" ? "pb-0" : "pb-12"} overflow-hidden`}
          >
            <div className="relative h-full">
              <div
                style={{ display: activeTab === 0 ? "block" : "none" }}
                className="h-full"
              >
                <ModuleErrorBoundary fallback={<></>}>
                  <AutoFetchView />
                </ModuleErrorBoundary>
              </div>
              <div
                style={{ display: activeTab === 1 ? "block" : "none" }}
                className="h-full"
              >
                <ModuleErrorBoundary fallback={<></>}>
                  <TimelineView />
                </ModuleErrorBoundary>
              </div>
              <div
                style={{ display: activeTab === 2 ? "block" : "none" }}
                className="h-full"
              >
                <ModuleErrorBoundary fallback={<></>}>
                  <BoardSummaryView isActive={activeTab === 2} />
                </ModuleErrorBoundary>
              </div>
              <div
                style={{ display: activeTab === 3 ? "block" : "none" }}
                className="h-full"
              >
                <ModuleErrorBoundary fallback={<></>}>
                  <SaferActionsView />
                </ModuleErrorBoundary>
              </div>
            </div>
          </div>

          {/* Scroll indicator */}
          <motion.div
            className="absolute bottom-6 right-6 md:right-12"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1 }}
          >
            <motion.div
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="flex flex-col items-center gap-2"
            >
              <span className="text-xs font-light uppercase tracking-wider text-white/40">
                Scroll
              </span>
              <div className="h-8 w-0.5 rounded-full bg-gradient-to-b from-white/40 to-transparent" />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default AuraBranches;

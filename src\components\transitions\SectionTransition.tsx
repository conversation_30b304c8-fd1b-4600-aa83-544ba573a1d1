import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import React, { memo, useCallback, useEffect, useRef, useState } from "react";

/**
 * Section Transition Component
 *
 * Optimized smooth transitions between sections using GSAP ScrollTrigger.
 * Features performance improvements and accessibility enhancements.
 */

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

interface SectionTransitionProps {
  /** CSS selector for previous section */
  from: string;
  /** CSS selector for next section */
  to: string;
  /** Animation duration (not used with scrub) */
  duration?: number;
  /** How much sections overlap during transition (0-1) */
  overlap?: number;
  /** Optional CSS class name */
  className?: string;
  /** Whether to enable animations */
  enableAnimations?: boolean;
  /** Callback when transition completes */
  onTransitionComplete?: () => void;
}

const SectionTransition: React.FC<SectionTransitionProps> = memo(
  ({
    from,
    to,
    duration = 1,
    overlap = 0.25, // Reduced default overlap
    className = "",
    enableAnimations = true,
    onTransitionComplete,
  }) => {
    const transitionRef = useRef<HTMLDivElement>(null);
    const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
    const [isIntersecting, setIsIntersecting] = useState(false);

    // Check for reduced motion preference
    useEffect(() => {
      const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
      setPrefersReducedMotion(mediaQuery.matches);
      const handleChange = (e: MediaQueryListEvent) =>
        setPrefersReducedMotion(e.matches);
      mediaQuery.addEventListener("change", handleChange);
      return () => mediaQuery.removeEventListener("change", handleChange);
    }, []);

    // Intersection observer for performance
    useEffect(() => {
      if (!transitionRef.current) return;

      const observer = new IntersectionObserver(
        ([entry]) => setIsIntersecting(entry.isIntersecting),
        { threshold: 0.1, rootMargin: "50px" }
      );

      observer.observe(transitionRef.current);
      return () => observer.disconnect();
    }, []);

    const handleTransitionUpdate = useCallback(
      (progress: number) => {
        if (!enableAnimations || prefersReducedMotion) return;

        const fromElement = document.querySelector(from);
        const toElement = document.querySelector(to);

        if (!fromElement || !toElement) return;

        // Optimized fade out previous section
        if (progress > 1 - overlap) {
          const fadeOutProgress = (progress - (1 - overlap)) / overlap;
          gsap.set(fromElement, {
            opacity: 1 - fadeOutProgress,
            scale: 1 - fadeOutProgress * 0.015, // Reduced scale change
            force3D: true,
          });
        }

        // Optimized fade in next section
        if (progress < overlap) {
          const fadeInProgress = progress / overlap;
          gsap.set(toElement, {
            opacity: fadeInProgress,
            scale: 0.985 + fadeInProgress * 0.015, // Reduced scale change
            force3D: true,
          });
        }

        // Call completion callback
        if (progress >= 1 && onTransitionComplete) {
          onTransitionComplete();
        }
      },
      [
        from,
        to,
        overlap,
        enableAnimations,
        prefersReducedMotion,
        onTransitionComplete,
      ]
    );

    useEffect(() => {
      if (
        !transitionRef.current ||
        !isIntersecting ||
        prefersReducedMotion ||
        !enableAnimations
      )
        return;

      const scrollTrigger = ScrollTrigger.create({
        trigger: transitionRef.current,
        start: "top bottom",
        end: "bottom top",
        scrub: 0.8, // Smoother scrubbing
        refreshPriority: -1,
        onUpdate: (self) => handleTransitionUpdate(self.progress),
      });

      return () => scrollTrigger.kill();
    }, [
      isIntersecting,
      prefersReducedMotion,
      enableAnimations,
      handleTransitionUpdate,
    ]);

    return (
      <div
        ref={transitionRef}
        className={`pointer-events-none relative h-[40vh] ${className}`} // Reduced height
        aria-hidden="true"
      />
    );
  }
);

SectionTransition.displayName = "SectionTransition";

export default SectionTransition;
export type { SectionTransitionProps };

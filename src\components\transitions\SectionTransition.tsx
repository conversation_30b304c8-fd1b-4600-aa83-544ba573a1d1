import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import React, { useEffect, useRef } from "react";

/**
 * Section Transition Component
 *
 * Creates smooth transitions between sections using GSAP ScrollTrigger.
 * Handles fade and scale effects with configurable overlap timing.
 */

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

interface SectionTransitionProps {
  from: string; // selector for previous section
  to: string; // selector for next section
  duration?: number;
  overlap?: number; // How much sections overlap during transition (0-1)
}

const SectionTransition: React.FC<SectionTransitionProps> = ({
  from,
  to,
  duration = 1,
  overlap = 0.3,
}) => {
  const transitionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!transitionRef.current) return;

    const fromElement = document.querySelector(from);
    const toElement = document.querySelector(to);

    if (!fromElement || !toElement) return;

    // Create transition timeline
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: transitionRef.current,
        start: "top bottom",
        end: "bottom top",
        scrub: 1,
        onUpdate: (self) => {
          const progress = self.progress;

          // Fade out previous section
          if (progress > 1 - overlap) {
            const fadeOutProgress = (progress - (1 - overlap)) / overlap;
            gsap.set(fromElement, {
              opacity: 1 - fadeOutProgress,
              scale: 1 - fadeOutProgress * 0.02, // Slight scale down
            });
          }

          // Fade in next section
          if (progress < overlap) {
            const fadeInProgress = progress / overlap;
            gsap.set(toElement, {
              opacity: fadeInProgress,
              scale: 0.98 + fadeInProgress * 0.02, // Scale up to 1
            });
          }
        },
      },
    });

    return () => {
      tl.kill();
    };
  }, [from, to, duration, overlap]);

  return (
    <div
      ref={transitionRef}
      className="pointer-events-none relative h-[50vh]"
      aria-hidden="true"
    />
  );
};

export default SectionTransition;

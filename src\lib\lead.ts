import { supabase } from "@/integrations/supabase/client";

/**
 * Lead Management Utilities
 *
 * Handles lead submission with multiple fallback methods:
 * 1. Primary: Direct Supabase storage
 * 2. Fallback: Zapier webhook
 * 3. Fallback: Alternative API endpoints
 */

export interface PilotPayload {
  source: "pilot" | "demo";
  email: string;
  name?: string;
  role?: "physician" | "administration" | "unknown";
  hospital?: string;
  city?: string;
  phone?: string;
  notes?: string;
}

const endpoints = [
  "/api/lead",
  "/.netlify/functions/lead",
  "https://ipaxnixvzawlwgjwcpup.supabase.co/functions/v1/store-lead",
];

export async function postLead(payload: PilotPayload): Promise<Response> {
  // Primary method: Store directly in Supabase
  try {
    console.log("Attempting to store lead in Supabase:", payload);

    const { data, error } = await supabase
      .from("leads")
      .insert({
        source: payload.source,
        email: payload.email,
        name: payload.name || null,
        role: payload.role || null,
        hospital: payload.hospital || null,
        city: payload.city || null,
        phone: payload.phone || null,
        notes: payload.notes || null,
      })
      .select();

    if (error) {
      console.error("Supabase error:", error);
      throw new Error("Failed to store in Supabase: " + error.message);
    }

    console.log("Successfully stored lead in Supabase:", data);

    // Return a mock Response object for compatibility
    return new Response(JSON.stringify({ success: true, data }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (supabaseError) {
    console.error(
      "Supabase submission failed, trying fallbacks:",
      supabaseError
    );
  }

  // Fallback 1: If a Zapier webhook is provided, try that
  const zap = import.meta.env?.VITE_ZAPIER_WEBHOOK_URL as string | undefined;
  if (zap) {
    try {
      const res = await fetch(zap, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
      // Zapier returns 200 even for many errors; treat non-200 as error
      if (!res.ok) throw new Error("Zapier webhook failed");
      return res;
    } catch (zapError) {
      console.error("Zapier fallback failed:", zapError);
    }
  }

  // Fallback 2: Try other endpoints
  let lastErr: Error;
  for (const url of endpoints) {
    try {
      const res = await fetch(url, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
      if (res.ok) return res;
    } catch (e) {
      lastErr = e;
    }
  }
  throw lastErr || new Error("All submission methods failed");
}

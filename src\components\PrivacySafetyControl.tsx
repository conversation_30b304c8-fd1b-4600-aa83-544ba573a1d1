import React from "react";
import { useReducedMotion } from "framer-motion";

// Section 4 minimalist hero – no chips or copy per latest spec

const PrivacySafetyControl: React.FC = () => {
  const prefersReduced = useReducedMotion();

  const [hasImage, setHasImage] = React.useState(true);

  return (
    <section
      className="relative snap-start overflow-hidden"
      style={{
        height: "100vh",
        background: "linear-gradient(180deg, #faf7f2 0%, #f6f7fb 100%)",
      }}
    >
      {/* Soft background washes, Microsoft-hero inspired but on-brand */}
      <style>
        {`
          @keyframes shieldDrift { 
            0% { transform: translate3d(0,0,0) scale(1); }
            50% { transform: translate3d(0,-6px,0) scale(1.02); }
            100% { transform: translate3d(0,0,0) scale(1); }
          }
        `}
      </style>
      <div className="relative sticky top-0 h-screen">
        {/* Soft background washes pinned with the viewport */}
        <div className="pointer-events-none absolute inset-0">
          <div
            className="absolute -inset-24"
            style={{
              background:
                "radial-gradient(1100px 760px at 18% 24%, rgba(251,244,234,0.9), transparent 60%), radial-gradient(1200px 820px at 88% 85%, rgba(186,197,255,0.22), transparent 62%)",
            }}
          />
          <div
            className="absolute inset-0"
            style={{
              background:
                "radial-gradient(120% 80% at 50% 50%, transparent 62%, rgba(0,0,0,0.05) 100%)",
            }}
          />
        </div>

        {/* Right-centered shield inside sticky viewport */}
        <div className="absolute right-6 top-1/2 z-10 -translate-y-1/2 md:right-10 lg:right-16">
          <div
            className="pointer-events-none absolute right-0 top-1/2 h-[60vw] max-h-[780px] w-[60vw] max-w-[780px] -translate-y-1/2 translate-x-6 rounded-full opacity-60 blur-3xl"
            style={{
              background:
                "radial-gradient(circle at 60% 40%, rgba(186,197,255,0.45), transparent 60%)",
            }}
          />
          {hasImage ? (
            <img
              src="/media/security-shield.png"
              alt="Security shield"
              className="h-auto max-w-[90vw] drop-shadow-2xl sm:w-[520px] md:w-[600px] lg:w-[680px] xl:w-[760px] 2xl:w-[840px]"
              style={{
                animation: prefersReduced
                  ? undefined
                  : "shieldDrift 18s ease-in-out infinite",
                filter: "drop-shadow(0 30px 60px rgba(79,70,229,0.25))",
              }}
              onError={() => setHasImage(false)}
            />
          ) : (
            <svg
              width="560"
              height="560"
              viewBox="0 0 512 512"
              className="max-w-[90vw]"
              aria-label="Security shield"
              style={{
                animation: prefersReduced
                  ? undefined
                  : "shieldDrift 18s ease-in-out infinite",
              }}
            >
              <defs>
                <linearGradient id="g" x1="0" x2="1" y1="0" y2="1">
                  <stop offset="0%" stopColor="#c7d2fe" />
                  <stop offset="100%" stopColor="#93c5fd" />
                </linearGradient>
              </defs>
              <path
                d="M256 32l160 64v128c0 106-69 201-160 240C165 425 96 330 96 224V96l160-64z"
                fill="url(#g)"
                stroke="#e5e7eb"
                strokeWidth="8"
              />
              <path
                d="M256 80l112 44v100c0 79-49 149-112 178-63-29-112-99-112-178V124l112-44z"
                fill="#ffffff40"
              />
            </svg>
          )}
        </div>

        {/* Left-centered heading in sticky viewport */}
        <div className="container relative z-0 mx-auto px-8 md:px-16">
          <div className="grid min-h-[60vh] items-center gap-12 md:min-h-[70vh] lg:min-h-[80vh] lg:grid-cols-2">
            <div>
              <h2 className="max-w-[20ch] text-left text-6xl font-light leading-[1.05] text-slate-900 md:text-7xl lg:text-8xl">
                Privacy, safety and control
                <br />
                by design
              </h2>
            </div>
            <div aria-hidden="true" />
          </div>
        </div>
      </div>
    </section>
  );
};

export default PrivacySafetyControl;

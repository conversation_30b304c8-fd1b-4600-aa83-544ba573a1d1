import PilotDialog from "@/components/common/PilotDialog";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { lazy, Suspense } from "react";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import ErrorBoundary from "./components/common/ErrorBoundary";
import { PageLoadingState } from "./components/common/LoadingStates";

const Index = lazy(() =>
  import("./pages/Index").then((module) => ({ default: module.default }))
);
const TrustExplainer = lazy(() =>
  import("./pages/TrustExplainer").then((module) => ({
    default: module.default,
  }))
);
const NotFound = lazy(() =>
  import("./pages/NotFound").then((module) => ({ default: module.default }))
);
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 10, // 10 minutes
      retry: 1,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      refetchOnMount: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

const App = () => (
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <PilotDialog />
        <BrowserRouter>
          <Suspense fallback={<PageLoadingState />}>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/trust/:slug" element={<TrustExplainer />} />
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Suspense>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  </ErrorBoundary>
);

export default App;

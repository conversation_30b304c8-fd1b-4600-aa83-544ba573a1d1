const ValuesSection = () => {
  const values = [
    {
      title: "Simplicity",
      description: "We don't overcomplicate. We aim for less.",
      background: "bg-white",
    },
    {
      title: "Safety & Security",
      description:
        "We are committed to the advancement of AI driven by ethical principles. We work to preserve digital safety while respecting human rights like privacy, freedom of speech, and security.",
      background: "bg-gradient-purple",
      hasMagnifyingGlass: true,
    },
    {
      title: "Evaluation",
      description:
        "We rely on internal signals and user feedback to continuously improve.",
      background: "bg-gray-900",
      textColor: "text-white",
    },
  ];

  return (
    <section className="bg-white py-32">
      <div className="container mx-auto px-6">
        <div className="space-y-32">
          {values.map((value, index) => (
            <div
              key={index}
              className={`relative flex min-h-screen items-center overflow-hidden ${value.background} ${value.textColor || "text-foreground"}`}
            >
              <div className="grid w-full items-center gap-20 px-6 lg:grid-cols-2 lg:px-20">
                <div>
                  <h3 className="mb-8 text-4xl font-light leading-tight md:text-5xl lg:text-6xl">
                    {value.title}
                  </h3>
                  <p className="max-w-lg text-lg leading-relaxed opacity-80 md:text-xl">
                    {value.description}
                  </p>
                </div>

                <div className="relative flex justify-center">
                  {value.hasMagnifyingGlass ? (
                    <div className="relative">
                      {/* Magnifying glass inspired by Microsoft AI */}
                      <div className="bg-gradient-radial flex h-64 w-64 items-center justify-center rounded-full from-purple-400 via-purple-500 to-purple-600 shadow-organic">
                        <div className="h-32 w-32 rounded-full border-4 border-white/30 bg-white/20 backdrop-blur-sm"></div>
                      </div>
                      <div className="absolute -bottom-8 -right-8 h-2 w-16 origin-left rotate-45 transform rounded-full bg-purple-600"></div>
                    </div>
                  ) : index === 2 ? (
                    <div className="text-center">
                      <div className="inline-block rounded-full bg-red-900 px-8 py-4 text-lg font-semibold text-white">
                        See job openings
                      </div>
                    </div>
                  ) : null}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ValuesSection;

import interoperabilityImage from "@/assets/interoperability-diagram.jpg";
import { Brain, Clock, Cloud, Database, Monitor, Shield } from "lucide-react";
import React from "react";

/**
 * Features Section Component
 *
 * Showcases key product features with icons, metrics, and descriptions.
 * Includes data aggregation, AI reports, and enterprise capabilities.
 */

interface Feature {
  icon: React.ReactNode;
  title: string;
  description: string;
  highlight: string;
  metric: string;
}

const Features: React.FC = () => {
  const features: Feature[] = [
    {
      icon: <Database className="h-6 w-6 text-primary" />,
      title: "Data Aggregation",
      description:
        "Seamless integration of EMR, PACS, LIS, HIMS, pathology, billing, and telemedicine sources.",
      highlight: "7+ Sources",
      metric: "99.9% Uptime",
    },
    {
      icon: <Clock className="h-6 w-6 text-accent" />,
      title: "Longitudinal Profiles",
      description:
        "Chronological patient timelines from multi-source inputs for comprehensive care context.",
      highlight: "Real-time",
      metric: "< 2s Response",
    },
    {
      icon: <Brain className="h-6 w-6 text-success" />,
      title: "AI-Generated Reports",
      description:
        "Contextual analysis with grounded citations from PubMed, ClinicalTrials.gov, and other databases.",
      highlight: "Evidence-Based",
      metric: "95% Accuracy",
    },
  ];

  const additionalFeatures: Feature[] = [
    {
      icon: <Monitor className="h-6 w-6 text-primary" />,
      title: "Intuitive Dashboard",
      description:
        "Modern interface designed for tumor boards with query-based evidence retrieval capabilities.",
      highlight: "User-Friendly",
      metric: "4.9/5 Rating",
    },
    {
      icon: <Cloud className="h-6 w-6 text-accent" />,
      title: "Enterprise Scalability",
      description:
        "Cloud deployment for enterprises with ABDM-compliant APIs and flexible architecture.",
      highlight: "ABDM Ready",
      metric: "Auto-scaling",
    },
    {
      icon: <Shield className="h-6 w-6 text-success" />,
      title: "Full Compliance",
      description:
        "Complete adherence to DPDP Act 2023, ABDM guidelines, FHIR standards, and international regulations.",
      highlight: "Certified",
      metric: "SOC 2 Type II",
    },
  ];

  return (
    <section id="features" className="bg-gradient-subtle py-16 lg:py-20">
      <div className="container-optimized">
        <div className="mb-16 text-center">
          <div className="mb-4 inline-flex items-center rounded-full border border-border/50 bg-background/80 px-4 py-2 text-sm font-medium text-foreground shadow-sm">
            <div className="mr-2 h-2 w-2 animate-pulse rounded-full bg-primary"></div>
            Platform Features
          </div>
          <h2 className="lg:text-display-large mb-4 text-heading-1 text-foreground">
            Comprehensive Healthcare
            <span className="mt-1 block text-primary">Interoperability</span>
          </h2>
          <p className="text-body-large mx-auto max-w-3xl text-muted-foreground">
            Our platform combines advanced AI with healthcare-grade security and
            compliance to deliver seamless data integration and evidence-based
            insights.
          </p>
        </div>

        {/* Primary Features */}
        <div className="mb-16 grid gap-6 lg:grid-cols-3">
          {features.map((feature, index) => (
            <div key={index} className="group relative">
              <div className="h-full rounded-2xl border border-border/50 bg-background p-6 shadow-sm transition-all duration-200 hover:border-border hover:shadow-md">
                <div className="mb-5 flex items-start justify-between">
                  <div className="flex h-12 w-12 items-center justify-center rounded-xl border border-border/20 bg-background shadow-sm transition-transform duration-200 group-hover:scale-105">
                    {feature.icon}
                  </div>
                  <div className="text-right">
                    <div className="mb-1 rounded-full bg-primary px-3 py-1 text-xs font-medium text-primary-foreground">
                      {feature.highlight}
                    </div>
                    <div className="text-xs font-medium text-muted-foreground">
                      {feature.metric}
                    </div>
                  </div>
                </div>
                <h3 className="mb-3 text-heading-3 text-foreground">
                  {feature.title}
                </h3>
                <p className="text-body-base text-muted-foreground">
                  {feature.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Secondary Features */}
        <div className="mb-16 grid items-center gap-16 lg:grid-cols-2">
          <div className="relative">
            <img
              src={interoperabilityImage}
              alt="Healthcare data interoperability platform"
              className="shadow-strong w-full rounded-3xl border border-border/20"
            />
            <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-primary/10 via-transparent to-accent/10"></div>

            {/* Floating overlay */}
            <div className="bg-gradient-surface shadow-strong absolute right-6 top-6 rounded-2xl border border-border/20 p-4 backdrop-blur-sm">
              <div className="text-sm font-medium text-muted-foreground">
                Processing Speed
              </div>
              <div className="text-2xl font-black text-foreground">&lt; 2s</div>
              <div className="text-xs font-semibold text-success">
                Real-time
              </div>
            </div>
          </div>

          <div className="space-y-8">
            {additionalFeatures.map((feature, index) => (
              <div key={index} className="group flex items-start space-x-6">
                <div className="bg-gradient-surface flex h-16 w-16 flex-shrink-0 items-center justify-center rounded-2xl border border-border/20 shadow-soft transition-transform duration-300 group-hover:scale-110">
                  {feature.icon}
                </div>
                <div className="flex-1">
                  <div className="mb-3 flex items-center gap-4">
                    <h3 className="text-2xl font-bold text-foreground">
                      {feature.title}
                    </h3>
                    <div className="flex items-center gap-2">
                      <span className="bg-gradient-accent rounded-full px-2 py-1 text-xs font-bold text-white">
                        {feature.highlight}
                      </span>
                      <span className="text-xs font-medium text-muted-foreground">
                        {feature.metric}
                      </span>
                    </div>
                  </div>
                  <p className="leading-relaxed text-muted-foreground">
                    {feature.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* System Status Dashboard */}
        <div className="bg-gradient-surface shadow-strong rounded-3xl border border-border/20 p-10">
          <div className="mb-8 flex items-center justify-between">
            <h4 className="text-3xl font-bold text-foreground">
              System Integration Status
            </h4>
            <div className="flex items-center gap-2">
              <div className="h-3 w-3 animate-pulse rounded-full bg-success"></div>
              <span className="rounded-full bg-success px-4 py-2 text-sm font-bold text-success-foreground">
                All Systems Operational
              </span>
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {[
              "FHIR R4 Compliance",
              "ABDM Integration",
              "Cloud Infrastructure",
              "Security Framework",
            ].map((item, index) => (
              <div
                key={index}
                className="group rounded-2xl border border-border/50 bg-background p-6 shadow-soft transition-all duration-300 hover:shadow-medium"
              >
                <div className="mb-4 flex items-center justify-between">
                  <span className="font-bold text-foreground">{item}</span>
                  <div className="h-3 w-3 rounded-full bg-success group-hover:animate-pulse"></div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="h-2 flex-1 rounded-full bg-muted">
                    <div className="h-2 w-full rounded-full bg-gradient-hero"></div>
                  </div>
                  <span className="text-sm font-bold text-success">100%</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Features;

{"buildCommand": "npm run build", "outputDirectory": "dist", "framework": "vite", "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}, {"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*).svg", "headers": [{"key": "Cache-Control", "value": "public, max-age=604800"}]}, {"source": "/(.*).js", "headers": [{"key": "Content-Type", "value": "application/javascript"}]}, {"source": "/(.*).css", "headers": [{"key": "Content-Type", "value": "text/css"}]}, {"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data: https://fonts.gstatic.com https://r2cdn.perplexity.ai; connect-src 'self' https://api.supabase.io https://*.supabase.co"}]}]}
@import "./styles/animations.css";
@import "./styles/performance.css";
@import "./styles/responsive.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Typography Scale - Optimized for readability */
    --text-xs: 0.75rem; /* 12px */
    --text-sm: 0.875rem; /* 14px */
    --text-base: 1rem; /* 16px */
    --text-lg: 1.125rem; /* 18px */
    --text-xl: 1.25rem; /* 20px */
    --text-2xl: 1.5rem; /* 24px */
    --text-3xl: 1.875rem; /* 30px */
    --text-4xl: 2.25rem; /* 36px */
    --text-5xl: 3rem; /* 48px */
    --text-6xl: 3.75rem; /* 60px */

    /* Spacing Scale - Consistent 8px grid */
    --space-1: 0.25rem; /* 4px */
    --space-2: 0.5rem; /* 8px */
    --space-3: 0.75rem; /* 12px */
    --space-4: 1rem; /* 16px */
    --space-5: 1.25rem; /* 20px */
    --space-6: 1.5rem; /* 24px */
    --space-8: 2rem; /* 32px */
    --space-10: 2.5rem; /* 40px */
    --space-12: 3rem; /* 48px */
    --space-16: 4rem; /* 64px */
    --space-20: 5rem; /* 80px */
    --space-24: 6rem; /* 96px */

    /* Line Heights */
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;
    --brand-primary: 220 85% 58%;
    --brand-primary-light: 220 85% 68%;
    --brand-primary-lighter: 220 85% 85%;
    --brand-primary-dark: 220 85% 48%;
    --brand-primary-darker: 220 85% 35%;
    --brand-primary-foreground: 0 0% 100%;
    --brand-secondary: 45 90% 60%;
    --brand-secondary-light: 45 90% 70%;
    --brand-secondary-dark: 45 90% 50%;
    --brand-secondary-foreground: 0 0% 15%;
    --medical-critical: 0 75% 55%;
    --medical-critical-light: 0 75% 95%;
    --medical-critical-foreground: 0 0% 100%;
    --medical-warning: 45 100% 50%;
    --medical-warning-light: 45 100% 95%;
    --medical-warning-foreground: 0 0% 15%;
    --medical-success: 140 65% 42%;
    --medical-success-light: 140 65% 95%;
    --medical-success-foreground: 0 0% 100%;
    --medical-info: 210 90% 55%;
    --medical-info-light: 210 90% 95%;
    --medical-info-foreground: 0 0% 100%;
    --medical-neutral: 200 15% 60%;
    --medical-neutral-light: 200 15% 95%;
    --medical-neutral-foreground: 0 0% 15%;
    --trust-verified: 140 70% 40%;
    --trust-secure: 220 85% 50%;
    --trust-compliant: 260 60% 50%;
    --neutral-50: 220 20% 98%;
    --neutral-100: 220 15% 95%;
    --neutral-200: 220 10% 90%;
    --neutral-300: 220 8% 80%;
    --neutral-400: 220 6% 65%;
    --neutral-500: 220 5% 50%;
    --neutral-600: 220 8% 35%;
    --neutral-700: 220 12% 25%;
    --neutral-800: 220 15% 15%;
    --neutral-900: 220 20% 8%;
    --surface-warm: 35 50% 98%;
    --surface-cool: 220 30% 98%;
    --surface-soft: 45 25% 97%;
    --surface-elevated: 0 0% 100%;
    --cta-primary: 220 85% 58%;
    --cta-primary-hover: 220 85% 48%;
    --cta-secondary: 45 85% 55%;
    --cta-success: 140 65% 42%;
    --cta-danger: 0 75% 55%;
    --interactive-hover: 220 85% 95%;
    --interactive-active: 220 85% 90%;
    --interactive-focus: 220 85% 58%;
    --interactive-disabled: 220 5% 80%;
    --text-primary-contrast: 220 20% 8%;
    --text-secondary-contrast: 220 15% 25%;
    --text-muted-contrast: 220 10% 42%;
    --focus-ring: 220 100% 60%;
    --error-indication: 0 100% 35%;
    --success-indication: 140 100% 25%;
    --background: 0 0% 100%;
    --foreground: 220 20% 8%;
    --card: 0 0% 100%;
    --card-foreground: 220 20% 8%;
    --popover: 0 0% 100%;
    --popover-foreground: 220 20% 8%;
    --primary: 220 85% 58%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 220 85% 68%;
    --primary-lighter: 220 85% 85%;
    --primary-dark: 220 85% 48%;
    --secondary: 220 15% 95%;
    --secondary-foreground: 220 20% 8%;
    --secondary-accent: 45 90% 60%;
    --muted: 220 15% 95%;
    --muted-foreground: 220 10% 42%;
    --accent: 260 60% 50%;
    --accent-foreground: 0 0% 100%;
    --accent-light: 260 60% 95%;
    --accent-secondary: 45 85% 55%;
    --success: 140 65% 42%;
    --success-foreground: 0 0% 100%;
    --success-light: 140 65% 95%;
    --destructive: 0 75% 55%;
    --destructive-foreground: 0 0% 100%;
    --border: 220 10% 90%;
    --input: 220 15% 95%;
    --ring: 220 85% 58%;
    --gradient-medical: linear-gradient(
      135deg,
      hsl(210 90% 55%) 0%,
      hsl(140 65% 42%) 100%
    );
    --gradient-trust: linear-gradient(
      135deg,
      hsl(220 85% 50%) 0%,
      hsl(140 70% 40%) 100%
    );
    --gradient-hero-medical: linear-gradient(
      135deg,
      hsl(220 85% 58%) 0%,
      hsl(210 90% 55%) 50%,
      hsl(140 65% 42%) 100%
    );
    --gradient-purple: linear-gradient(
      135deg,
      #e6e6fa 0%,
      #dda0dd 50%,
      #da70d6 100%
    );
    --gradient-orange: linear-gradient(
      135deg,
      #fff8dc 0%,
      #ffe4b5 30%,
      #ffa500 70%,
      #ff8c00 100%
    );
    --gradient-blue: linear-gradient(
      135deg,
      #f0f8ff 0%,
      #b0e0e6 40%,
      #87ceeb 100%
    );
    --gradient-green: linear-gradient(
      135deg,
      #f0fff0 0%,
      #98fb98 50%,
      #90ee90 100%
    );
    --gradient-hero: linear-gradient(
      135deg,
      hsl(220 85% 58%) 0%,
      hsl(260 60% 50%) 100%
    );
    --gradient-soft: radial-gradient(
      ellipse at center,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(248, 248, 248, 0.8) 100%
    );
    --shadow-soft: 0 4px 20px rgba(34, 60, 120, 0.08);
    --shadow-medium: 0 8px 40px rgba(34, 60, 120, 0.12);
    --shadow-organic: 0 20px 60px rgba(34, 60, 120, 0.15);
    --transition-organic: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --radius: 0.75rem;
    --sidebar-background: 220 15% 98%;
    --sidebar-foreground: 220 20% 8%;
    --sidebar-primary: 220 85% 58%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 220 15% 95%;
    --sidebar-accent-foreground: 220 20% 8%;
    --sidebar-border: 220 10% 90%;
    --sidebar-ring: 220 85% 58%;
  }

  .dark {
    --background: 220 15% 8%;
    --foreground: 220 15% 95%;
    --card: 220 12% 12%;
    --card-foreground: 220 15% 95%;
    --popover: 220 12% 12%;
    --popover-foreground: 220 15% 95%;
    --brand-primary: 220 85% 68%;
    --brand-primary-light: 220 85% 78%;
    --brand-primary-lighter: 220 85% 25%;
    --brand-primary-dark: 220 85% 58%;
    --brand-primary-darker: 220 85% 45%;
    --primary: 220 85% 68%;
    --primary-foreground: 220 15% 8%;
    --primary-light: 220 85% 78%;
    --primary-lighter: 220 85% 25%;
    --primary-dark: 220 85% 58%;
    --secondary: 220 12% 15%;
    --secondary-foreground: 220 15% 95%;
    --muted: 220 12% 15%;
    --muted-foreground: 220 8% 65%;
    --accent: 260 60% 70%;
    --accent-foreground: 220 15% 8%;
    --accent-light: 260 60% 20%;
    --medical-critical: 0 75% 65%;
    --medical-critical-light: 0 75% 20%;
    --medical-warning: 45 100% 60%;
    --medical-warning-light: 45 100% 15%;
    --medical-success: 140 55% 55%;
    --medical-success-light: 140 55% 15%;
    --medical-info: 210 90% 65%;
    --medical-info-light: 210 90% 20%;
    --success: 140 55% 55%;
    --success-foreground: 220 15% 8%;
    --success-light: 140 55% 15%;
    --destructive: 0 75% 65%;
    --destructive-foreground: 220 15% 8%;
    --border: 220 12% 20%;
    --input: 220 12% 20%;
    --ring: 220 85% 68%;
    --gradient-medical: linear-gradient(
      135deg,
      hsl(210 90% 65%) 0%,
      hsl(140 55% 55%) 100%
    );
    --gradient-trust: linear-gradient(
      135deg,
      hsl(220 85% 68%) 0%,
      hsl(140 55% 55%) 100%
    );
    --gradient-hero-medical: linear-gradient(
      135deg,
      hsl(220 85% 68%) 0%,
      hsl(210 90% 65%) 50%,
      hsl(140 55% 55%) 100%
    );
    --gradient-purple: linear-gradient(
      135deg,
      #2d1b69 0%,
      #6a4c93 50%,
      #8b5a96 100%
    );
    --gradient-orange: linear-gradient(
      135deg,
      #4a3728 0%,
      #8b4513 30%,
      #cd853f 70%,
      #d2691e 100%
    );
    --gradient-blue: linear-gradient(
      135deg,
      #1c2331 0%,
      #2c4f70 40%,
      #4682b4 100%
    );
    --gradient-green: linear-gradient(
      135deg,
      #1c3a1c 0%,
      #2e5c2e 50%,
      #4a7c4a 100%
    );
    --gradient-hero: linear-gradient(
      135deg,
      hsl(220 85% 68%) 0%,
      hsl(260 60% 70%) 100%
    );
    --gradient-soft: radial-gradient(
      ellipse at center,
      rgba(24, 24, 24, 0.9) 0%,
      rgba(16, 16, 16, 0.8) 100%
    );
    --shadow-soft: 0 4px 20px rgba(0, 0, 0, 0.4);
    --shadow-medium: 0 8px 40px rgba(0, 0, 0, 0.5);
    --shadow-organic: 0 20px 60px rgba(0, 0, 0, 0.6);
    --sidebar-background: 220 15% 8%;
    --sidebar-foreground: 220 15% 95%;
    --sidebar-primary: 220 85% 68%;
    --sidebar-primary-foreground: 220 15% 8%;
    --sidebar-accent: 220 12% 15%;
    --sidebar-accent-foreground: 220 15% 95%;
    --sidebar-border: 220 12% 20%;
    --sidebar-ring: 220 85% 68%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family:
      "Inter",
      system-ui,
      -apple-system,
      BlinkMacSystemFont,
      "Segoe UI",
      sans-serif;
    font-feature-settings:
      "kern" 1,
      "liga" 1,
      "calt" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}
.organic-blob {
  position: absolute;
  border-radius: 50% 40% 60% 30%;
  filter: blur(40px);
  opacity: 0.15;
  animation: float 20s ease-in-out infinite;
}
.organic-blob:nth-child(2) {
  animation-delay: -5s;
}
.organic-blob:nth-child(3) {
  animation-delay: -10s;
}
.organic-blob:nth-child(4) {
  animation-delay: -15s;
}
@keyframes float {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  33% {
    transform: translateY(-30px) rotate(120deg);
  }
  66% {
    transform: translateY(30px) rotate(240deg);
  }
}

@layer components {
  .text-display-hero {
    @apply text-display-lg font-light tracking-tight text-foreground md:text-display-lg;
    font-variation-settings: "wght" 300;
  }
  .text-display-large {
    @apply text-display-md font-light tracking-tight text-foreground md:text-display-md;
    font-variation-settings: "wght" 300;
  }
  .text-display-small {
    @apply text-display-sm font-light tracking-tight text-foreground md:text-display-sm;
    font-variation-settings: "wght" 300;
    font-variation-settings: "wght" 300;
  }
  .text-heading-major {
    @apply text-heading-1 font-normal tracking-tight text-foreground md:text-heading-1;
    font-variation-settings: "wght" 400;
  }
  .text-heading-section {
    @apply text-heading-2 font-normal tracking-tight text-foreground md:text-heading-2;
    font-variation-settings: "wght" 400;
  }
  .text-heading-subsection {
    @apply text-heading-3 font-medium tracking-tight text-foreground md:text-heading-3;
    font-variation-settings: "wght" 500;
  }
  .text-heading-card {
    @apply text-heading-4 font-medium tracking-tight text-foreground md:text-heading-4;
    font-variation-settings: "wght" 500;
  }
  .text-heading-minor {
    @apply text-heading-5 font-medium tracking-tight text-foreground md:text-heading-5;
    font-variation-settings: "wght" 500;
  }
  .text-heading-small {
    @apply text-heading-6 font-semibold tracking-tight text-foreground md:text-heading-6;
    font-variation-settings: "wght" 600;
  }
  .text-body-lead {
    @apply text-body-lg font-normal leading-relaxed text-foreground md:text-body-lg;
    font-variation-settings: "wght" 400;
  }
  .text-body-large {
    @apply text-body-md font-normal leading-relaxed text-foreground md:text-body-md;
    font-variation-settings: "wght" 400;
  }
  .text-body-default {
    @apply text-body-sm font-normal leading-relaxed text-foreground md:text-body-sm;
    font-variation-settings: "wght" 400;
  }
  .text-body-small {
    @apply text-body-xs font-normal leading-relaxed text-muted-foreground md:text-body-xs;
    font-variation-settings: "wght" 400;
  }
  .text-caption {
    @apply text-caption font-medium uppercase tracking-wide text-muted-foreground;
    font-variation-settings: "wght" 500;
  }
  .text-caption-default {
    @apply text-caption font-normal text-muted-foreground;
    font-variation-settings: "wght" 400;
  }
  .text-nav-item {
    @apply text-body-xs font-medium text-muted-foreground hover:text-foreground md:text-body-sm;
    font-variation-settings: "wght" 500;
    transition: var(--transition-organic);
  }
  .text-button {
    @apply text-body-xs font-medium tracking-wide md:text-body-sm;
    font-variation-settings: "wght" 500;
  }
  .text-button-large {
    @apply text-body-sm font-medium tracking-wide md:text-body-md;
    font-variation-settings: "wght" 500;
  }
  .text-form-label {
    @apply text-body-xs font-medium text-foreground;
    font-variation-settings: "wght" 500;
  }
  .text-card-title {
    @apply text-heading-5 font-semibold text-foreground md:text-heading-4;
    font-variation-settings: "wght" 600;
  }
  .text-metric {
    @apply text-heading-2 font-bold tabular-nums text-primary md:text-heading-1;
    font-variation-settings: "wght" 700;
  }
  .text-metric-label {
    @apply text-body-xs font-medium uppercase tracking-wide text-muted-foreground;
    font-variation-settings: "wght" 500;
  }
  .text-medical-term {
    @apply text-body-sm font-medium tracking-wide text-foreground;
    font-variation-settings: "wght" 500;
  }
  .text-clinical-data {
    @apply font-mono text-body-sm tabular-nums text-foreground;
    font-family:
      "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas,
      "Courier New", monospace;
  }
  .text-medical-critical {
    color: hsl(var(--medical-critical));
  }
  .text-medical-warning {
    color: hsl(var(--medical-warning));
  }
  .text-medical-success {
    color: hsl(var(--medical-success));
  }
  .text-medical-info {
    color: hsl(var(--medical-info));
  }
  img,
  video,
  iframe,
  canvas,
  svg,
  object {
    max-width: 100%;
    height: auto;
    display: block;
  }
  *,
  *::before,
  *::after {
    box-sizing: border-box;
  }
  body {
    overflow-x: hidden;
  }
  @media (max-width: 600px) {
    html {
      font-size: 100%;
    }
  }
  @media (min-width: 601px) and (max-width: 1024px) {
    html {
      font-size: 100%;
    }
  }
  @media (min-width: 1025px) {
    html {
      font-size: 100%;
    }
  }
  @media (max-width: 640px) {
    .text-display-hero {
      @apply text-heading-1;
    }
    .text-display-large {
      @apply text-heading-2;
    }
    .text-heading-major {
      @apply text-heading-3;
    }
    .text-heading-section {
      @apply text-heading-4;
    }
  }
  @media (min-width: 1280px) {
    .text-display-hero {
      font-size: 7rem;
      line-height: 7.5rem;
    }
    .text-display-large {
      font-size: 6rem;
      line-height: 6.5rem;
    }
  }
  @media (prefers-reduced-motion: reduce) {
    .text-nav-item {
      transition: none;
    }
  }
  @media (prefers-contrast: high) {
    .text-muted-foreground {
      @apply text-foreground;
    }
  }
}
@layer components {
  .status-critical {
    @apply rounded-lg border border-medical-critical/20 bg-medical-critical/10 px-3 py-1 text-sm font-medium text-medical-critical;
  }
  .status-warning {
    @apply rounded-lg border border-medical-warning/20 bg-medical-warning/10 px-3 py-1 text-sm font-medium text-medical-warning;
  }
  .status-success {
    @apply rounded-lg border border-medical-success/20 bg-medical-success/10 px-3 py-1 text-sm font-medium text-medical-success;
  }
  .status-info {
    @apply rounded-lg border border-medical-info/20 bg-medical-info/10 px-3 py-1 text-sm font-medium text-medical-info;
  }
  .trust-badge {
    @apply rounded-full border border-trust-verified/20 bg-trust-verified/10 px-4 py-2 text-sm font-semibold text-trust-verified;
  }
}

@layer utilities {
  /* Optimized Typography Utilities */
  .text-display-hero {
    font-size: clamp(2.5rem, 5vw, 4rem);
    line-height: var(--leading-tight);
    font-weight: 300;
    letter-spacing: -0.02em;
  }

  .text-display-large {
    font-size: clamp(2rem, 4vw, 3rem);
    line-height: var(--leading-tight);
    font-weight: 300;
    letter-spacing: -0.01em;
  }

  .text-heading-1 {
    font-size: clamp(1.75rem, 3vw, 2.25rem);
    line-height: var(--leading-snug);
    font-weight: 600;
  }

  .text-heading-2 {
    font-size: clamp(1.5rem, 2.5vw, 1.875rem);
    line-height: var(--leading-snug);
    font-weight: 600;
  }

  .text-heading-3 {
    font-size: clamp(1.25rem, 2vw, 1.5rem);
    line-height: var(--leading-snug);
    font-weight: 600;
  }

  .text-body-large {
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    font-weight: 400;
  }

  .text-body-base {
    font-size: var(--text-base);
    line-height: var(--leading-normal);
    font-weight: 400;
  }

  .text-body-small {
    font-size: var(--text-sm);
    line-height: var(--leading-normal);
    font-weight: 400;
  }

  /* Spacing Utilities */
  .space-y-optimal > * + * {
    margin-top: var(--space-6);
  }

  .space-y-tight > * + * {
    margin-top: var(--space-4);
  }

  .space-y-loose > * + * {
    margin-top: var(--space-8);
  }

  /* Container Improvements */
  .container-optimized {
    max-width: 1200px;
    margin: 0 auto;
    padding-left: var(--space-6);
    padding-right: var(--space-6);
  }

  @media (max-width: 768px) {
    .container-optimized {
      padding-left: var(--space-4);
      padding-right: var(--space-4);
    }
  }
}

"use client";

import React, { Suspense, useEffect, useMemo, useRef, useState } from "react";
import { motion, useAnimation } from "framer-motion";
import { <PERSON><PERSON>, use<PERSON>rame, useThree } from "@react-three/fiber";
import { <PERSON><PERSON>, <PERSON><PERSON>he<PERSON>, Lock, Play, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import * as THREE from "three";

// Shader uniforms interface
interface ShaderUniforms {
  u_time: { value: number };
  u_resolution: { value: THREE.Vector2 };
  u_intensity: { value: number };
  u_flowSpeed: { value: number };
  u_noiseScale: { value: number };
  u_hueShift: { value: number };
  u_bloomBoost: { value: number };
  [uniform: string]: any;
}

// Aurora shader material
const auroraVertexShader = `
  varying vec2 vUv;
  
  void main() {
    vUv = uv;
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
  }
`;

const auroraFragmentShader = `
  uniform float u_time;
  uniform vec2 u_resolution;
  uniform float u_intensity;
  uniform float u_flowSpeed;
  uniform float u_noiseScale;
  uniform float u_hueShift;
  uniform float u_bloomBoost;
  
  varying vec2 vUv;

  // Simplex noise implementation
  vec3 mod289(vec3 x) { return x - floor(x * (1.0 / 289.0)) * 289.0; }
  vec2 mod289(vec2 x) { return x - floor(x * (1.0 / 289.0)) * 289.0; }
  vec3 permute(vec3 x) { return mod289(((x * 34.0) + 1.0) * x); }

  float snoise(vec2 v) {
    const vec4 C = vec4(0.211324865405187, 0.366025403784439, -0.577350269189626, 0.024390243902439);
    vec2 i = floor(v + dot(v, C.yy));
    vec2 x0 = v - i + dot(i, C.xx);
    vec2 i1;
    i1 = (x0.x > x0.y) ? vec2(1.0, 0.0) : vec2(0.0, 1.0);
    vec4 x12 = x0.xyxy + C.xxzz;
    x12.xy -= i1;
    i = mod289(i);
    vec3 p = permute(permute(i.y + vec3(0.0, i1.y, 1.0)) + i.x + vec3(0.0, i1.x, 1.0));
    vec3 m = max(0.5 - vec3(dot(x0, x0), dot(x12.xy, x12.xy), dot(x12.zw, x12.zw)), 0.0);
    m = m * m;
    m = m * m;
    vec3 x = 2.0 * fract(p * C.www) - 1.0;
    vec3 h = abs(x) - 0.5;
    vec3 ox = floor(x + 0.5);
    vec3 a0 = x - ox;
    m *= 1.79284291400159 - 0.85373472095314 * (a0 * a0 + h * h);
    vec3 g;
    g.x = a0.x * x0.x + h.x * x0.y;
    g.yz = a0.yz * x12.xz + h.yz * x12.yw;
    return 130.0 * dot(m, g);
  }

  // FBM (Fractal Brownian Motion)
  float fbm(vec2 st) {
    float value = 0.0;
    float amplitude = 0.5;
    float frequency = 0.0;
    
    for (int i = 0; i < 5; i++) {
      value += amplitude * snoise(st);
      st *= 2.0;
      amplitude *= 0.5;
    }
    return value;
  }

  void main() {
    vec2 st = vUv;
    vec2 resolution = u_resolution;
    
    // Aspect ratio correction
    st.x *= resolution.x / resolution.y;
    
    // Time-based movement
    float time = u_time * u_flowSpeed * 0.1;
    
    // Domain warping with multiple octaves
    vec2 warp1 = vec2(fbm(st * u_noiseScale + time), fbm(st * u_noiseScale + time + 100.0));
    vec2 warp2 = vec2(fbm(st * u_noiseScale * 0.7 + warp1 * 0.4 + time * 0.8), 
                      fbm(st * u_noiseScale * 0.7 + warp1 * 0.4 + time * 0.8 + 200.0));
    
    // Final field calculation
    float field = fbm(st * u_noiseScale * 0.5 + warp2 * 0.6 + time * 0.6);
    
    // Aurora colors (Cyan -> Blue -> Violet)
    vec3 color1 = vec3(0.133, 0.827, 0.933); // #22D3EE Cyan
    vec3 color2 = vec3(0.376, 0.647, 0.980); // #60A5FA Blue  
    vec3 color3 = vec3(0.655, 0.545, 0.980); // #A78BFA Violet
    
    // Hue shift
    float hue = field + u_hueShift;
    
    // Color mixing based on field value
    vec3 color;
    if (hue < 0.33) {
      color = mix(color1, color2, hue * 3.0);
    } else if (hue < 0.66) {
      color = mix(color2, color3, (hue - 0.33) * 3.0);
    } else {
      color = mix(color3, color1, (hue - 0.66) * 3.0);
    }
    
    // Apply intensity and bloom boost
    float intensity = (field * 0.5 + 0.5) * u_intensity;
    color *= intensity;
    color += vec3(u_bloomBoost);
    
    // Add slight blue noise dither to prevent banding
    float dither = (fract(sin(dot(vUv.xy, vec2(12.9898, 78.233))) * 43758.5453) - 0.5) * 0.01;
    color += vec3(dither);
    
    gl_FragColor = vec4(color, 1.0);
  }
`;

// Aurora background component
const AuroraBackground: React.FC<{
  intensity: number;
  flowSpeed: number;
  noiseScale: number;
  hueShift: number;
  bloomBoost: number;
}> = ({ intensity, flowSpeed, noiseScale, hueShift, bloomBoost }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const { size } = useThree();

  const uniforms = useMemo<ShaderUniforms>(
    () => ({
      u_time: { value: 0 },
      u_resolution: { value: new THREE.Vector2(size.width, size.height) },
      u_intensity: { value: intensity },
      u_flowSpeed: { value: flowSpeed },
      u_noiseScale: { value: noiseScale },
      u_hueShift: { value: hueShift },
      u_bloomBoost: { value: bloomBoost },
    }),
    [size, intensity, flowSpeed, noiseScale, hueShift, bloomBoost]
  );

  useFrame((state) => {
    if (meshRef.current) {
      uniforms.u_time.value = state.clock.elapsedTime;
      uniforms.u_resolution.value.set(size.width, size.height);
    }
  });

  // Update uniforms when props change
  useEffect(() => {
    uniforms.u_intensity.value = intensity;
    uniforms.u_flowSpeed.value = flowSpeed;
    uniforms.u_noiseScale.value = noiseScale;
    uniforms.u_hueShift.value = hueShift;
    uniforms.u_bloomBoost.value = bloomBoost;
  }, [uniforms, intensity, flowSpeed, noiseScale, hueShift, bloomBoost]);

  return (
    <mesh ref={meshRef} scale={[2, 2, 1]}>
      <planeGeometry args={[2, 2]} />
      <shaderMaterial
        vertexShader={auroraVertexShader}
        fragmentShader={auroraFragmentShader}
        uniforms={uniforms}
        transparent
      />
    </mesh>
  );
};

// Component interfaces
interface EntheoryHeroShaderProps {
  poster?: string;
  videoWebm?: string;
  videoMp4?: string;
  intensity?: number;
  flowSpeed?: number;
  noiseScale?: number;
  hueShift?: number;
  bloomBoost?: number;
}

interface PresetConfig {
  intensity: number;
  flowSpeed: number;
  bloomBoost: number;
}

const PRESETS: Record<string, PresetConfig> = {
  "calm-clinic": {
    intensity: 0.95,
    flowSpeed: 0.5,
    bloomBoost: 0.12,
  },
  "conference-stage": {
    intensity: 1.2,
    flowSpeed: 0.65,
    bloomBoost: 0.22,
  },
};

// Animation sequences (0-12s loop) - Problem → Solution narrative
const ANIMATION_PHASES = [
  { start: 0.0, end: 1.6, name: "problems" }, // Show fragmented workflow challenges
  { start: 1.6, end: 2.4, name: "consent" }, // ABDM consent verification
  { start: 2.4, end: 3.8, name: "ingest" }, // Ingest EMR, LIS, PACS
  { start: 3.8, end: 4.8, name: "normalize" }, // FHIR normalization
  { start: 4.8, end: 6.0, name: "unify" }, // Patient OS unification
  { start: 6.0, end: 7.8, name: "answer" }, // AI summary generation
  { start: 7.8, end: 8.8, name: "safety" }, // Safety flag detection
  { start: 8.8, end: 10.2, name: "plan" }, // Tumor board plan
  { start: 10.2, end: 12.0, name: "reset" }, // Reset for seamless loop
];

const EntheoryHeroShader: React.FC<EntheoryHeroShaderProps> = ({
  poster = "/media/hero-poster.webp",
  videoWebm = "/media/hero-loop.webm",
  videoMp4 = "/media/hero-loop.mp4",
  intensity = 1.0,
  flowSpeed = 0.55,
  noiseScale = 0.9,
  hueShift = 0.06,
  bloomBoost = 0.18,
}) => {
  console.log("EntheoryHeroShader: Component mounting with props:", {
    intensity,
    flowSpeed,
    noiseScale,
    hueShift,
    bloomBoost,
  });

  const [isClient, setIsClient] = useState(false);
  const [reducedMotion, setReducedMotion] = useState(false);
  const [currentPhase, setCurrentPhase] = useState(0);
  const [animationTime, setAnimationTime] = useState(0);

  const controls = useAnimation();

  // Detect client-side and reduced motion
  useEffect(() => {
    console.log("EntheoryHeroShader: Setting up client-side detection");
    setIsClient(true);
    const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
    setReducedMotion(mediaQuery.matches);
    console.log(
      "EntheoryHeroShader: Reduced motion detected:",
      mediaQuery.matches
    );

    const handleChange = (e: MediaQueryListEvent) =>
      setReducedMotion(e.matches);
    mediaQuery.addEventListener("change", handleChange);
    return () => mediaQuery.removeEventListener("change", handleChange);
  }, []);

  // Animation loop timer
  useEffect(() => {
    if (reducedMotion) return;

    const interval = setInterval(() => {
      setAnimationTime((prev) => {
        const newTime = (prev + 0.1) % 12; // 12-second loop

        // Determine current phase
        const phase = ANIMATION_PHASES.findIndex(
          (p) => newTime >= p.start && newTime < p.end
        );
        setCurrentPhase(phase >= 0 ? phase : 0);

        return newTime;
      });
    }, 100);

    return () => clearInterval(interval);
  }, [reducedMotion]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.08,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 40 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  // Animation variants for problem → solution narrative
  const phaseVariants = {
    problems: {
      initial: { opacity: 0, y: 10, scale: 0.9 },
      visible: { opacity: 1, y: 0, scale: 1 },
      dimmed: { opacity: 0.6, scale: 0.98, y: 4 },
    },
    consent: {
      initial: { opacity: 0, x: 50, scale: 0.96 },
      visible: { opacity: 1, x: 0, scale: 1 },
      dimmed: { opacity: 0.55, scale: 0.98, y: 4 },
    },
    ingest: {
      initial: { opacity: 0, x: 16, y: 16, scale: 0.9 },
      visible: { opacity: 1, x: 0, y: 0, scale: 1 },
      dimmed: { opacity: 0.6, scale: 0.98, y: 4 },
    },
    normalize: {
      initial: { opacity: 0, scaleX: 0, x: -20 },
      visible: { opacity: 1, scaleX: 1, x: 0 },
      dimmed: { opacity: 0.6, scale: 0.98 },
    },
    unify: {
      initial: { scale: 0.96, opacity: 0.8 },
      visible: { scale: 1.0, opacity: 1 },
      dimmed: { opacity: 0.7, scale: 0.98 },
    },
    answer: {
      initial: { opacity: 0, scaleX: 0 },
      visible: { opacity: 1, scaleX: 1 },
      dimmed: { opacity: 0.85, scale: 0.99 },
    },
    safety: {
      initial: { opacity: 0, y: -10, scale: 0.9 },
      visible: { opacity: 1, y: 0, scale: 1 },
      dimmed: { opacity: 0.85, scale: 0.99 },
    },
    plan: {
      initial: { opacity: 0, x: 12, scale: 0.9 },
      visible: { opacity: 1, x: 0, scale: 1 },
      dimmed: { opacity: 0.85, scale: 0.99 },
    },
  };

  // Phase transition easing
  const phaseTransition = {
    duration: 0.7,
  };

  // Typing effect for AI summary
  const [typedText, setTypedText] = useState("");
  const summaryText =
    "ER+/PR+, HER2–. Stage IIA (T2N0M0). Recommend endocrine therapy + CDK4/6 inhibitor.";

  useEffect(() => {
    if (currentPhase === 5) {
      // Answer phase (index 5, timing 6.0-7.8s)
      const timer = setTimeout(() => {
        const progress = Math.min((animationTime - 6.0) / 1.8, 1); // 1.8s to type
        const chars = summaryText.slice(
          0,
          Math.floor(progress * summaryText.length)
        );
        setTypedText(chars);
      }, 200);
      return () => clearTimeout(timer);
    } else {
      setTypedText("");
    }
  }, [currentPhase, animationTime]);

  // Helper to determine visibility based on phase
  const getPhaseVisibility = (elementPhase: number) => {
    console.log(
      `Phase check: current=${currentPhase}, element=${elementPhase}`
    );
    if (currentPhase === elementPhase) return "visible";
    if (currentPhase > elementPhase) return "dimmed";
    return "initial";
  };

  // Helper to determine if element should be rendered at all
  const shouldRenderElement = (elementPhase: number) => {
    return currentPhase >= elementPhase;
  };

  const badges = [
    { icon: Cpu, text: "FHIR-native" },
    { icon: ShieldCheck, text: "ABDM-ready" },
    { icon: Lock, text: "PHI-first security" },
  ];

  if (!isClient) {
    console.log("EntheoryHeroShader: Not client-side yet, returning null");
    return null;
  }

  console.log(
    "EntheoryHeroShader: Rendering component. Current phase:",
    currentPhase,
    "Animation time:",
    animationTime
  );

  return (
    <section className="relative min-h-screen overflow-hidden bg-gradient-to-br from-neutral-900 via-neutral-800 to-neutral-900">
      {/* Background Layer */}
      <div className="absolute inset-0">
        {reducedMotion ? (
          // Fallback for reduced motion
          <div className="absolute inset-0 bg-gradient-to-br from-cyan-900/20 via-blue-900/20 to-violet-900/20" />
        ) : (
          // Three.js Aurora Background
          <Canvas
            className="absolute inset-0"
            camera={{ position: [0, 0, 1], fov: 75 }}
            dpr={Math.min(window.devicePixelRatio, 1.5)}
          >
            <Suspense fallback={null}>
              <AuroraBackground
                intensity={intensity}
                flowSpeed={flowSpeed}
                noiseScale={noiseScale}
                hueShift={hueShift}
                bloomBoost={bloomBoost}
              />
            </Suspense>
          </Canvas>
        )}

        {/* Subtle grid overlay */}
        <div className="absolute inset-0 opacity-5">
          <div className="grid h-full grid-cols-12 grid-rows-8">
            {Array.from({ length: 96 }).map((_, i) => (
              <div key={i} className="border border-white/10" />
            ))}
          </div>
        </div>

        {/* Linear scrim for text readability */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-black/20" />
      </div>

      {/* Content Overlay */}
      <div className="relative z-10 flex min-h-screen items-center">
        <div className="container mx-auto px-6 lg:px-8">
          <motion.div
            className="grid items-center gap-12 lg:grid-cols-2 lg:gap-16"
            initial="hidden"
            animate="visible"
            variants={containerVariants}
          >
            {/* Left Column - Content */}
            <div className="space-y-8">
              <motion.div variants={itemVariants}>
                <h1 className="text-6xl font-light leading-tight tracking-tight text-white lg:text-7xl xl:text-8xl">
                  Oncology,{" "}
                  <span className="bg-gradient-to-r from-cyan-400 via-blue-400 to-violet-400 bg-clip-text text-transparent">
                    unified
                  </span>{" "}
                  in 60 seconds
                </h1>
              </motion.div>

              <motion.p
                className="max-w-2xl text-xl font-light leading-relaxed text-neutral-300 lg:text-2xl"
                variants={itemVariants}
              >
                Unifies EMR, PACS, and LIS into an ABDM-compliant Patient OS
                with AI summaries.
              </motion.p>

              {/* CTAs */}
              <motion.div
                className="flex flex-col gap-4 sm:flex-row"
                variants={itemVariants}
              >
                <Button
                  size="lg"
                  className="border-0 bg-gradient-to-r from-cyan-500 to-blue-500 text-white shadow-lg transition-all duration-300 hover:from-cyan-600 hover:to-blue-600 hover:shadow-xl"
                >
                  Book a demo
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="border-white/20 text-white hover:border-white/40 hover:bg-white/10"
                >
                  <Play className="mr-2 h-4 w-4" />
                  Watch prototype
                </Button>
              </motion.div>

              {/* Badges */}
              <motion.div
                className="flex flex-wrap gap-3"
                variants={itemVariants}
              >
                {badges.map((badge, index) => (
                  <motion.div
                    key={badge.text}
                    variants={{
                      hidden: { opacity: 0, scale: 0.8 },
                      visible: {
                        opacity: 1,
                        scale: 1,
                        transition: { delay: index * 0.06 },
                      },
                    }}
                  >
                    <Badge
                      variant="outline"
                      className="border-white/20 bg-white/5 px-3 py-1.5 text-white backdrop-blur-sm"
                    >
                      <badge.icon className="mr-2 h-3 w-3" />
                      {badge.text}
                    </Badge>
                  </motion.div>
                ))}
              </motion.div>
            </div>

            {/* Right Column - Problem → Solution Narrative */}
            <div className="relative space-y-4">
              {/* Phase 0: Problem Context (0.0-1.6s) */}
              {shouldRenderElement(0) && (
                <>
                  <motion.div
                    className="absolute rounded-lg border border-red-400/20 bg-red-500/10 p-3 text-white backdrop-blur-sm"
                    animate={phaseVariants.problems[getPhaseVisibility(0)]}
                    transition={{ duration: 0.5, delay: 0.0 }}
                    style={{
                      filter:
                        currentPhase !== 0 && currentPhase > 0
                          ? "blur(2px)"
                          : "none",
                      top: "10%",
                      left: "15%",
                      width: "180px",
                    }}
                  >
                    <div className="text-xs font-medium text-red-300">
                      Problem
                    </div>
                    <div className="mt-1 text-xs text-neutral-300">
                      Siloed systems (EMR/LIS/PACS)
                    </div>
                  </motion.div>

                  <motion.div
                    className="absolute rounded-lg border border-orange-400/20 bg-orange-500/10 p-3 text-white backdrop-blur-sm"
                    animate={phaseVariants.problems[getPhaseVisibility(0)]}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    style={{
                      filter:
                        currentPhase !== 0 && currentPhase > 0
                          ? "blur(2px)"
                          : "none",
                      top: "35%",
                      right: "20%",
                      width: "180px",
                    }}
                  >
                    <div className="text-xs font-medium text-orange-300">
                      Problem
                    </div>
                    <div className="mt-1 text-xs text-neutral-300">
                      Unstructured PDFs & jargon (ABX?)
                    </div>
                  </motion.div>

                  <motion.div
                    className="absolute rounded-lg border border-yellow-400/20 bg-yellow-500/10 p-3 text-white backdrop-blur-sm"
                    animate={phaseVariants.problems[getPhaseVisibility(0)]}
                    transition={{ duration: 0.6, delay: 0.9 }}
                    style={{
                      filter:
                        currentPhase !== 0 && currentPhase > 0
                          ? "blur(2px)"
                          : "none",
                      top: "60%",
                      left: "25%",
                      width: "180px",
                    }}
                  >
                    <div className="text-xs font-medium text-yellow-300">
                      Problem
                    </div>
                    <div className="mt-1 text-xs text-neutral-300">
                      Consent & compliance friction
                    </div>
                  </motion.div>
                </>
              )}

              {/* Phase 1: Consent Card (1.6-2.4s) */}
              {shouldRenderElement(1) && (
                <motion.div
                  className="rounded-xl border border-white/20 bg-white/10 p-4 text-white backdrop-blur-sm"
                  animate={phaseVariants.consent[getPhaseVisibility(1)]}
                  transition={phaseTransition}
                  style={{
                    filter:
                      currentPhase !== 1 && currentPhase > 1
                        ? "blur(2px)"
                        : "none",
                    position: "absolute",
                    top: "10%",
                    right: "10%",
                    width: "200px",
                  }}
                >
                  <div className="mb-2 flex items-center gap-2">
                    <ShieldCheck className="h-4 w-4 text-green-400" />
                    <div className="text-sm font-medium">ABDM Consent</div>
                  </div>
                  <div className="text-xs text-neutral-300">
                    Patient consent verified
                  </div>
                </motion.div>
              )}

              {/* Phase 2: Ingest Tiles (2.4-3.8s) */}
              {shouldRenderElement(2) &&
                ["EMR", "PACS", "LIS"].map((system, index) => (
                  <motion.div
                    key={system}
                    className="absolute rounded-xl border border-white/20 bg-white/10 p-4 text-white backdrop-blur-sm"
                    animate={phaseVariants.ingest[getPhaseVisibility(2)]}
                    transition={{
                      ...phaseTransition,
                      delay: index * 0.18, // Sequential ingestion
                    }}
                    style={{
                      filter:
                        currentPhase !== 2 && currentPhase > 2
                          ? "blur(2px)"
                          : "none",
                      width: "100px",
                      height: "80px",
                      top: `${20 + index * 25}%`,
                      left: `${10 + index * 15}%`,
                    }}
                  >
                    <div className="text-sm font-medium">{system}</div>
                    <div className="mt-1 text-xs text-neutral-300">
                      {system === "EMR" && "Records"}
                      {system === "PACS" && "Imaging"}
                      {system === "LIS" && "Labs"}
                    </div>
                  </motion.div>
                ))}

              {/* Phase 3: FHIR Mapper Bar (3.8-4.8s) */}
              {shouldRenderElement(3) && (
                <motion.div
                  className="absolute rounded-lg border border-blue-400/30 bg-blue-500/20 p-3 text-white backdrop-blur-sm"
                  animate={phaseVariants.normalize[getPhaseVisibility(3)]}
                  transition={phaseTransition}
                  style={{
                    filter:
                      currentPhase !== 3 && currentPhase > 3
                        ? "blur(2px)"
                        : "none",
                    top: "60%",
                    left: "5%",
                    width: "280px",
                  }}
                >
                  <div className="mb-2 text-xs font-medium text-blue-300">
                    FHIR Resources Created
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {[
                      "Patient",
                      "Condition",
                      "Observation",
                      "DiagnosticReport",
                      "ImagingStudy",
                      "Medication",
                      "Procedure",
                    ].map((resource, index) => (
                      <motion.span
                        key={resource}
                        className="rounded bg-blue-400/20 px-2 py-1 text-xs"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.08 }}
                      >
                        {resource}
                      </motion.span>
                    ))}
                  </div>
                </motion.div>
              )}

              {/* Phase 4: Patient OS Browser Frame (4.8-6.0s) */}
              {shouldRenderElement(4) && (
                <motion.div
                  className="ml-auto max-w-md rounded-xl border border-white/20 bg-white/10 p-6 text-white backdrop-blur-sm"
                  animate={phaseVariants.unify[getPhaseVisibility(4)]}
                  transition={phaseTransition}
                  style={{
                    filter:
                      currentPhase !== 4 && currentPhase > 4
                        ? "blur(2px)"
                        : "none",
                  }}
                >
                  <div className="mb-4 flex items-center gap-2">
                    <div className="h-3 w-3 rounded-full bg-red-400" />
                    <div className="h-3 w-3 rounded-full bg-yellow-400" />
                    <div className="h-3 w-3 rounded-full bg-green-400" />
                    <div className="ml-2 text-sm text-neutral-300">
                      Patient OS — Tumor Board View
                    </div>
                  </div>

                  {/* Mock UI Elements */}
                  <div className="grid grid-cols-3 gap-2">
                    <div className="rounded bg-white/5 p-2 text-xs">
                      <div className="text-cyan-300">EMR</div>
                      <div className="text-neutral-400">Records</div>
                    </div>
                    <div className="rounded bg-white/5 p-2 text-xs">
                      <div className="text-blue-300">Labs (LIS)</div>
                      <div className="text-neutral-400">Results</div>
                    </div>
                    <div className="rounded bg-white/5 p-2 text-xs">
                      <div className="text-violet-300">Imaging (PACS)</div>
                      <div className="text-neutral-400">Scans</div>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Phase 5: AI Summary (6.0-7.8s) */}
              {shouldRenderElement(5) && (
                <motion.div
                  className="absolute rounded-lg border border-cyan-400/30 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 p-3"
                  animate={phaseVariants.answer[getPhaseVisibility(5)]}
                  transition={phaseTransition}
                  style={{
                    filter:
                      currentPhase !== 5 && currentPhase > 5
                        ? "blur(2px)"
                        : "none",
                    top: "40%",
                    right: "5%",
                    width: "320px",
                  }}
                >
                  <div className="mb-1 text-xs font-medium text-cyan-300">
                    AI Clinical Summary
                  </div>
                  <div className="text-sm leading-relaxed text-white">
                    {typedText}
                    {currentPhase === 5 && animationTime < 7.8 && (
                      <span className="animate-pulse">|</span>
                    )}
                  </div>
                </motion.div>
              )}

              {/* Phase 6: Safety Flag (7.8-8.8s) */}
              {shouldRenderElement(6) && (
                <motion.div
                  className="absolute rounded-lg border border-yellow-400/30 bg-yellow-500/20 p-3 text-white backdrop-blur-sm"
                  animate={phaseVariants.safety[getPhaseVisibility(6)]}
                  transition={phaseTransition}
                  style={{
                    filter:
                      currentPhase !== 6 && currentPhase > 6
                        ? "blur(2px)"
                        : "none",
                    top: "70%",
                    right: "15%",
                    width: "260px",
                  }}
                >
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 animate-pulse rounded-full bg-yellow-400" />
                    <div className="text-xs font-medium text-yellow-300">
                      Safety Alert
                    </div>
                  </div>
                  <div className="mt-1 text-xs text-neutral-300">
                    ⚠ Drug–drug: warfarin interaction
                  </div>
                </motion.div>
              )}

              {/* Phase 7: Tumor Board Plan (8.8-10.2s) */}
              {shouldRenderElement(7) && (
                <motion.div
                  className="absolute rounded-lg border border-green-400/30 bg-green-500/20 p-3 text-white backdrop-blur-sm"
                  animate={phaseVariants.plan[getPhaseVisibility(7)]}
                  transition={phaseTransition}
                  style={{
                    filter:
                      currentPhase !== 7 && currentPhase > 7
                        ? "blur(2px)"
                        : "none",
                    top: "80%",
                    right: "5%",
                    width: "300px",
                  }}
                >
                  <div className="mb-1 flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-400" />
                    <div className="text-xs font-medium text-green-300">
                      Tumor Board Plan
                    </div>
                  </div>
                  <div className="text-xs text-neutral-300">
                    Planned: Endocrine + CDK4/6 • Genomic assay consideration
                  </div>
                </motion.div>
              )}

              {/* Mobile Frame - Always visible */}
              <motion.div
                className="absolute left-0 top-1/2 h-56 w-32 -translate-y-1/2 rounded-2xl border border-white/20 bg-white/10 p-3 text-white backdrop-blur-sm"
                variants={itemVariants}
                whileHover={{ scale: 1.05, y: -4 }}
                transition={{ duration: 0.2 }}
              >
                <div className="mb-2 text-xs font-medium">Patient Glance</div>
                <div className="space-y-2">
                  <div className="h-2 w-full rounded bg-white/20" />
                  <div className="h-2 w-3/4 rounded bg-white/15" />
                  <div className="h-2 w-1/2 rounded bg-white/10" />
                  <div className="mt-3 rounded bg-gradient-to-r from-cyan-500/20 to-blue-500/20 p-2 text-xs">
                    Status: Stable
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default EntheoryHeroShader;

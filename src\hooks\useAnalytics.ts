/**
 * Analytics Hook
 *
 * Provides analytics tracking functionality with fallback to console logging
 * in development mode. Safely handles missing analytics providers.
 */

export type TrackEventFn = (
  eventName: string,
  props?: Record<string, unknown>
) => void;

interface AnalyticsWindow extends Window {
  analytics?: {
    track?: (eventName: string, props?: Record<string, unknown>) => void;
  };
}

export const useAnalytics = (): { trackEvent: TrackEventFn } => {
  const trackEvent: TrackEventFn = (eventName, props) => {
    try {
      if (typeof window !== "undefined") {
        const analyticsWindow = window as AnalyticsWindow;
        if (
          analyticsWindow.analytics &&
          typeof analyticsWindow.analytics.track === "function"
        ) {
          analyticsWindow.analytics.track(eventName, props || {});
          return;
        }
        if (import.meta.env?.DEV) {
          console.debug("[analytics]", eventName, props || {});
        }
      }
    } catch {
      // swallow
    }
  };

  return { trackEvent };
};

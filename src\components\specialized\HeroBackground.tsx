import React, { memo, useCallback, useState } from "react";

/**
 * Hero Background Component
 *
 * Optimized responsive background image component for the hero section.
 * Features performance optimizations, error handling, and accessibility improvements.
 */

interface HeroBackgroundProps {
  /** Optional CSS class name */
  className?: string;
  /** Loading priority for performance optimization */
  priority?: boolean;
  /** Callback when image loads successfully */
  onLoad?: () => void;
  /** Callback when image fails to load */
  onError?: () => void;
}

const HeroBackground: React.FC<HeroBackgroundProps> = memo(
  ({ className = "", priority = true, onLoad, onError }) => {
    const [imageLoaded, setImageLoaded] = useState(false);
    const [imageError, setImageError] = useState(false);

    const handleImageLoad = useCallback(() => {
      setImageLoaded(true);
      onLoad?.();
    }, [onLoad]);

    const handleImageError = useCallback(() => {
      setImageError(true);
      onError?.();
    }, [onError]);

    return (
      <div
        className={`pointer-events-none absolute inset-0 z-0 overflow-hidden ${className}`}
      >
        {/* Fallback gradient background */}
        <div
          className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-slate-100"
          aria-hidden="true"
        />

        {/* Optimized responsive picture element */}
        <picture aria-hidden="true" className="absolute inset-0">
          <source
            media="(max-width: 768px)"
            srcSet="/media/hero/hero-bg-mobile.png"
            type="image/png"
          />
          <source
            media="(min-width: 769px)"
            srcSet="/media/hero/hero-bg-desktop.png"
            type="image/png"
          />
          <img
            src="/media/hero/hero-bg-desktop.png"
            alt=""
            className={`h-full w-full object-cover transition-opacity duration-300 ${
              imageLoaded ? "opacity-100" : "opacity-0"
            }`}
            style={{
              display: "block",
              willChange: imageLoaded ? "auto" : "opacity",
            }}
            loading={priority ? "eager" : "lazy"}
            decoding="async"
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        </picture>

        {/* Loading state indicator */}
        {!imageLoaded && !imageError && (
          <div
            className="absolute inset-0 flex items-center justify-center bg-slate-50/50"
            aria-hidden="true"
          >
            <div className="h-8 w-8 animate-spin rounded-full border-2 border-slate-300 border-t-slate-600" />
          </div>
        )}

        {/* Error state fallback */}
        {imageError && (
          <div
            className="absolute inset-0 bg-gradient-to-br from-slate-100 via-slate-50 to-white"
            aria-hidden="true"
          />
        )}
      </div>
    );
  }
);

HeroBackground.displayName = "HeroBackground";

export default HeroBackground;

import React from "react";

/**
 * Hero Background Component
 *
 * Responsive background image component for the hero section.
 * Automatically switches between mobile and desktop images based on screen size.
 */
const HeroBackground: React.FC = () => {
  return (
    <div className="pointer-events-none absolute inset-0 z-0 overflow-hidden">
      <picture aria-hidden className="absolute inset-0">
        <source
          media="(max-width: 768px)"
          srcSet="/media/hero/hero-bg-mobile.png"
        />
        <img
          src="/media/hero/hero-bg-desktop.png"
          alt=""
          className="h-full w-full object-cover"
          style={{ display: "block" }}
        />
      </picture>
    </div>
  );
};

export default HeroBackground;

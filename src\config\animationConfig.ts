// Central configuration for all animation timings and settings

export const ANIMATION_CONFIG = {
  // Global settings
  global: {
    defaultDuration: 0.8,
    defaultEase: "power2.inOut",
    scrollScrub: 1,
    reducedMotionDuration: 0.01,
  },

  // Page entry animations
  pageEntry: {
    headerDelay: 0,
    heroDelay: 0.2,
    heroTextDelay: 0.4,
    ctaDelay: 0.6,
    duration: 0.8,
  },

  // Section transitions
  transitions: {
    heroToAura: {
      duration: 1.2,
      overlap: 0.3,
      fadeStart: 0.7,
    },
    auraToSection3: {
      duration: 1.0,
      overlap: 0.4,
      moduleStagger: 0.1,
    },
    section3To4: {
      duration: 1.0,
      overlap: 0.3,
      cloudFormation: 0.2,
    },
    section4To5: {
      duration: 0.8,
      overlap: 0.25,
      frameTransition: 0.25,
    },
    section5ToCTA: {
      duration: 0.6,
      overlap: 0.2,
    },
  },

  // AuraStack specific
  auraStack: {
    plateDuration: 0.15,
    plateStagger: 0.1,
    bounceEase: "elastic.out(1, 0.5)",
    scrollDuration: "400%",
    pinSpacing: true,
  },

  // Section 4 (Cloud/Mycelium)
  section4: {
    cloudFormation: {
      duration: 0.2,
      fromScale: 0.7,
      blur: 20,
    },
    threadGrowth: {
      duration: 0.3,
      stagger: 0.05,
      ease: "power2.inOut",
    },
    badgeEntry: {
      duration: 0.2,
      stagger: 0.05,
      fromY: 20,
      ease: "back.out(1.7)",
    },
    exit: {
      duration: 0.2,
      toScale: 0.95,
    },
  },

  // Section 5 (4 Frames)
  section5: {
    scrollDuration: "300%",
    frameTransition: {
      duration: 0.25,
      fadeOverlap: 0.1,
      fromScale: 1.02,
      toScale: 0.98,
      fromY: 20,
      toY: -20,
    },
    overlays: {
      fadeInDelay: 0.1,
      fadeOutEarly: 0.1,
      duration: 0.15,
    },
    ctaTrigger: {
      progress: 0.9,
      delay: 2200,
    },
    emblemGlow: {
      duration: 4,
      minScale: 1,
      maxScale: 1.1,
    },
  },

  // Loading states
  loading: {
    minimumDuration: 1500,
    fadeOutDuration: 0.5,
    skeletonPulse: 2,
  },

  // Responsive breakpoints
  responsive: {
    mobile: {
      durationMultiplier: 1.2,
      reducedStagger: 0.05,
      simplifiedTransitions: true,
    },
    tablet: {
      durationMultiplier: 1.1,
      reducedStagger: 0.08,
    },
  },

  // Performance settings
  performance: {
    willChange: ["transform", "opacity"],
    gpuAcceleration: true,
    throttleScroll: 16, // ~60fps
    debounceResize: 150,
  },
};

// Helper function to get responsive duration
export const getResponsiveDuration = (baseDuration: number): number => {
  if (typeof window === "undefined") return baseDuration;

  const width = window.innerWidth;
  if (width < 640) {
    return baseDuration * ANIMATION_CONFIG.responsive.mobile.durationMultiplier;
  } else if (width < 1024) {
    return baseDuration * ANIMATION_CONFIG.responsive.tablet.durationMultiplier;
  }
  return baseDuration;
};

// Helper function to check if reduced motion is preferred
export const prefersReducedMotion = (): boolean => {
  if (typeof window === "undefined") return false;
  return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
};

// Get animation settings based on user preference
export const getAnimationSettings = (settings: any) => {
  if (prefersReducedMotion()) {
    return {
      ...settings,
      duration: ANIMATION_CONFIG.global.reducedMotionDuration,
      stagger: 0,
      delay: 0,
    };
  }
  return settings;
};

// Easing functions
export const EASINGS = {
  // Power easings
  power1: "power1.inOut",
  power2: "power2.inOut",
  power3: "power3.inOut",
  power4: "power4.inOut",

  // Back easings (overshooting)
  backIn: "back.in(1.7)",
  backOut: "back.out(1.7)",
  backInOut: "back.inOut(1.7)",

  // Elastic easings (bouncy)
  elasticOut: "elastic.out(1, 0.5)",
  elasticIn: "elastic.in(1, 0.5)",

  // Expo easings (dramatic)
  expoIn: "expo.in",
  expoOut: "expo.out",
  expoInOut: "expo.inOut",

  // Circ easings (circular)
  circIn: "circ.in",
  circOut: "circ.out",
  circInOut: "circ.inOut",

  // Linear (no easing)
  none: "none",
  linear: "linear",
};

export default ANIMATION_CONFIG;

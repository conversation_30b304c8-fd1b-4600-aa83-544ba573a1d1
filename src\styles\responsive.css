@media (max-width: 768px) {
  #hero {
    --hero-axis-x: 50vw !important;
    --card-w: min(90vw, 350px) !important;
    --branch-offset: 60px !important;
    --left-card-offset: -100px !important;
    --flow-h: 60vh !important;
  }
  .container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
  .flow-hero-card {
    width: 100% !important;
    max-width: 90vw !important;
  }
  .aura-stack-section {
    height: auto !important;
    min-height: 100vh;
  }
  .will-change-transform-opacity {
    will-change: auto !important;
  }
  .frame-container {
    padding: 1rem !important;
  }
  [data-section] {
    transform: translateY(20px) !important;
  }
  .overlay-animation {
    display: none !important;
  }
  .text-display-hero {
    font-size: clamp(2rem, 6vw, 2.5rem) !important;
  }
  .text-display-large {
    font-size: clamp(1.75rem, 5vw, 2rem) !important;
  }
  .text-heading-1 {
    font-size: clamp(1.5rem, 4vw, 1.75rem) !important;
  }
  .text-heading-2 {
    font-size: clamp(1.25rem, 3vw, 1.5rem) !important;
  }
  .text-body-large {
    font-size: 1rem !important;
  }
  .grid.lg\:grid-cols-2 {
    grid-template-columns: 1fr !important;
    gap: 2rem !important;
  }
  [aria-hidden="true"]:has(svg) {
    height: 20vh !important;
  }
  .ScrollTrigger-pin {
    position: relative !important;
  }
  .container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
  .container-optimized {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
  @media (max-width: 480px) {
    #hero {
      --flow-h: 50vh !important;
    }
    .oncology-flow-section {
      display: none !important;
    }
    .text-display-hero {
      font-size: 1.875rem !important;
    }
    .text-display-large {
      font-size: 1.5rem !important;
    }
  }
}
@media (min-width: 641px) and (max-width: 1024px) {
  .aura-stack-section {
    min-height: 80vh;
  }
  .overlay-animation {
    opacity: 0.6 !important;
  }
  .text-display-hero {
    font-size: clamp(2.25rem, 7vw, 3rem) !important;
  }
  .text-display-large {
    font-size: clamp(2rem, 6vw, 2.5rem) !important;
  }
  .frame-container {
    padding: 2rem !important;
  }
  [aria-hidden="true"]:has(svg) {
    height: 30vh !important;
  }
}
@media (hover: none) and (pointer: coarse) {
  *:hover {
    transform: none !important;
  }
  button,
  a,
  [role="button"] {
    min-height: 44px;
    min-width: 44px;
    padding: 0.75rem 1rem;
  }
  .parallax {
    transform: none !important;
  }
  [data-scroll-trigger] {
    animation-duration: 0.3s !important;
  }
  input,
  select,
  textarea {
    font-size: 16px !important;
  }
  body {
    -webkit-overflow-scrolling: touch;
  }
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .lazyload {
    transform: translateZ(0);
    backface-visibility: hidden;
  }
}
@media (max-width: 896px) and (orientation: landscape) {
  .min-h-screen {
    min-height: 100vw !important;
  }
  .py-24 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .landscape\:hidden {
    display: none !important;
  }
}
@media print {
  .will-change-transform,
  .will-change-opacity,
  .overlay-animation,
  button,
  input,
  video,
  [role="progressbar"] {
    display: none !important;
  }
  * {
    opacity: 1 !important;
    visibility: visible !important;
    transform: none !important;
  }
  body {
    font-size: 12pt !important;
    color: black !important;
    background: white !important;
  }
}
@media (prefers-contrast: high) {
  * {
    border-color: currentColor !important;
  }
  button,
  a {
    text-decoration: underline !important;
  }
  .text-gray-600,
  .text-slate-600 {
    color: #333 !important;
  }
}
@media (prefers-color-scheme: dark) {
  .dark\:invert {
    filter: invert(1);
  }
}
@media (prefers-reduced-data: reduce) {
  video,
  iframe {
    display: none !important;
  }
  img {
    filter: blur(5px);
  }
  * {
    animation-duration: 0.1s !important;
  }
}

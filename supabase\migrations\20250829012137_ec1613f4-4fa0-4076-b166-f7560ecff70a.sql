-- Fix security vulnerability: Remove public read access to sensitive lead data
DROP POLICY "Allow viewing all leads" ON public.leads;

-- Only allow authenticated users to view leads (for admin/staff access)
CREATE POLICY "Authenticated users can view leads" 
ON public.leads 
FOR SELECT 
TO authenticated
USING (true);

-- Keep public insert access for lead capture form
-- (existing "Allow public lead submissions" policy already handles this correctly)
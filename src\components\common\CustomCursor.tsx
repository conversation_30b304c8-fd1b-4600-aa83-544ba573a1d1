import { cn } from "@/lib/utils";
import { motion, useMotionValue, useSpring } from "framer-motion";
import React, { useEffect, useRef, useState } from "react";

/**
 * Custom Cursor Component
 *
 * Provides an interactive custom cursor with trail effects and hover states.
 * Automatically hides on touch devices and provides visual feedback for interactive elements.
 */
interface CustomCursorProps {
  /** Additional CSS classes for styling */
  className?: string;
}

interface TrailPoint {
  x: number;
  y: number;
  id: number;
}

const CustomCursor: React.FC<CustomCursorProps> = ({ className }) => {
  const [isPointer, setIsPointer] = useState(false);
  const [isHidden, setIsHidden] = useState(false);
  const [isText, setIsText] = useState(false);
  const [trail, setTrail] = useState<TrailPoint[]>([]);

  const cursorX = useMotionValue(0);
  const cursorY = useMotionValue(0);

  const springConfig = { damping: 25, stiffness: 500 };
  const cursorXSpring = useSpring(cursorX, springConfig);
  const cursorYSpring = useSpring(cursorY, springConfig);

  const dotRef = useRef<HTMLDivElement>(null);
  const ringRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (typeof window === "undefined") return;

    // Hide on touch devices
    if ("ontouchstart" in window) {
      setIsHidden(true);
      return;
    }

    const handleMouseMove = (e: MouseEvent) => {
      cursorX.set(e.clientX);
      cursorY.set(e.clientY);

      // Add to trail
      const id = Date.now();
      setTrail((prev) => [
        ...prev.slice(-10),
        { x: e.clientX, y: e.clientY, id },
      ]);

      // Check what element we're hovering
      const target = e.target as HTMLElement;
      const isLink = target.tagName === "A" || target.closest("a");
      const isButton = target.tagName === "BUTTON" || target.closest("button");
      const isInteractive = target.dataset.cursor === "pointer";
      const isTextElement =
        target.tagName === "P" ||
        target.tagName === "H1" ||
        target.tagName === "H2" ||
        target.tagName === "H3" ||
        target.tagName === "SPAN" ||
        target.closest("p, h1, h2, h3");

      setIsPointer(!!isLink || !!isButton || !!isInteractive);
      setIsText(!!isTextElement);
    };

    const handleMouseEnter = () => setIsHidden(false);
    const handleMouseLeave = () => setIsHidden(true);

    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseenter", handleMouseEnter);
    document.addEventListener("mouseleave", handleMouseLeave);

    // Hide default cursor
    document.body.style.cursor = "none";

    // Add custom cursor styles to interactive elements
    const style = document.createElement("style");
    style.textContent = `
      * { cursor: none !important; }
      a, button, [data-cursor="pointer"] { cursor: none !important; }
    `;
    document.head.appendChild(style);

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseenter", handleMouseEnter);
      document.removeEventListener("mouseleave", handleMouseLeave);
      document.body.style.cursor = "auto";
      document.head.removeChild(style);
    };
  }, [cursorX, cursorY]);

  // Clean up trail periodically
  useEffect(() => {
    const interval = setInterval(() => {
      setTrail((prev) => prev.slice(-5));
    }, 100);

    return () => clearInterval(interval);
  }, []);

  if (isHidden) return null;

  return (
    <>
      {/* Trail effect */}
      {trail.map((point, index) => (
        <motion.div
          key={point.id}
          className="pointer-events-none fixed z-[9997]"
          style={{
            left: point.x,
            top: point.y,
            x: "-50%",
            y: "-50%",
          }}
          initial={{ scale: 0.8, opacity: 0.4 }}
          animate={{ scale: 0, opacity: 0 }}
          transition={{ duration: 0.5, delay: index * 0.02 }}
        >
          <div className="h-2 w-2 rounded-full bg-blue-400 mix-blend-screen" />
        </motion.div>
      ))}

      {/* Cursor dot */}
      <motion.div
        ref={dotRef}
        className={cn(
          "pointer-events-none fixed z-[9999] h-2 w-2 mix-blend-difference",
          className
        )}
        style={{
          left: cursorXSpring,
          top: cursorYSpring,
          x: "-50%",
          y: "-50%",
        }}
        animate={{
          scale: isPointer ? 0.5 : 1,
          backgroundColor: isPointer ? "#3b82f6" : "#ffffff",
        }}
        transition={{ duration: 0.2 }}
      >
        <div className="h-full w-full rounded-full bg-current" />
      </motion.div>

      {/* Cursor ring */}
      <motion.div
        ref={ringRef}
        className="pointer-events-none fixed z-[9998]"
        style={{
          left: cursorXSpring,
          top: cursorYSpring,
          x: "-50%",
          y: "-50%",
        }}
        animate={{
          scale: isPointer ? 1.5 : isText ? 1.2 : 1,
          borderWidth: isPointer ? 2 : 1,
        }}
        transition={{ duration: 0.3 }}
      >
        <div
          className={cn(
            "h-8 w-8 rounded-full border border-blue-500 mix-blend-difference",
            isPointer && "border-blue-600",
            isText && "border-purple-500"
          )}
        />
      </motion.div>

      {/* Hover effect for interactive elements */}
      {isPointer && (
        <motion.div
          className="pointer-events-none fixed z-[9996]"
          style={{
            left: cursorXSpring,
            top: cursorYSpring,
            x: "-50%",
            y: "-50%",
          }}
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 2, opacity: 0.2 }}
          transition={{ duration: 0.3 }}
        >
          <div className="h-12 w-12 rounded-full bg-blue-400 blur-xl" />
        </motion.div>
      )}
    </>
  );
};

export default CustomCursor;

import React from "react";

/**
 * Third Section Component
 *
 * Experience-focused section with animated organic shapes
 * and bird-like SVG patterns.
 */
const ThirdSection: React.FC = () => {
  return (
    <section className="relative flex min-h-screen items-center justify-center overflow-hidden bg-gradient-blue">
      {/* Organic floating elements inspired by Microsoft AI */}
      <div className="organic-blob right-1/4 top-1/4 h-72 w-72 bg-gradient-purple"></div>
      <div className="organic-blob bottom-1/3 left-1/3 h-64 w-64 bg-gradient-green"></div>

      <div className="container-optimized relative z-10">
        <div className="grid items-center gap-16 lg:grid-cols-2">
          <div className="relative">
            {/* Abstract birds/organic shapes inspired by Microsoft AI */}
            <div className="relative h-80 w-full lg:h-96">
              <svg
                className="absolute inset-0 h-full w-full"
                viewBox="0 0 400 400"
                fill="none"
              >
                {/* Organic bird-like shapes */}
                <path
                  d="M100 200 Q150 150 200 200 Q250 250 300 200 Q250 150 200 100 Q150 150 100 200Z"
                  fill="rgba(255,255,255,0.3)"
                  className="animate-float"
                />
                <path
                  d="M80 250 Q130 200 180 250 Q230 300 280 250 Q230 200 180 150 Q130 200 80 250Z"
                  fill="rgba(255,255,255,0.2)"
                  className="animate-float"
                  style={{ animationDelay: "5s" }}
                />
                <path
                  d="M120 300 Q170 250 220 300 Q270 350 320 300 Q270 250 220 200 Q170 250 120 300Z"
                  fill="rgba(255,255,255,0.25)"
                  className="animate-float"
                  style={{ animationDelay: "10s" }}
                />
              </svg>
            </div>
          </div>

          <div className="space-y-6">
            <h2 className="text-display-large text-foreground">
              We build
              <br />
              <em className="font-light italic text-primary">experiences.</em>
            </h2>
            <p className="text-body-large max-w-lg text-muted-foreground">
              Our eyes are set on an entirely new class of AI-centered
              experiences built for people.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ThirdSection;

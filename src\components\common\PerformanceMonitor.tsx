import { useEffect, useState, useRef } from "react";

interface PerformanceMetrics {
  lcp?: number;
  fid?: number;
  cls?: number;
  fcp?: number;
  ttfb?: number;
  fps?: number;
  scrollDepth?: number;
  sectionViews?: Map<string, number>;
}

const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({});
  const [showMetrics, setShowMetrics] = useState(false);
  const scrollDepthRef = useRef(0);
  const sectionTimersRef = useRef<Map<string, number>>(new Map());
  const sectionViewsRef = useRef<Map<string, number>>(new Map());
  const frameCountRef = useRef(0);
  const lastFrameTimeRef = useRef(performance.now());

  useEffect(() => {
    // Only run in development mode
    if (process.env.NODE_ENV !== "development") return;

    // Core Web Vitals tracking
    if ("PerformanceObserver" in window) {
      try {
        // LCP (Largest Contentful Paint)
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          setMetrics((prev) => ({ ...prev, lcp: lastEntry.startTime }));
          console.log(`[Performance] LCP: ${lastEntry.startTime.toFixed(2)}ms`);
        });
        lcpObserver.observe({ entryTypes: ["largest-contentful-paint"] });

        // FID (First Input Delay)
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const firstInput = entries[0] as any;
          const fid = firstInput.processingStart - firstInput.startTime;
          setMetrics((prev) => ({ ...prev, fid }));
          console.log(`[Performance] FID: ${fid.toFixed(2)}ms`);
        });
        fidObserver.observe({ entryTypes: ["first-input"] });

        // CLS (Cumulative Layout Shift)
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!(entry as any).hadRecentInput) {
              clsValue += (entry as any).value;
              setMetrics((prev) => ({ ...prev, cls: clsValue }));
            }
          }
          console.log(`[Performance] CLS: ${clsValue.toFixed(4)}`);
        });
        clsObserver.observe({ entryTypes: ["layout-shift"] });

        // FCP (First Contentful Paint)
        const fcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const fcpEntry = entries.find(
            (entry) => entry.name === "first-contentful-paint"
          );
          if (fcpEntry) {
            setMetrics((prev) => ({ ...prev, fcp: fcpEntry.startTime }));
            console.log(
              `[Performance] FCP: ${fcpEntry.startTime.toFixed(2)}ms`
            );
          }
        });
        fcpObserver.observe({ entryTypes: ["paint"] });
      } catch (error) {
        console.warn("[Performance] Some metrics not available:", error);
      }
    }

    // TTFB (Time to First Byte)
    if (window.performance && window.performance.timing) {
      const timing = window.performance.timing;
      const ttfb = timing.responseStart - timing.navigationStart;
      setMetrics((prev) => ({ ...prev, ttfb }));
      console.log(`[Performance] TTFB: ${ttfb}ms`);
    }

    // Scroll depth tracking
    let maxScrollDepth = 0;
    const handleScroll = () => {
      const scrollHeight =
        document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = (window.scrollY / scrollHeight) * 100;
      maxScrollDepth = Math.max(maxScrollDepth, scrollPercent);
      scrollDepthRef.current = maxScrollDepth;
      setMetrics((prev) => ({ ...prev, scrollDepth: maxScrollDepth }));
    };

    window.addEventListener("scroll", handleScroll, { passive: true });

    // Section visibility tracking
    const sections = document.querySelectorAll(
      'section, [id^="s"], .crossfade-section'
    );
    const sectionObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const sectionId = entry.target.id || entry.target.className;

          if (entry.isIntersecting) {
            sectionTimersRef.current.set(sectionId, Date.now());
          } else if (sectionTimersRef.current.has(sectionId)) {
            const startTime = sectionTimersRef.current.get(sectionId)!;
            const duration = Date.now() - startTime;
            const currentTotal = sectionViewsRef.current.get(sectionId) || 0;
            sectionViewsRef.current.set(sectionId, currentTotal + duration);
            console.log(
              `[Analytics] Section "${sectionId}" viewed for ${(duration / 1000).toFixed(1)}s`
            );
            sectionTimersRef.current.delete(sectionId);
          }
        });
      },
      { threshold: 0.5 }
    );

    sections.forEach((section) => sectionObserver.observe(section));

    // FPS monitoring
    let animationFrameId: number;
    const measureFPS = () => {
      const now = performance.now();
      const delta = now - lastFrameTimeRef.current;

      if (delta >= 1000) {
        const fps = Math.round((frameCountRef.current * 1000) / delta);
        setMetrics((prev) => ({ ...prev, fps }));
        frameCountRef.current = 0;
        lastFrameTimeRef.current = now;
      }

      frameCountRef.current++;
      animationFrameId = requestAnimationFrame(measureFPS);
    };

    animationFrameId = requestAnimationFrame(measureFPS);

    // Resource timing
    const logResourceTiming = () => {
      const resources = performance.getEntriesByType("resource");
      const summary = {
        images: resources.filter((r) =>
          r.name.match(/\.(jpg|jpeg|png|gif|webp|svg)/i)
        ),
        scripts: resources.filter((r) => r.name.match(/\.(js|mjs)/i)),
        styles: resources.filter((r) => r.name.match(/\.(css)/i)),
      };

      console.log("[Performance] Resource Summary:", {
        images: `${summary.images.length} images, ${(summary.images.reduce((acc, r) => acc + r.duration, 0) / 1000).toFixed(2)}s total`,
        scripts: `${summary.scripts.length} scripts, ${(summary.scripts.reduce((acc, r) => acc + r.duration, 0) / 1000).toFixed(2)}s total`,
        styles: `${summary.styles.length} styles, ${(summary.styles.reduce((acc, r) => acc + r.duration, 0) / 1000).toFixed(2)}s total`,
      });
    };

    // Log resource timing after page load
    if (document.readyState === "complete") {
      logResourceTiming();
    } else {
      window.addEventListener("load", logResourceTiming);
    }

    // Log session summary on page unload
    const logSessionSummary = () => {
      console.log("[Analytics] Session Summary:", {
        maxScrollDepth: `${scrollDepthRef.current.toFixed(1)}%`,
        sectionViews: Object.fromEntries(
          Array.from(sectionViewsRef.current.entries()).map(([id, time]) => [
            id,
            `${(time / 1000).toFixed(1)}s`,
          ])
        ),
        metrics: {
          lcp: metrics.lcp ? `${metrics.lcp.toFixed(0)}ms` : "N/A",
          fid: metrics.fid ? `${metrics.fid.toFixed(0)}ms` : "N/A",
          cls: metrics.cls ? metrics.cls.toFixed(4) : "N/A",
          fcp: metrics.fcp ? `${metrics.fcp.toFixed(0)}ms` : "N/A",
          ttfb: metrics.ttfb ? `${metrics.ttfb}ms` : "N/A",
        },
      });
    };

    window.addEventListener("beforeunload", logSessionSummary);

    // Keyboard shortcut to toggle metrics display (Ctrl/Cmd + Shift + P)
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.key === "P" && e.shiftKey && (e.ctrlKey || e.metaKey)) {
        setShowMetrics((prev) => !prev);
      }
    };

    window.addEventListener("keydown", handleKeyPress);

    // Cleanup
    return () => {
      window.removeEventListener("scroll", handleScroll);
      window.removeEventListener("beforeunload", logSessionSummary);
      window.removeEventListener("keydown", handleKeyPress);
      window.removeEventListener("load", logResourceTiming);
      cancelAnimationFrame(animationFrameId);
      sectionObserver.disconnect();
    };
  }, [metrics]);

  // Don't render anything in production
  if (process.env.NODE_ENV !== "development") return null;

  // Optional metrics overlay (toggle with Ctrl/Cmd + Shift + P)
  if (!showMetrics) return null;

  return (
    <div className="fixed bottom-4 right-4 z-[9999] max-w-sm rounded-lg bg-black/80 p-4 font-mono text-xs text-white">
      <div className="mb-2 font-bold">Performance Metrics</div>
      <div className="space-y-1">
        <div>FPS: {metrics.fps || "—"}</div>
        <div>LCP: {metrics.lcp ? `${metrics.lcp.toFixed(0)}ms` : "—"}</div>
        <div>FID: {metrics.fid ? `${metrics.fid.toFixed(0)}ms` : "—"}</div>
        <div>CLS: {metrics.cls ? metrics.cls.toFixed(4) : "—"}</div>
        <div>FCP: {metrics.fcp ? `${metrics.fcp.toFixed(0)}ms` : "—"}</div>
        <div>TTFB: {metrics.ttfb ? `${metrics.ttfb}ms` : "—"}</div>
        <div>
          Scroll:{" "}
          {metrics.scrollDepth ? `${metrics.scrollDepth.toFixed(1)}%` : "—"}
        </div>
      </div>
      <div className="mt-2 text-[10px] opacity-60">
        Press Ctrl+Shift+P to toggle
      </div>
    </div>
  );
};

export default PerformanceMonitor;

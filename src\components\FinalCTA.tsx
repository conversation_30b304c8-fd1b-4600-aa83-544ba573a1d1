import React from "react";
import { Button } from "@/components/ui/button";

const FinalCTA: React.FC = () => {
  const [email, setEmail] = React.useState("");
  const [submitted, setSubmitted] = React.useState(false);

  const onSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitted(true);
  };

  return (
    <section
      className="relative overflow-hidden"
      style={{ background: "linear-gradient(180deg, #f6f7fb 0%, #ffffff 60%)" }}
    >
      <div className="container relative z-10 mx-auto px-6 py-24 md:py-32">
        <div className="grid items-center gap-12 lg:grid-cols-2">
          <div>
            <h2 className="max-w-[18ch] text-6xl font-light leading-[1.05] text-slate-900 md:text-7xl lg:text-8xl">
              See Entheory in action
            </h2>
            <p className="mt-5 max-w-xl text-lg text-slate-600">
              Get a 6‑minute walkthrough of the workflow. No sales talk—just the
              product.
            </p>
            <form
              onSubmit={onSubmit}
              className="mt-6 flex max-w-xl flex-col gap-3 sm:flex-row sm:items-center"
            >
              <label htmlFor="cta-email" className="sr-only">
                Work email
              </label>
              <input
                id="cta-email"
                type="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Work email"
                className="h-11 w-full rounded-md border border-slate-300 bg-white px-3 text-sm text-slate-900 placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-indigo-300 sm:w-72"
                aria-describedby="cta-note"
              />
              <Button
                type="submit"
                className="h-11 bg-indigo-600 px-5 text-white hover:bg-indigo-700"
              >
                Watch the demo
              </Button>
            </form>
            <p id="cta-note" className="mt-3 text-[12px] text-slate-500">
              We’ll send a link—no spam, no sharing.
            </p>
            {submitted && (
              <div className="mt-3 text-sm text-emerald-700">
                Thanks! We’ll email you the demo link shortly.
              </div>
            )}
          </div>
          <div className="flex justify-center lg:justify-end">
            <div className="w-full max-w-md rounded-2xl bg-white/70 p-6 shadow-xl ring-1 ring-slate-200 backdrop-blur-md">
              <div className="text-sm font-semibold text-slate-900">
                What you’ll see
              </div>
              <ul className="mt-3 space-y-2 text-sm text-slate-700">
                <li>• Intake & consent in under a minute</li>
                <li>• One timeline with provenance</li>
                <li>• Board‑ready summary with citations</li>
                <li>• Safer actions & checks</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      {/* Headroom to smooth enter/exit like Section 4 tail buffer */}
      <div className="h-[20vh] md:h-[26vh] lg:h-[30vh]" aria-hidden="true" />
    </section>
  );
};

export default FinalCTA;

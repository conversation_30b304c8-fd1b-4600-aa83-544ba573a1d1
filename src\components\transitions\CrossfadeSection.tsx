import { motion, useScroll, useTransform } from "framer-motion";
import React from "react";

/**
 * Crossfade Section Component
 *
 * Provides smooth fade-in/fade-out transitions based on scroll position.
 * Elements fade in when entering viewport and fade out when leaving.
 */

interface CrossfadeSectionProps {
  id?: string;
  className?: string;
  children: React.ReactNode;
}

const CrossfadeSection: React.FC<CrossfadeSectionProps> = ({
  id,
  className,
  children,
}) => {
  const ref = React.useRef<HTMLDivElement>(null);
  // Progress 0→1 as this section moves through the viewport
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start 85%", "end 15%"],
  });
  // Fade in on enter, stay solid, fade out on exit
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);
  const y = useTransform(scrollYProgress, [0, 1], [12, 0]);

  return (
    <motion.div
      id={id}
      ref={ref}
      className={`relative ${className || ""}`}
      style={{ opacity, y }}
    >
      {children}
    </motion.div>
  );
};

export default CrossfadeSection;

import {
  motion,
  useReducedMotion,
  useScroll,
  useTransform,
} from "framer-motion";
import React, { memo, useRef } from "react";

/**
 * Crossfade Section Component
 *
 * Optimized smooth fade-in/fade-out transitions based on scroll position.
 * Features performance optimizations and accessibility improvements.
 */

interface CrossfadeSectionProps {
  /** Optional element ID */
  id?: string;
  /** Optional CSS class name */
  className?: string;
  /** Child elements to render */
  children: React.ReactNode;
  /** Whether to enable animations */
  enableAnimations?: boolean;
  /** Custom fade offset points */
  fadeOffset?: [string, string];
  /** Custom opacity transition points */
  opacityPoints?: [number, number, number, number];
}

const CrossfadeSection: React.FC<CrossfadeSectionProps> = memo(
  ({
    id,
    className = "",
    children,
    enableAnimations = true,
    fadeOffset = ["start 85%", "end 15%"],
    opacityPoints = [0, 0.2, 0.8, 1],
  }) => {
    const ref = useRef<HTMLDivElement>(null);
    const prefersReducedMotion = useReducedMotion();
    const shouldAnimate = enableAnimations && !prefersReducedMotion;

    // Progress 0→1 as this section moves through the viewport
    const { scrollYProgress } = useScroll({
      target: ref,
      offset: fadeOffset,
    });

    // Optimized transforms - only apply if animations are enabled
    const opacity = useTransform(
      scrollYProgress,
      opacityPoints,
      shouldAnimate ? [0, 1, 1, 0] : [1, 1, 1, 1]
    );
    const y = useTransform(
      scrollYProgress,
      [0, 1],
      shouldAnimate ? [8, 0] : [0, 0] // Reduced movement for better performance
    );

    return (
      <motion.div
        id={id}
        ref={ref}
        className={`relative ${className}`}
        style={{
          opacity,
          y,
          willChange: shouldAnimate ? "opacity, transform" : "auto",
        }}
      >
        {children}
      </motion.div>
    );
  }
);

CrossfadeSection.displayName = "CrossfadeSection";

export default CrossfadeSection;
export type { CrossfadeSectionProps };

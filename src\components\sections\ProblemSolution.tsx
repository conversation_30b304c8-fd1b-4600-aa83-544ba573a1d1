import { AlertTriangle, Database, TrendingUp, Users } from "lucide-react";
import React from "react";

/**
 * Problem Solution Section Component
 *
 * Highlights healthcare challenges and presents OncoConnect's solutions
 * with statistics and impact metrics.
 */

interface Problem {
  icon: React.ReactNode;
  title: string;
  description: string;
  impact: string;
}

interface Solution {
  icon: React.ReactNode;
  title: string;
  description: string;
  benefit: string;
}

const ProblemSolution: React.FC = () => {
  const problems = [
    {
      icon: <AlertTriangle className="h-8 w-8 text-destructive" />,
      title: "Data Fragmentation Crisis",
      description:
        "60-70% of Indian hospitals use outdated legacy systems creating dangerous data silos that delay critical cancer diagnosis and treatment decisions.",
      impact: "916K+ deaths in 2022",
    },
    {
      icon: <TrendingUp className="h-8 w-8 text-destructive" />,
      title: "Growing Cancer Burden",
      description:
        "Cancer incidence projected to reach 1.57 million cases by 2025, with mortality rising largely due to late detection and fragmented care.",
      impact: "+12.8% increase from 2020",
    },
    {
      icon: <Users className="h-8 w-8 text-destructive" />,
      title: "Critical Workforce Gap",
      description:
        "Severe oncologist shortage with a 1:730 ratio, hampering tumor board effectiveness and multidisciplinary care coordination.",
      impact: "2,300 shortage by 2025",
    },
  ];

  return (
    <section id="solution" className="bg-background py-24">
      <div className="container mx-auto px-6">
        {/* Problem Statement */}
        <div className="mb-20 text-center">
          <div className="mb-6 inline-flex items-center rounded-full bg-destructive/10 px-4 py-2 text-sm font-semibold text-destructive shadow-soft">
            ⚠️ Critical Healthcare Challenge
          </div>
          <h2 className="mb-6 text-5xl font-black leading-tight text-foreground lg:text-6xl">
            The Cancer Care Crisis in
            <span className="mt-2 block bg-gradient-hero bg-clip-text text-transparent">
              India
            </span>
          </h2>
          <p className="mx-auto max-w-4xl text-xl font-medium leading-relaxed text-muted-foreground">
            Fragmented healthcare data systems are creating life-threatening
            delays in cancer diagnosis and treatment across India's healthcare
            infrastructure.
          </p>
        </div>

        <div className="mb-24 grid gap-8 md:grid-cols-3">
          {problems.map((problem, index) => (
            <div key={index} className="group relative">
              <div className="bg-gradient-surface hover:shadow-strong h-full rounded-3xl border border-border/50 p-8 shadow-soft transition-all duration-300 hover:scale-105 hover:border-border">
                <div className="relative mb-6">
                  <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-destructive/10 transition-transform duration-300 group-hover:scale-110">
                    {problem.icon}
                  </div>
                  <div className="absolute -right-2 -top-2 rounded-full bg-destructive px-2 py-1 text-xs font-bold text-destructive-foreground">
                    {problem.impact}
                  </div>
                </div>
                <h3 className="mb-4 text-2xl font-bold text-foreground">
                  {problem.title}
                </h3>
                <p className="leading-relaxed text-muted-foreground">
                  {problem.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Solution */}
        <div className="relative">
          <div className="mb-16 text-center">
            <div className="bg-gradient-surface mb-6 inline-flex items-center rounded-full border border-border/50 px-4 py-2 text-sm font-semibold text-foreground shadow-soft">
              <div className="mr-3 h-2 w-2 animate-pulse rounded-full bg-gradient-hero"></div>
              Our AI Solution
            </div>
            <h2 className="mb-6 text-5xl font-black leading-tight text-foreground lg:text-6xl">
              FHIR-Based
              <span className="mt-2 block bg-gradient-hero bg-clip-text text-transparent">
                Interoperability Platform
              </span>
            </h2>
            <p className="mx-auto max-w-4xl text-xl font-medium leading-relaxed text-muted-foreground">
              A comprehensive platform that seamlessly integrates disparate
              hospital data sources to create unified patient histories and
              generate evidence-based tumor board reports with academic
              citations.
            </p>
          </div>

          <div className="grid items-center gap-16 lg:grid-cols-2">
            <div className="space-y-8">
              <div className="group flex items-start space-x-6">
                <div className="bg-gradient-surface flex h-16 w-16 flex-shrink-0 items-center justify-center rounded-2xl border border-border/20 shadow-soft transition-transform duration-300 group-hover:scale-110">
                  <Database className="h-8 w-8 text-primary" />
                </div>
                <div>
                  <h3 className="mb-3 text-2xl font-bold text-foreground">
                    Unified Data Integration
                  </h3>
                  <p className="font-medium leading-relaxed text-muted-foreground">
                    Seamlessly connects EMR, PACS, LIS, HIMS, pathology reports,
                    billing systems, and telemedicine data into a single,
                    comprehensive view.
                  </p>
                </div>
              </div>

              <div className="group flex items-start space-x-6">
                <div className="bg-gradient-surface flex h-16 w-16 flex-shrink-0 items-center justify-center rounded-2xl border border-border/20 shadow-soft transition-transform duration-300 group-hover:scale-110">
                  <TrendingUp className="h-8 w-8 text-accent" />
                </div>
                <div>
                  <h3 className="mb-3 text-2xl font-bold text-foreground">
                    Longitudinal Patient Histories
                  </h3>
                  <p className="font-medium leading-relaxed text-muted-foreground">
                    Creates chronological patient timelines from multi-source
                    inputs, providing complete context for informed
                    decision-making.
                  </p>
                </div>
              </div>

              <div className="group flex items-start space-x-6">
                <div className="bg-gradient-surface flex h-16 w-16 flex-shrink-0 items-center justify-center rounded-2xl border border-border/20 shadow-soft transition-transform duration-300 group-hover:scale-110">
                  <AlertTriangle className="h-8 w-8 text-success" />
                </div>
                <div>
                  <h3 className="mb-3 text-2xl font-bold text-foreground">
                    Evidence-Based Reports
                  </h3>
                  <p className="font-medium leading-relaxed text-muted-foreground">
                    Generates contextual tumor board reports with verifiable
                    citations from PubMed, ClinicalTrials.gov, and academic
                    databases.
                  </p>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="bg-gradient-surface shadow-strong rounded-3xl border border-border/20 p-10">
                <div className="space-y-6">
                  <div className="flex items-center justify-between rounded-2xl border border-border/50 bg-background p-4 shadow-soft">
                    <span className="font-semibold text-foreground">
                      EMR Systems
                    </span>
                    <div className="h-3 w-3 animate-pulse rounded-full bg-success"></div>
                  </div>
                  <div className="flex items-center justify-between rounded-2xl border border-border/50 bg-background p-4 shadow-soft">
                    <span className="font-semibold text-foreground">
                      PACS Imaging
                    </span>
                    <div className="h-3 w-3 animate-pulse rounded-full bg-success"></div>
                  </div>
                  <div className="flex items-center justify-between rounded-2xl border border-border/50 bg-background p-4 shadow-soft">
                    <span className="font-semibold text-foreground">
                      Lab Results (LIS)
                    </span>
                    <div className="h-3 w-3 animate-pulse rounded-full bg-success"></div>
                  </div>
                  <div className="flex items-center justify-between rounded-2xl border border-border/50 bg-background p-4 shadow-soft">
                    <span className="font-semibold text-foreground">
                      Pathology Reports
                    </span>
                    <div className="h-3 w-3 animate-pulse rounded-full bg-success"></div>
                  </div>
                  <div className="py-6 text-center">
                    <div className="shadow-glow inline-flex items-center rounded-2xl bg-gradient-hero px-6 py-3 font-bold text-white">
                      ⚡ AI Processing Layer
                    </div>
                  </div>
                  <div className="shadow-glow rounded-2xl bg-gradient-hero p-6 text-center text-white">
                    <div className="text-xl font-bold">
                      Unified Patient History
                    </div>
                    <div className="mt-2 text-sm opacity-90">
                      with Evidence-Based Insights
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProblemSolution;

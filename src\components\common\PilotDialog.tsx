import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { postLead } from "@/lib/lead";
import React from "react";

/**
 * Pilot Dialog Component
 *
 * Modal dialog for collecting early pilot program signups.
 * Includes form validation, submission handling, and success state.
 */

const PilotDialog: React.FC = () => {
  const [open, setOpen] = React.useState(false);
  const [email, setEmail] = React.useState("");
  const [fullName, setFullName] = React.useState("");
  const [role, setRole] = React.useState<"physician" | "administration">(
    "physician"
  );
  const [hospital, setHospital] = React.useState("");
  const [city, setCity] = React.useState("");
  const [phone, setPhone] = React.useState("");
  const [notes, setNotes] = React.useState("");
  const [submitted, setSubmitted] = React.useState(false);

  React.useEffect(() => {
    const handler = () => setOpen(true);
    window.addEventListener("early-pilot-open", handler as EventListener);
    return () =>
      window.removeEventListener("early-pilot-open", handler as EventListener);
  }, []);

  const reset = () => {
    setEmail("");
    setFullName("");
    setHospital("");
    setCity("");
    setPhone("");
    setNotes("");
    setRole("physician");
    setSubmitted(false);
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(v) => {
        setOpen(v);
        if (!v) reset();
      }}
    >
      <DialogContent className="overflow-hidden p-0 sm:max-w-[600px]">
        <div className="p-6">
          {!submitted ? (
            <>
              <DialogHeader className="space-y-3">
                <DialogTitle className="text-heading-2">
                  Partner with Entheory: Early Pilot
                </DialogTitle>
                <DialogDescription className="text-body-base text-muted-foreground">
                  Book a personalized walkthrough to explore a limited early
                  pilot with your oncology team.
                </DialogDescription>
              </DialogHeader>

              <form
                className="mt-6 grid gap-4"
                onSubmit={async (e) => {
                  e.preventDefault();
                  if (!/[^\s@]+@[^\s@]+\.[^\s@]+/.test(email)) return;
                  if (!fullName.trim() || !hospital.trim()) return;
                  try {
                    window.dispatchEvent(
                      new CustomEvent("early-pilot-submit", {
                        detail: {
                          email,
                          name: fullName,
                          role,
                          hospital,
                          city,
                          phone,
                          notes,
                        },
                      })
                    );
                    await postLead({
                      source: "pilot",
                      email,
                      name: fullName,
                      role,
                      hospital,
                      city,
                      phone,
                      notes,
                    });
                  } catch (error) {
                    console.warn("Failed to submit pilot form:", error);
                  }
                  setSubmitted(true);
                }}
              >
                <div className="grid gap-4">
                  <Input
                    id="pilot-name-global"
                    type="text"
                    required
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    placeholder="Full name"
                    aria-label="Full name"
                  />
                  <Input
                    id="pilot-email-global"
                    type="email"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Company email"
                    aria-label="Company email"
                  />
                  <div className="grid gap-3 sm:grid-cols-2">
                    <Input
                      id="pilot-hospital"
                      type="text"
                      required
                      value={hospital}
                      onChange={(e) => setHospital(e.target.value)}
                      placeholder="Hospital / Institution"
                      aria-label="Hospital / Institution"
                    />
                    <Input
                      id="pilot-city"
                      type="text"
                      value={city}
                      onChange={(e) => setCity(e.target.value)}
                      placeholder="City"
                      aria-label="City"
                    />
                  </div>
                  <div className="grid gap-3 sm:grid-cols-2">
                    <div className="space-y-2">
                      <span className="text-sm font-medium text-foreground">
                        Your role
                      </span>
                      <div className="flex gap-2">
                        <Button
                          type="button"
                          variant={role === "physician" ? "default" : "outline"}
                          size="sm"
                          onClick={() => setRole("physician")}
                          aria-pressed={role === "physician"}
                        >
                          Physician
                        </Button>
                        <Button
                          type="button"
                          variant={
                            role === "administration" ? "default" : "outline"
                          }
                          size="sm"
                          onClick={() => setRole("administration")}
                          aria-pressed={role === "administration"}
                        >
                          Administration
                        </Button>
                      </div>
                    </div>
                    <Input
                      id="pilot-phone"
                      type="tel"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      placeholder="Phone (optional)"
                      aria-label="Phone number"
                    />
                  </div>
                  <label
                    htmlFor="pilot-notes"
                    className="sr-only text-sm font-medium"
                  >
                    Key context
                  </label>
                  <textarea
                    id="pilot-notes"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="Key context (eg. tumor board workflow, bed count, EHR/PACS vendors)"
                    className="min-h-[90px] w-full rounded-md border border-slate-300 px-3 py-2 text-[14px]"
                  />
                  <Button
                    type="submit"
                    className="h-12 rounded-md bg-black font-medium text-white hover:bg-neutral-900"
                  >
                    Book an early pilot →
                  </Button>
                </div>
                <p className="mt-4 text-[12px] text-slate-500">
                  By submitting, you agree to be contacted about a pilot. No
                  spam. We respect your privacy.
                </p>
              </form>
            </>
          ) : (
            <div className="py-12 text-center">
              <h3 className="text-2xl font-medium text-slate-900">
                Thanks! We’ll contact you shortly.
              </h3>
              <p className="mt-2 text-slate-600">
                Our team received your request for an early pilot. We’ll reach
                out by email with next steps.
              </p>
              <div className="mt-6">
                <Button
                  onClick={() => {
                    setOpen(false);
                    reset();
                  }}
                  className="h-11 px-6"
                >
                  Close
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PilotDialog;

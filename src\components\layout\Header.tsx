import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, SheetTrigger } from "@/components/ui/sheet";
import { Menu, X } from "lucide-react";
import React, { useState } from "react";

/**
 * Header Component
 *
 * Main site header with navigation, logo, and mobile menu.
 * Includes smooth scrolling navigation and responsive design.
 */

interface NavLink {
  href: string;
  label: string;
  external?: boolean;
}

const Header: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  const navLinks: NavLink[] = [
    { href: "#s2", label: "Approach" },
    { href: "#s3", label: "Product" },
    { href: "#s4", label: "Advantage" },
    {
      href: "https://forms.office.com/r/rv2nCDByTQ",
      label: "Contribute",
      external: true,
    },
  ];

  const handleNavClick = (href: string): void => {
    setIsOpen(false); // Close mobile menu
    if (!href.startsWith("http")) {
      // Smooth scroll for anchor links
      setTimeout(() => {
        const element = document.querySelector(href);
        if (element) {
          element.scrollIntoView({ behavior: "smooth" });
        }
      }, 100);
    }
  };

  const handlePilotOpen = (): void => {
    try {
      window.dispatchEvent(new Event("early-pilot-open"));
    } catch (error) {
      console.warn("Failed to dispatch pilot event:", error);
    }
  };

  return (
    <header className="fixed left-1/2 top-4 z-50 w-[calc(100%-2rem)] -translate-x-1/2 transform sm:top-6 sm:w-auto">
      <nav className="rounded-2xl border border-border/20 bg-white/90 px-6 py-3 shadow-lg backdrop-blur-xl sm:px-8 sm:py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <a
              href="#hero"
              aria-label="Go to hero"
              onClick={() => handleNavClick("#hero")}
              className="transition-opacity hover:opacity-80"
            >
              <img
                src="/logo.png"
                alt="Entheory Logo"
                className="h-7 w-auto sm:h-8"
              />
            </a>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden items-center space-x-8 md:flex">
            {navLinks.map((link) => (
              <a
                key={link.href}
                href={link.href}
                className="transition-organic text-sm font-medium text-muted-foreground hover:text-foreground"
                onClick={
                  link.external
                    ? undefined
                    : (e) => {
                        e.preventDefault();
                        handleNavClick(link.href);
                      }
                }
                {...(link.external && {
                  target: "_blank",
                  rel: "noopener noreferrer",
                })}
              >
                {link.label}
              </a>
            ))}
          </div>

          {/* CTA and Mobile Menu */}
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="rounded-full px-3 text-xs font-medium sm:px-4 sm:text-sm"
              onClick={handlePilotOpen}
            >
              Early pilot
            </Button>

            {/* Mobile Menu Trigger */}
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="p-2 md:hidden"
                  aria-label="Open menu"
                >
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[280px] p-0 sm:w-[300px]">
                <div className="flex h-full flex-col">
                  {/* Mobile Menu Header */}
                  <div className="flex items-center justify-between border-b p-6">
                    <img
                      src="/logo.png"
                      alt="Entheory Logo"
                      className="h-8 w-auto"
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsOpen(false)}
                      className="p-2"
                      aria-label="Close menu"
                    >
                      <X className="h-5 w-5" />
                    </Button>
                  </div>

                  {/* Mobile Navigation Links */}
                  <div className="flex flex-col gap-1 p-6">
                    {navLinks.map((link) => (
                      <a
                        key={link.href}
                        href={link.href}
                        className="rounded-lg px-2 py-3 text-base font-medium text-foreground transition-colors hover:bg-muted/50 hover:text-primary"
                        onClick={
                          link.external
                            ? () => setIsOpen(false)
                            : (e) => {
                                e.preventDefault();
                                handleNavClick(link.href);
                              }
                        }
                        {...(link.external && {
                          target: "_blank",
                          rel: "noopener noreferrer",
                        })}
                      >
                        {link.label}
                      </a>
                    ))}

                    {/* Mobile CTA */}
                    <Button
                      className="mt-6 rounded-full"
                      onClick={() => {
                        setIsOpen(false);
                        handlePilotOpen();
                      }}
                    >
                      Get Early Access
                    </Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </nav>
    </header>
  );
};

export default Header;

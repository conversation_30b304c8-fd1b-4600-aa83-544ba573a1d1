import React, { useEffect, useRef } from "react";
import { motion, useReducedMotion } from "framer-motion";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

const Section4Enhanced: React.FC = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const cloudRef = useRef<HTMLDivElement>(null);
  const threadsRef = useRef<SVGSVGElement>(null);
  const badgesRef = useRef<HTMLDivElement>(null);
  const prefersReduced = useReducedMotion();

  useEffect(() => {
    if (!sectionRef.current || prefersReduced) return;

    const section = sectionRef.current;
    const cloud = cloudRef.current;
    const threads = threadsRef.current;
    const badges = badgesRef.current;

    // Create section-specific timeline
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: section,
        start: "top top",
        end: "+=200%",
        scrub: 1,
        pin: true,
        pinSpacing: true,
        anticipatePin: 1,
        onUpdate: (self) => {
          const progress = self.progress;
          // Update any progress-based states here
          document.documentElement.style.setProperty(
            "--section4-progress",
            `${progress}`
          );
        },
      },
    });

    // Animation sequence
    // 0-20%: Cloud forms from particles
    if (cloud) {
      tl.fromTo(
        cloud,
        {
          opacity: 0,
          scale: 0.7,
          filter: "blur(20px)",
        },
        {
          opacity: 1,
          scale: 1,
          filter: "blur(0px)",
          duration: 0.2,
        },
        0
      );
    }

    // 20-50%: Mycelium threads grow
    if (threads) {
      const paths = threads.querySelectorAll(".thread-path");
      paths.forEach((path, i) => {
        const length = (path as SVGPathElement).getTotalLength();
        gsap.set(path, {
          strokeDasharray: length,
          strokeDashoffset: length,
        });
        tl.to(
          path,
          {
            strokeDashoffset: 0,
            duration: 0.3,
            ease: "power2.inOut",
          },
          0.2 + i * 0.05
        );
      });
    }

    // 50-80%: Badges appear with stagger
    if (badges) {
      const badgeElements = badges.querySelectorAll(".badge");
      tl.fromTo(
        badgeElements,
        {
          opacity: 0,
          scale: 0.8,
          y: 20,
        },
        {
          opacity: 1,
          scale: 1,
          y: 0,
          duration: 0.2,
          stagger: 0.05,
          ease: "back.out(1.7)",
        },
        0.5
      );
    }

    // 80-100%: Prepare exit transition
    tl.to(
      [cloud, threads, badges],
      {
        opacity: 0,
        scale: 0.95,
        duration: 0.2,
      },
      0.8
    );

    return () => {
      tl.kill();
    };
  }, [prefersReduced]);

  return (
    <section
      ref={sectionRef}
      data-section="4"
      className="relative min-h-screen overflow-hidden"
      style={{
        background:
          "linear-gradient(180deg, #faf7f2 0%, #f0f4ff 50%, #f6f7fb 100%)",
      }}
    >
      {/* Background atmosphere */}
      <div className="pointer-events-none absolute inset-0">
        <div
          className="absolute -inset-24 opacity-40"
          style={{
            background:
              "radial-gradient(ellipse at 30% 20%, rgba(147, 197, 253, 0.3), transparent 50%), radial-gradient(ellipse at 70% 80%, rgba(196, 181, 253, 0.3), transparent 50%)",
            animation: prefersReduced
              ? "none"
              : "pulse 8s ease-in-out infinite",
          }}
        />
      </div>

      <div className="container relative z-10 mx-auto grid min-h-screen items-center gap-12 px-8 md:px-16 lg:grid-cols-2">
        {/* Left: Text content */}
        <div>
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-6xl font-light leading-[1.05] text-slate-900 md:text-7xl lg:text-8xl"
          >
            Standards connect.
            <br />
            <em className="font-light italic">Systems align.</em>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="mt-6 max-w-lg text-xl text-slate-600"
          >
            Your data ecosystem unified through industry standards, creating
            seamless connections across every system.
          </motion.p>
        </div>

        {/* Right: Animated cloud & mycelium visualization */}
        <div className="relative flex h-full items-center justify-center">
          {/* Cloud formation */}
          <div
            ref={cloudRef}
            className="will-change-transform-opacity absolute inset-0 flex items-center justify-center"
          >
            <svg
              width="600"
              height="400"
              viewBox="0 0 600 400"
              className="h-auto w-full max-w-[600px]"
            >
              <defs>
                <filter id="cloudFilter">
                  <feGaussianBlur in="SourceGraphic" stdDeviation="3" />
                  <feColorMatrix
                    values="0 0 0 0 0.9
                            0 0 0 0 0.95
                            0 0 0 0 1
                            0 0 0 1 0"
                  />
                </filter>
                <linearGradient
                  id="cloudGradient"
                  x1="0%"
                  y1="0%"
                  x2="100%"
                  y2="100%"
                >
                  <stop offset="0%" stopColor="#e0e7ff" />
                  <stop offset="100%" stopColor="#cdd5f3" />
                </linearGradient>
              </defs>

              {/* Cloud shape */}
              <g filter="url(#cloudFilter)">
                <ellipse
                  cx="200"
                  cy="200"
                  rx="120"
                  ry="60"
                  fill="url(#cloudGradient)"
                  opacity="0.8"
                />
                <ellipse
                  cx="350"
                  cy="180"
                  rx="100"
                  ry="55"
                  fill="url(#cloudGradient)"
                  opacity="0.7"
                />
                <ellipse
                  cx="280"
                  cy="220"
                  rx="130"
                  ry="50"
                  fill="url(#cloudGradient)"
                  opacity="0.9"
                />
              </g>
            </svg>
          </div>

          {/* Mycelium threads */}
          <svg
            ref={threadsRef}
            className="pointer-events-none absolute inset-0 h-full w-full"
            viewBox="0 0 800 600"
            style={{ maxWidth: "800px", margin: "0 auto" }}
          >
            <defs>
              <linearGradient
                id="threadGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="100%"
              >
                <stop offset="0%" stopColor="#a5b4fc" stopOpacity="0.6" />
                <stop offset="100%" stopColor="#818cf8" stopOpacity="0.3" />
              </linearGradient>
            </defs>

            {/* Connection threads */}
            <path
              className="thread-path"
              d="M 200 300 Q 300 250 400 280 T 600 320"
              stroke="url(#threadGradient)"
              strokeWidth="2"
              fill="none"
              strokeLinecap="round"
            />
            <path
              className="thread-path"
              d="M 150 350 Q 280 300 380 330 T 580 280"
              stroke="url(#threadGradient)"
              strokeWidth="2"
              fill="none"
              strokeLinecap="round"
            />
            <path
              className="thread-path"
              d="M 250 280 Q 350 320 450 300 T 620 350"
              stroke="url(#threadGradient)"
              strokeWidth="2"
              fill="none"
              strokeLinecap="round"
            />
            <path
              className="thread-path"
              d="M 180 320 Q 320 280 420 310 T 590 330"
              stroke="url(#threadGradient)"
              strokeWidth="2"
              fill="none"
              strokeLinecap="round"
            />
          </svg>

          {/* Standards badges */}
          <div
            ref={badgesRef}
            className="pointer-events-none absolute inset-0 flex items-center justify-center"
          >
            <div className="relative h-full max-h-[400px] w-full max-w-[600px]">
              {/* ABDM Badge */}
              <div className="badge absolute left-[15%] top-[20%] rounded-xl border border-slate-200 bg-white px-4 py-2 shadow-lg">
                <span className="text-sm font-semibold text-slate-700">
                  ABDM
                </span>
              </div>

              {/* FHIR Badge */}
              <div className="badge absolute right-[20%] top-[35%] rounded-xl border border-slate-200 bg-white px-4 py-2 shadow-lg">
                <span className="text-sm font-semibold text-slate-700">
                  FHIR R4
                </span>
              </div>

              {/* DICOM Badge */}
              <div className="badge absolute bottom-[35%] left-[25%] rounded-xl border border-slate-200 bg-white px-4 py-2 shadow-lg">
                <span className="text-sm font-semibold text-slate-700">
                  DICOM
                </span>
              </div>

              {/* HL7 Badge */}
              <div className="badge absolute bottom-[25%] right-[15%] rounded-xl border border-slate-200 bg-white px-4 py-2 shadow-lg">
                <span className="text-sm font-semibold text-slate-700">
                  HL7
                </span>
              </div>

              {/* CSV Badge */}
              <div className="badge absolute left-[45%] top-[50%] rounded-xl border border-slate-200 bg-white px-4 py-2 shadow-lg">
                <span className="text-sm font-semibold text-slate-700">
                  CSV
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Pulse animation for background */}
      <style>{`
        @keyframes pulse {
          0%, 100% { opacity: 0.4; transform: scale(1); }
          50% { opacity: 0.6; transform: scale(1.05); }
        }
      `}</style>
    </section>
  );
};

export default Section4Enhanced;

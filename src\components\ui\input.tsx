import * as React from "react";

import { cn } from "@/lib/utils";

/**
 * Input Component
 *
 * Optimized input component with consistent sizing, improved accessibility,
 * and better visual feedback states.
 */

export interface InputProps extends React.ComponentProps<"input"> {
  error?: boolean;
  success?: boolean;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, error, success, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          // Base styles with optimized sizing
          "flex h-10 w-full rounded-lg border bg-background px-3 py-2 text-sm transition-all duration-200",
          // Typography and spacing
          "font-medium placeholder:text-muted-foreground/70",
          // File input styles
          "file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground",
          // Focus states
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-1",
          // Disabled state
          "disabled:cursor-not-allowed disabled:opacity-50",
          // Default border and focus
          !error && !success && "border-input focus-visible:ring-ring",
          // Error state
          error &&
            "border-destructive bg-destructive/5 focus-visible:ring-destructive/30",
          // Success state
          success &&
            "border-success bg-success/5 focus-visible:ring-success/30",
          className
        )}
        ref={ref}
        aria-invalid={error}
        {...props}
      />
    );
  }
);
Input.displayName = "Input";

export { Input };

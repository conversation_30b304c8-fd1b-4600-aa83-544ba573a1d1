import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>gle, Home, RefreshCw } from "lucide-react";
import React, { Component, ErrorInfo, ReactNode } from "react";

/**
 * Error Boundary Component
 *
 * Catches JavaScript errors anywhere in the child component tree and displays a fallback UI.
 * Includes error reporting, retry functionality, and development-mode error details.
 */
interface Props {
  /** Child components to wrap with error boundary */
  children: ReactNode;
  /** Custom fallback UI to display when an error occurs */
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorCount: number;
}

interface AnalyticsWindow extends Window {
  analytics?: {
    track: (event: string, properties: Record<string, unknown>) => void;
  };
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log to error reporting service
    console.error("ErrorBoundary caught an error:", error, errorInfo);

    this.setState((prevState) => ({
      errorInfo,
      errorCount: prevState.errorCount + 1,
    }));

    // Report to analytics/monitoring service
    if (typeof window !== "undefined") {
      const analyticsWindow = window as AnalyticsWindow;
      if (analyticsWindow.analytics) {
        analyticsWindow.analytics.track("Error Boundary Triggered", {
          error: error.toString(),
          componentStack: errorInfo.componentStack,
          errorCount: this.state.errorCount + 1,
        });
      }
    }
  }

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = "/";
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return <>{this.props.fallback}</>;
      }

      // Default error UI
      return (
        <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
          <div className="mx-auto w-full max-w-md p-6">
            <div className="rounded-xl bg-white p-6 text-center shadow-lg">
              <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>

              <h1 className="mb-2 text-heading-2 text-gray-900">
                Something went wrong
              </h1>

              <p className="mb-6 text-gray-600">
                We encountered an unexpected error. The issue has been logged
                and we'll look into it.
              </p>

              {/* Show error details in development */}
              {process.env.NODE_ENV === "development" && this.state.error && (
                <div className="mb-6 rounded-lg bg-gray-50 p-4 text-left">
                  <p className="mb-2 font-mono text-sm text-red-600">
                    {this.state.error.toString()}
                  </p>
                  {this.state.errorInfo && (
                    <details className="text-xs text-gray-600">
                      <summary className="cursor-pointer hover:text-gray-800">
                        Component Stack
                      </summary>
                      <pre className="mt-2 max-h-40 overflow-auto text-xs">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </details>
                  )}
                </div>
              )}

              <div className="flex justify-center gap-3">
                <Button
                  onClick={this.handleGoHome}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Home className="h-4 w-4" />
                  Go Home
                </Button>

                <Button
                  onClick={this.handleReset}
                  className="flex items-center gap-2 bg-indigo-600 text-white hover:bg-indigo-700"
                >
                  <RefreshCw className="h-4 w-4" />
                  Try Again
                </Button>
              </div>

              {this.state.errorCount > 2 && (
                <p className="mt-4 text-sm text-amber-600">
                  Multiple errors detected. Please contact support if this
                  persists.
                </p>
              )}
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Async error boundary for Suspense
export const AsyncErrorBoundary: React.FC<Props> = ({ children, fallback }) => {
  return (
    <ErrorBoundary fallback={fallback}>
      <React.Suspense
        fallback={
          <div className="flex min-h-screen items-center justify-center">
            <div className="text-center">
              <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-4 border-indigo-600 border-t-transparent" />
              <p className="text-gray-600">Loading...</p>
            </div>
          </div>
        }
      >
        {children}
      </React.Suspense>
    </ErrorBoundary>
  );
};

export default ErrorBoundary;

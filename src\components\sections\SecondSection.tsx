import React from "react";

/**
 * Second Section Component
 *
 * Technology-focused section with abstract geometric patterns
 * and organic background elements.
 */
const SecondSection: React.FC = () => {
  return (
    <section className="relative flex min-h-screen items-center justify-center overflow-hidden bg-gradient-orange">
      {/* Organic background shapes */}
      <div className="organic-blob right-1/3 top-1/3 h-80 w-80 bg-gradient-blue"></div>
      <div className="organic-blob bottom-1/4 left-1/4 h-96 w-96 bg-gradient-green"></div>

      <div className="container-optimized relative z-10">
        <div className="grid items-center gap-16 lg:grid-cols-2">
          <div className="space-y-6">
            <h2 className="text-display-large text-foreground">
              We build
              <br />
              <em className="font-light italic text-primary">technology.</em>
            </h2>
            <p className="text-body-large max-w-lg text-muted-foreground">
              We're pioneering the frontier of AI and what technology can do for
              humanity.
            </p>
          </div>

          <div className="relative">
            {/* Abstract geometric pattern representing technology */}
            <div className="grid grid-cols-3 gap-4">
              {[...Array(9)].map((_, i) => (
                <div
                  key={i}
                  className={`h-24 w-24 rounded-2xl ${
                    i % 3 === 0
                      ? "bg-white/20"
                      : i % 3 === 1
                        ? "bg-white/30"
                        : "bg-white/10"
                  } border border-white/20 backdrop-blur-sm`}
                ></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SecondSection;

import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import React, { memo, useCallback, useEffect, useRef, useState } from "react";

/**
 * Aura to Section 3 Transition Component
 *
 * Optimized transition from the aura section to section 3
 * with performance improvements and accessibility features.
 */

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

interface AuraToSection3TransitionProps {
  /** Optional CSS class name */
  className?: string;
  /** Whether to enable animations */
  enableAnimations?: boolean;
  /** Callback when transition completes */
  onTransitionComplete?: () => void;
}

const AuraToSection3Transition: React.FC<AuraToSection3TransitionProps> = memo(
  ({ className = "", enableAnimations = true, onTransitionComplete }) => {
    const transitionRef = useRef<HTMLDivElement>(null);
    const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
    const [isIntersecting, setIsIntersecting] = useState(false);

    // Check for reduced motion preference
    useEffect(() => {
      const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
      setPrefersReducedMotion(mediaQuery.matches);
      const handleChange = (e: MediaQueryListEvent) =>
        setPrefersReducedMotion(e.matches);
      mediaQuery.addEventListener("change", handleChange);
      return () => mediaQuery.removeEventListener("change", handleChange);
    }, []);

    // Intersection observer for performance
    useEffect(() => {
      if (!transitionRef.current) return;

      const observer = new IntersectionObserver(
        ([entry]) => setIsIntersecting(entry.isIntersecting),
        { threshold: 0.1, rootMargin: "50px" }
      );

      observer.observe(transitionRef.current);
      return () => observer.disconnect();
    }, []);

    const handleTransitionUpdate = useCallback(
      (progress: number) => {
        if (!enableAnimations || prefersReducedMotion) return;

        // Optimized AuraStack fade out
        const auraPlates = document.querySelectorAll("[data-s5-img]");
        auraPlates.forEach((plate, index) => {
          gsap.set(plate, {
            opacity: Math.max(0, 1 - progress * 1.2), // Slightly gentler fade
            y: progress * -(8 + index * 1.5), // Reduced movement
            scale: 1 - progress * 0.02, // Reduced scale change
            force3D: true,
          });
        });

        // Optimized Section 3 entry
        const section3 = document.querySelector("#s3");
        if (section3 && progress > 0.5) {
          // Later trigger
          const entryProgress = (progress - 0.5) / 0.5;

          // Animate the entire section
          gsap.set(section3, {
            opacity: entryProgress,
            scale: 0.99 + entryProgress * 0.01, // Reduced scale change
            force3D: true,
          });

          // Optimized stagger for modules
          const modules = section3.querySelectorAll(".badge");
          modules.forEach((module, i) => {
            const moduleProgress = Math.max(0, entryProgress - i * 0.08); // Faster stagger
            gsap.set(module, {
              opacity: moduleProgress,
              y: (1 - moduleProgress) * 12, // Reduced movement
              scale: 0.98 + moduleProgress * 0.02, // Reduced scale change
              force3D: true,
            });
          });
        }

        // Call completion callback
        if (progress >= 1 && onTransitionComplete) {
          onTransitionComplete();
        }
      },
      [enableAnimations, prefersReducedMotion, onTransitionComplete]
    );

    useEffect(() => {
      if (
        !transitionRef.current ||
        !isIntersecting ||
        prefersReducedMotion ||
        !enableAnimations
      )
        return;

      const scrollTrigger = ScrollTrigger.create({
        trigger: transitionRef.current,
        start: "top bottom",
        end: "bottom top",
        scrub: 0.8, // Smoother scrubbing
        refreshPriority: -1, // Lower priority for better performance
        onUpdate: (self) => handleTransitionUpdate(self.progress),
      });

      return () => scrollTrigger.kill();
    }, [
      isIntersecting,
      prefersReducedMotion,
      enableAnimations,
      handleTransitionUpdate,
    ]);

    return (
      <div
        ref={transitionRef}
        className={`relative h-[40vh] overflow-hidden ${className}`} // Reduced height, use className prop
        aria-hidden="true"
      >
        {/* Optimized gradient blend */}
        <div
          className="absolute inset-0"
          style={{
            background: `linear-gradient(180deg,
            rgba(255,239,179,0.25) 0%,
            rgba(6,182,212,0.15) 50%,
            rgba(6,182,212,0.25) 100%)`, // Reduced opacity for better performance
          }}
        />

        {/* Simplified connection lines - only if animations enabled */}
        {enableAnimations && !prefersReducedMotion && (
          <svg
            className="absolute inset-0 h-full w-full"
            viewBox="0 0 100 100"
            preserveAspectRatio="none"
            aria-hidden="true"
          >
            <defs>
              <linearGradient
                id="lineGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="100%"
              >
                <stop offset="0%" stopColor="#fbbf24" stopOpacity="0.2" />
                <stop offset="50%" stopColor="#06b6d4" stopOpacity="0.15" />
                <stop offset="100%" stopColor="#06b6d4" stopOpacity="0.3" />
              </linearGradient>
            </defs>

            {/* Simplified animated paths */}
            <path
              d="M 0 50 Q 50 35 100 50"
              stroke="url(#lineGradient)"
              strokeWidth="0.4"
              fill="none"
              opacity="0.5"
            >
              <animate
                attributeName="d"
                values="M 0 50 Q 50 35 100 50; M 0 50 Q 50 65 100 50; M 0 50 Q 50 35 100 50"
                dur="12s" // Slower animation
                repeatCount="indefinite"
              />
            </path>

            <path
              d="M 0 35 Q 50 50 100 65"
              stroke="url(#lineGradient)"
              strokeWidth="0.4"
              fill="none"
              opacity="0.3"
            >
              <animate
                attributeName="d"
                values="M 0 35 Q 50 50 100 65; M 0 40 Q 50 50 100 60; M 0 35 Q 50 50 100 65"
                dur="15s" // Slower animation
                repeatCount="indefinite"
              />
            </path>
          </svg>
        )}
      </div>
    );
  }
);

AuraToSection3Transition.displayName = "AuraToSection3Transition";

export default AuraToSection3Transition;
export type { AuraToSection3TransitionProps };

import react from "@vitejs/plugin-react-swc";
import path from "path";
import { defineConfig } from "vite";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const isProduction = mode === "production";

  return {
    server: {
      host: "::",
      port: 8080,
    },
    plugins: [react()],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    build: {
      outDir: "dist",
      sourcemap: !isProduction, // Source maps for development only
      chunkSizeWarningLimit: 1000,
      rollupOptions: {
        output: {
          // Optimized chunking for production
          manualChunks: isProduction
            ? (id) => {
                // Vendor chunks for better caching
                if (id.includes("node_modules")) {
                  if (id.includes("react") || id.includes("react-dom")) {
                    return "react";
                  }
                  if (id.includes("gsap")) {
                    return "gsap";
                  }
                  if (id.includes("framer-motion")) {
                    return "framer-motion";
                  }
                  if (id.includes("@radix-ui")) {
                    return "radix-ui";
                  }
                  if (id.includes("three")) {
                    return "three";
                  }
                  if (id.includes("supabase")) {
                    return "supabase";
                  }
                  return "vendor";
                }
                // App chunks
                if (id.includes("src/components/ui")) {
                  return "ui";
                }
                if (id.includes("src/components")) {
                  return "components";
                }
                if (id.includes("src/pages")) {
                  return "pages";
                }
              }
            : undefined,
          assetFileNames: "assets/[name].[hash].[ext]",
          chunkFileNames: "assets/[name].[hash].js",
          entryFileNames: "assets/[name].[hash].js",
        },
      },
      minify: isProduction ? "terser" : false,
      terserOptions: isProduction
        ? {
            compress: {
              drop_console: true,
              drop_debugger: true,
              pure_funcs: ["console.log", "console.info", "console.debug"],
            },
          }
        : undefined,
      target: ["es2020", "edge88", "firefox78", "chrome87", "safari14"],
      assetsInlineLimit: 4096,
      cssCodeSplit: true,
    },
    optimizeDeps: {
      include: [
        "react",
        "react-dom",
        "react-router-dom",
        "gsap",
        "framer-motion",
        "@tanstack/react-query",
      ],
      force: true,
    },
    define: {
      global: "globalThis",
    },
    esbuild: {
      target: "es2020",
      keepNames: true,
    },
    css: {
      postcss: "./postcss.config.js",
    },
    envPrefix: "VITE_",
  };
});

import { Button, ButtonProps } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import React, { ReactNode, useRef } from "react";

/**
 * Interactive Button Component
 *
 * Enhanced button with ripple effects, lift animations, and glow effects.
 * Provides tactile feedback for better user experience.
 */
interface InteractiveButtonProps extends ButtonProps {
  children: ReactNode;
  ripple?: boolean;
  lift?: boolean;
  glow?: boolean;
}

export const InteractiveButton: React.FC<InteractiveButtonProps> = ({
  children,
  className,
  ripple = true,
  lift = true,
  glow = false,
  onMouseDown,
  ...props
}) => {
  const buttonRef = useRef<HTMLButtonElement>(null);

  const createRipple = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (!ripple || !buttonRef.current) return;

    const button = buttonRef.current;
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    const rippleElement = document.createElement("span");
    rippleElement.style.cssText = `
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.6);
      transform: scale(0);
      animation: ripple 0.6s linear;
      left: ${x}px;
      top: ${y}px;
      width: ${size}px;
      height: ${size}px;
      pointer-events: none;
    `;

    button.style.position = "relative";
    button.style.overflow = "hidden";
    button.appendChild(rippleElement);

    setTimeout(() => {
      rippleElement.remove();
    }, 600);
  };

  const handleMouseDown = (event: React.MouseEvent<HTMLButtonElement>) => {
    createRipple(event);

    // Add press animation
    if (buttonRef.current) {
      buttonRef.current.classList.add("animate-button-press");
      setTimeout(() => {
        buttonRef.current?.classList.remove("animate-button-press");
      }, 150);
    }

    onMouseDown?.(event);
  };

  return (
    <Button
      ref={buttonRef}
      className={cn(
        "transition-all duration-300",
        lift && "hover-lift",
        glow && "hover-glow",
        className
      )}
      onMouseDown={handleMouseDown}
      {...props}
    >
      {children}
    </Button>
  );
};

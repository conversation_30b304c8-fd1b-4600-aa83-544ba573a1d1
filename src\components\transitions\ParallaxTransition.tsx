import {
  motion,
  useReducedMotion,
  useScroll,
  useTransform,
} from "framer-motion";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import React, { memo, useCallback, useEffect, useRef, useState } from "react";

/**
 * Parallax Transition Components
 *
 * Optimized collection of components for creating parallax scrolling effects
 * with performance improvements and accessibility features.
 */

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

interface ParallaxTransitionProps {
  children: React.ReactNode;
  offset?: number;
  className?: string;
  enableAnimations?: boolean;
}

export const ParallaxSection: React.FC<ParallaxTransitionProps> = memo(
  ({
    children,
    offset = 30, // Reduced default offset
    className = "",
    enableAnimations = true,
  }) => {
    const ref = useRef<HTMLDivElement>(null);
    const prefersReducedMotion = useReducedMotion();
    const shouldAnimate = enableAnimations && !prefersReducedMotion;

    const { scrollYProgress } = useScroll({
      target: ref,
      offset: ["start end", "end start"],
    });

    const y = useTransform(
      scrollYProgress,
      [0, 1],
      shouldAnimate ? [offset, -offset] : [0, 0]
    );
    const opacity = useTransform(
      scrollYProgress,
      [0, 0.3, 0.7, 1],
      shouldAnimate ? [0.85, 1, 1, 0.85] : [1, 1, 1, 1] // Less dramatic opacity change
    );

    return (
      <motion.div
        ref={ref}
        style={{
          y,
          opacity,
          willChange: shouldAnimate ? "transform, opacity" : "auto",
        }}
        className={`relative ${className}`}
      >
        {children}
      </motion.div>
    );
  }
);

interface FloatingElementProps {
  children: React.ReactNode;
  speed?: number;
  rotationSpeed?: number;
  className?: string;
  enableAnimations?: boolean;
}

export const FloatingElement: React.FC<FloatingElementProps> = memo(
  ({
    children,
    speed = 0.3, // Reduced default speed
    rotationSpeed = 0.05, // Reduced rotation speed
    className = "",
    enableAnimations = true,
  }) => {
    const ref = useRef<HTMLDivElement>(null);
    const prefersReducedMotion = useReducedMotion();
    const shouldAnimate = enableAnimations && !prefersReducedMotion;
    const { scrollY } = useScroll();

    const y = useTransform(scrollY, (value) =>
      shouldAnimate ? value * speed : 0
    );
    const rotate = useTransform(scrollY, (value) =>
      shouldAnimate ? value * rotationSpeed : 0
    );

    return (
      <motion.div
        ref={ref}
        style={{
          y,
          rotate,
          willChange: shouldAnimate ? "transform" : "auto",
        }}
        className={`absolute ${className}`}
      >
        {children}
      </motion.div>
    );
  }
);

interface ParallaxBackgroundProps {
  layers: Array<{
    image: string;
    speed: number;
    opacity?: number;
  }>;
  enableAnimations?: boolean;
}

export const ParallaxBackground: React.FC<ParallaxBackgroundProps> = memo(
  ({ layers, enableAnimations = true }) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const prefersReducedMotion = useReducedMotion();
    const shouldAnimate = enableAnimations && !prefersReducedMotion;
    const { scrollY } = useScroll();

    return (
      <div
        ref={containerRef}
        className="absolute inset-0 overflow-hidden"
        aria-hidden="true"
      >
        {layers.map((layer, index) => {
          const y = useTransform(scrollY, (value) =>
            shouldAnimate ? value * layer.speed : 0
          );

          return (
            <motion.div
              key={index}
              style={{
                y,
                backgroundImage: `url(${layer.image})`,
                opacity: layer.opacity || 1,
                willChange: shouldAnimate ? "transform" : "auto",
              }}
              className="absolute inset-0 bg-cover bg-center"
            />
          );
        })}
      </div>
    );
  }
);

interface RevealOnScrollProps {
  children: React.ReactNode;
  threshold?: number;
  className?: string;
  enableAnimations?: boolean;
}

export const RevealOnScroll: React.FC<RevealOnScrollProps> = memo(
  ({ children, threshold = 0.1, className = "", enableAnimations = true }) => {
    const ref = useRef<HTMLDivElement>(null);
    const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
    const [isIntersecting, setIsIntersecting] = useState(false);

    // Check for reduced motion preference
    useEffect(() => {
      const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
      setPrefersReducedMotion(mediaQuery.matches);
      const handleChange = (e: MediaQueryListEvent) =>
        setPrefersReducedMotion(e.matches);
      mediaQuery.addEventListener("change", handleChange);
      return () => mediaQuery.removeEventListener("change", handleChange);
    }, []);

    // Intersection observer for performance
    useEffect(() => {
      if (!ref.current) return;

      const observer = new IntersectionObserver(
        ([entry]) => setIsIntersecting(entry.isIntersecting),
        { threshold: 0.1, rootMargin: "50px" }
      );

      observer.observe(ref.current);
      return () => observer.disconnect();
    }, []);

    useEffect(() => {
      if (
        !ref.current ||
        !isIntersecting ||
        prefersReducedMotion ||
        !enableAnimations
      )
        return;

      const element = ref.current;

      gsap.fromTo(
        element,
        {
          opacity: 0,
          y: 30, // Reduced movement
          scale: 0.98, // Less dramatic scale
        },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.8, // Faster animation
          ease: "power2.out", // Gentler easing
          force3D: true,
          scrollTrigger: {
            trigger: element,
            start: `top ${80 + threshold * 20}%`,
            end: `bottom ${20 + threshold * 20}%`,
            toggleActions: "play none none reverse",
            refreshPriority: -1,
          },
        }
      );

      return () => {
        ScrollTrigger.getAll().forEach((trigger) => {
          if (trigger.trigger === element) trigger.kill();
        });
      };
    }, [threshold, isIntersecting, prefersReducedMotion, enableAnimations]);

    return (
      <div ref={ref} className={className}>
        {children}
      </div>
    );
  }
);

interface StickyRevealProps {
  children: React.ReactNode;
  className?: string;
  enableAnimations?: boolean;
}

export const StickyReveal: React.FC<StickyRevealProps> = memo(
  ({ children, className = "", enableAnimations = true }) => {
    const ref = useRef<HTMLDivElement>(null);
    const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
    const [isIntersecting, setIsIntersecting] = useState(false);

    // Check for reduced motion preference
    useEffect(() => {
      const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
      setPrefersReducedMotion(mediaQuery.matches);
      const handleChange = (e: MediaQueryListEvent) =>
        setPrefersReducedMotion(e.matches);
      mediaQuery.addEventListener("change", handleChange);
      return () => mediaQuery.removeEventListener("change", handleChange);
    }, []);

    // Intersection observer for performance
    useEffect(() => {
      if (!ref.current) return;

      const observer = new IntersectionObserver(
        ([entry]) => setIsIntersecting(entry.isIntersecting),
        { threshold: 0.1, rootMargin: "50px" }
      );

      observer.observe(ref.current);
      return () => observer.disconnect();
    }, []);

    const handleUpdate = useCallback(
      (progress: number) => {
        if (!ref.current || prefersReducedMotion || !enableAnimations) return;

        gsap.set(ref.current, {
          opacity: 1 - progress * 0.3, // Less dramatic fade
          scale: 1 - progress * 0.02, // Reduced scale change
          force3D: true,
        });
      },
      [prefersReducedMotion, enableAnimations]
    );

    useEffect(() => {
      if (
        !ref.current ||
        !isIntersecting ||
        prefersReducedMotion ||
        !enableAnimations
      )
        return;

      const element = ref.current;

      const scrollTrigger = ScrollTrigger.create({
        trigger: element,
        start: "top top",
        end: "bottom top",
        pin: true,
        pinSpacing: false,
        scrub: 0.8, // Smoother scrubbing
        refreshPriority: -1,
        onUpdate: (self) => handleUpdate(self.progress),
      });

      return () => scrollTrigger.kill();
    }, [isIntersecting, prefersReducedMotion, enableAnimations, handleUpdate]);

    return (
      <div ref={ref} className={`relative ${className}`}>
        {children}
      </div>
    );
  }
);

// Set display names for debugging
ParallaxSection.displayName = "ParallaxSection";
FloatingElement.displayName = "FloatingElement";
ParallaxBackground.displayName = "ParallaxBackground";
RevealOnScroll.displayName = "RevealOnScroll";
StickyReveal.displayName = "StickyReveal";

export default {
  ParallaxSection,
  FloatingElement,
  ParallaxBackground,
  RevealOnScroll,
  StickyReveal,
};

export type {
  FloatingElementProps,
  ParallaxBackgroundProps,
  ParallaxTransitionProps,
  RevealOnScrollProps,
  StickyRevealProps,
};

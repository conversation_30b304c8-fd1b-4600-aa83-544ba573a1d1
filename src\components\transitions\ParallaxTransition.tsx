import { motion, useScroll, useTransform } from "framer-motion";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import React, { useEffect, useRef } from "react";

/**
 * Parallax Transition Components
 *
 * Collection of components for creating parallax scrolling effects
 * using both Framer Motion and GSAP animations.
 */

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

interface ParallaxTransitionProps {
  children: React.ReactNode;
  offset?: number;
  className?: string;
}

export const ParallaxSection: React.FC<ParallaxTransitionProps> = ({
  children,
  offset = 50,
  className = "",
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  const y = useTransform(scrollYProgress, [0, 1], [offset, -offset]);
  const opacity = useTransform(
    scrollYProgress,
    [0, 0.3, 0.7, 1],
    [0.8, 1, 1, 0.8]
  );

  return (
    <motion.div
      ref={ref}
      style={{ y, opacity }}
      className={`relative ${className}`}
    >
      {children}
    </motion.div>
  );
};

interface FloatingElementProps {
  children: React.ReactNode;
  speed?: number;
  rotationSpeed?: number;
  className?: string;
}

export const FloatingElement: React.FC<FloatingElementProps> = ({
  children,
  speed = 0.5,
  rotationSpeed = 0.1,
  className = "",
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollY } = useScroll();

  const y = useTransform(scrollY, (value) => value * speed);
  const rotate = useTransform(scrollY, (value) => value * rotationSpeed);

  return (
    <motion.div
      ref={ref}
      style={{ y, rotate }}
      className={`absolute ${className}`}
    >
      {children}
    </motion.div>
  );
};

interface ParallaxBackgroundProps {
  layers: Array<{
    image: string;
    speed: number;
    opacity?: number;
  }>;
}

export const ParallaxBackground: React.FC<ParallaxBackgroundProps> = ({
  layers,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollY } = useScroll();

  return (
    <div ref={containerRef} className="absolute inset-0 overflow-hidden">
      {layers.map((layer, index) => {
        const y = useTransform(scrollY, (value) => value * layer.speed);

        return (
          <motion.div
            key={index}
            style={{
              y,
              backgroundImage: `url(${layer.image})`,
              opacity: layer.opacity || 1,
            }}
            className="absolute inset-0 bg-cover bg-center"
          />
        );
      })}
    </div>
  );
};

interface RevealOnScrollProps {
  children: React.ReactNode;
  threshold?: number;
  className?: string;
}

export const RevealOnScroll: React.FC<RevealOnScrollProps> = ({
  children,
  threshold = 0.1,
  className = "",
}) => {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!ref.current) return;

    const element = ref.current;

    gsap.fromTo(
      element,
      {
        opacity: 0,
        y: 50,
        scale: 0.95,
      },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 1,
        ease: "power3.out",
        scrollTrigger: {
          trigger: element,
          start: `top ${80 + threshold * 20}%`,
          end: `bottom ${20 + threshold * 20}%`,
          toggleActions: "play none none reverse",
        },
      }
    );

    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    };
  }, [threshold]);

  return (
    <div ref={ref} className={className}>
      {children}
    </div>
  );
};

interface StickyRevealProps {
  children: React.ReactNode;
  className?: string;
}

export const StickyReveal: React.FC<StickyRevealProps> = ({
  children,
  className = "",
}) => {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!ref.current) return;

    const element = ref.current;

    ScrollTrigger.create({
      trigger: element,
      start: "top top",
      end: "bottom top",
      pin: true,
      pinSpacing: false,
      scrub: 1,
      onUpdate: (self) => {
        const progress = self.progress;
        gsap.set(element, {
          opacity: 1 - progress * 0.5,
          scale: 1 - progress * 0.05,
        });
      },
    });

    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    };
  }, []);

  return (
    <div ref={ref} className={`relative ${className}`}>
      {children}
    </div>
  );
};

export default {
  ParallaxSection,
  FloatingElement,
  ParallaxBackground,
  RevealOnScroll,
  StickyReveal,
};

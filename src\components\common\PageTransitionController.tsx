import React, { useEffect, useRef } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { motion, useReducedMotion } from "framer-motion";

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

interface PageTransitionControllerProps {
  children: React.ReactNode;
}

const PageTransitionController: React.FC<PageTransitionControllerProps> = ({
  children,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const prefersReduced = useReducedMotion();

  useEffect(() => {
    if (!containerRef.current || prefersReduced) return;

    // Page entry animation
    const sections = containerRef.current.querySelectorAll("section");

    // Initial state for all sections
    sections.forEach((section, index) => {
      if (index === 0) {
        // Hero section starts visible
        gsap.set(section, { opacity: 1, y: 0 });
      } else {
        // Other sections start invisible
        gsap.set(section, { opacity: 0, y: 50 });
      }
    });

    // Create scroll-triggered animations for each section
    sections.forEach((section, index) => {
      if (index === 0) return; // Skip hero

      ScrollTrigger.create({
        trigger: section,
        start: "top 80%",
        end: "top 20%",
        onEnter: () => {
          gsap.to(section, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power2.out",
          });
        },
        once: true, // Only animate once
      });
    });

    // Smooth transitions between adjacent sections
    for (let i = 0; i < sections.length - 1; i++) {
      const currentSection = sections[i];
      const nextSection = sections[i + 1];

      ScrollTrigger.create({
        trigger: currentSection,
        start: "bottom bottom",
        end: "bottom top",
        scrub: 1,
        onUpdate: (self) => {
          const progress = self.progress;

          // Fade out current section gradually
          if (progress > 0.7) {
            const fadeProgress = (progress - 0.7) / 0.3;
            gsap.set(currentSection, {
              opacity: 1 - fadeProgress * 0.2, // Fade to 0.8
            });
          }

          // Start revealing next section
          if (progress > 0.5 && nextSection) {
            const revealProgress = (progress - 0.5) / 0.5;
            gsap.set(nextSection, {
              opacity: Math.min(1, revealProgress * 1.2),
              y: (1 - revealProgress) * 30,
            });
          }
        },
      });
    }

    return () => {
      ScrollTrigger.getAll().forEach((st) => st.kill());
    };
  }, [prefersReduced]);

  // Page load animation
  useEffect(() => {
    if (prefersReduced) return;

    // Animate page elements on load
    const tl = gsap.timeline({
      defaults: { ease: "power2.out" },
    });

    // Header animation
    const header = document.querySelector("header");
    if (header) {
      tl.from(header, {
        y: -50,
        opacity: 0,
        duration: 0.6,
      });
    }

    // Hero content animation
    const heroTitle = document.querySelector("h1");
    const heroText = document.querySelector("h1 + p");
    const heroCTA = document.querySelector("h1 + p + div");

    if (heroTitle) {
      tl.from(
        heroTitle,
        {
          y: 30,
          opacity: 0,
          duration: 0.8,
        },
        "-=0.3"
      );
    }

    if (heroText) {
      tl.from(
        heroText,
        {
          y: 20,
          opacity: 0,
          duration: 0.6,
        },
        "-=0.4"
      );
    }

    if (heroCTA) {
      tl.from(
        heroCTA,
        {
          y: 20,
          opacity: 0,
          duration: 0.6,
        },
        "-=0.3"
      );
    }

    return () => {
      tl.kill();
    };
  }, [prefersReduced]);

  return (
    <motion.div
      ref={containerRef}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="transition-controller"
    >
      {children}
    </motion.div>
  );
};

export default PageTransitionController;

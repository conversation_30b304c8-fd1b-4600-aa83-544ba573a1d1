import HeroBackground from "@/components/specialized/HeroBackground";
import OncologyFlow from "@/components/specialized/OncologyFlow";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { postLead } from "@/lib/lead";
import React, { useState } from "react";

/**
 * Hero Section Component
 *
 * Main landing section with interactive oncology flow visualization,
 * email capture form, and demo video modal.
 */
const Hero: React.FC = () => {
  const [email, setEmail] = useState("");
  const [open, setOpen] = useState(false);
  const [error, setError] = useState("");

  const handleWatch = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    const valid = /[^\s@]+@[^\s@]+\.[^\s@]+/.test(email);
    if (!valid) {
      setError("Please enter a valid work email");
      return;
    }
    setError("");
    setOpen(true);
    try {
      await postLead({ source: "demo", email, role: "unknown" });
    } catch (error) {
      console.warn("Failed to post lead:", error);
    }
  };

  return (
    <section
      id="hero"
      className="relative min-h-screen overflow-hidden"
      style={{
        // Responsive layout tokens
        ["--hero-axis-x" as any]: "clamp(50vw, 66vw, 75vw)",
        ["--card-w" as any]: "clamp(280px, 90vw, 520px)",
        ["--axis-gap" as any]: "clamp(12px, 3vw, 24px)",
        ["--axis-gap-right" as any]: "clamp(20px, 5vw, 40px)",
        ["--axis-gap-left" as any]: "clamp(8px, 2vw, 16px)",
        ["--branch-offset" as any]: "clamp(60px, 15vw, 140px)",
        // Responsive vertical rhythm
        ["--top-y1" as any]: "clamp(60px, 15vh, 120px)",
        ["--top-y2" as any]: "clamp(120px, 25vh, 252px)",
        ["--gate-y" as any]: "clamp(180px, 35vh, 348px)",
        ["--bot-y1" as any]: "clamp(240px, 45vh, 452px)",
        ["--bot-y2" as any]: "clamp(300px, 60vh, 596px)",
        ["--flow-h" as any]: "clamp(400px, 80vh, 720px)",
        ["--card-shadow" as any]: "0 12px 40px rgba(2, 6, 23, 0.12)",
      }}
    >
      <HeroBackground />

      <div className="container-optimized relative">
        <div className="flex min-h-screen items-center py-20 lg:py-0">
          <div className="grid w-full items-center gap-12 lg:grid-cols-2 lg:gap-16">
            {/* Left side - Hero content */}
            <div className="mx-auto max-w-2xl text-center lg:mx-0 lg:text-left">
              <h1 className="text-display-hero mb-6 text-foreground">
                <span className="block">Stop hunting</span>
                <span className="block">
                  for <em className="italic text-primary">patient data</em>
                </span>
                <span className="block">across systems.</span>
              </h1>
              <p className="text-body-large mx-auto max-w-xl text-muted-foreground lg:mx-0">
                Get complete clinical context in 60 seconds. AI‑powered
                summaries from all your systems, ready for tumor board
                decisions.
              </p>
              <form
                onSubmit={handleWatch}
                className="mx-auto mt-8 flex max-w-md flex-col gap-4 lg:mx-0 lg:max-w-none lg:flex-row lg:items-center"
              >
                <label htmlFor="hero-email" className="sr-only">
                  Enter email
                </label>
                <input
                  id="hero-email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter work email"
                  className={`h-12 w-full rounded-lg border bg-white px-4 text-base text-slate-900 placeholder-slate-500 transition-all duration-200 focus:outline-none focus:ring-2 lg:w-80 ${error ? "border-red-400 focus:ring-red-300" : "border-slate-300 focus:ring-primary/30"}`}
                  aria-invalid={!!error}
                />
                <button
                  type="submit"
                  onClick={handleWatch}
                  className="h-12 whitespace-nowrap rounded-lg bg-primary px-6 font-medium text-primary-foreground shadow-md transition-all duration-200 hover:bg-primary-dark hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-primary/30"
                >
                  Watch the demo
                </button>
              </form>
              {error && (
                <p className="mt-2 text-center text-sm text-red-500 sm:text-left">
                  {error}
                </p>
              )}
            </div>

            {/* Placeholder column to reserve space on large screens */}
            <div className="hidden lg:block" />
          </div>
        </div>
      </div>

      {/* Flow overlay spanning the hero for precise axis alignment */}
      <div
        className="pointer-events-none absolute inset-0 z-10 flex items-center"
        style={{ paddingTop: "0" }}
      >
        <OncologyFlow />
      </div>

      {/* Responsive video dialog */}
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="h-[85vh] w-[95vw] max-w-[95vw] rounded-lg bg-black p-0 sm:h-[80vh] sm:w-[90vw] sm:max-w-[90vw] sm:rounded-xl lg:h-[85vh] lg:w-[85vw] lg:max-w-[85vw]">
          {open && (
            <iframe
              key="demo-video"
              className="h-full w-full rounded-lg sm:rounded-xl"
              src="https://www.youtube.com/embed/CDz3KzEaDSU?autoplay=1&rel=0&modestbranding=1"
              title="Entheory demo"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowFullScreen
            />
          )}
        </DialogContent>
      </Dialog>
    </section>
  );
};

export default Hero;

export const CONTRAST_RATIOS = {
  AA_NORMAL: 4.5,
  AA_LARGE: 3.0,
  AAA_NORMAL: 7.0,
  AAA_LARGE: 4.5,
} as const;

export const HEALTHCARE_COLORS = {
  critical: "hsl(0 75% 55%)",
  warning: "hsl(45 100% 50%)",
  success: "hsl(140 65% 42%)",
  info: "hsl(210 90% 55%)",
  neutral: "hsl(200 15% 60%)",
} as const;

export const BRAND_COLORS = {
  primary: "hsl(220 85% 58%)",
  primaryLight: "hsl(220 85% 68%)",
  primaryDark: "hsl(220 85% 48%)",
  secondary: "hsl(45 90% 60%)",
} as const;

export const TRUST_COLORS = {
  verified: "hsl(140 70% 40%)",
  secure: "hsl(220 85% 50%)",
  compliant: "hsl(260 60% 50%)",
} as const;

export function hslToRgb(hsl: string): [number, number, number] {
  const match = hsl.match(/hsl\((\d+)\s+(\d+)%\s+(\d+)%\)/);
  if (!match) throw new Error("Invalid HSL format");

  const h = parseInt(match[1]) / 360;
  const s = parseInt(match[2]) / 100;
  const l = parseInt(match[3]) / 100;

  const hue2rgb = (p: number, q: number, t: number) => {
    if (t < 0) t += 1;
    if (t > 1) t -= 1;
    if (t < 1 / 6) return p + (q - p) * 6 * t;
    if (t < 1 / 2) return q;
    if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
    return p;
  };

  let r, g, b;

  if (s === 0) {
    r = g = b = l;
  } else {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    r = hue2rgb(p, q, h + 1 / 3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1 / 3);
  }

  return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
}

export function getLuminance(rgb: [number, number, number]): number {
  const [r, g, b] = rgb.map((c) => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });

  return 0.2126 * r + 0.7152 * g + 0.0722 * b;
}

export function getContrastRatio(color1: string, color2: string): number {
  const rgb1 = hslToRgb(color1);
  const rgb2 = hslToRgb(color2);

  const lum1 = getLuminance(rgb1);
  const lum2 = getLuminance(rgb2);

  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);

  return (brightest + 0.05) / (darkest + 0.05);
}

export function isAccessible(
  foreground: string,
  background: string,
  level: "AA" | "AAA" = "AA",
  size: "normal" | "large" = "normal"
): boolean {
  const ratio = getContrastRatio(foreground, background);
  const threshold =
    level === "AA"
      ? size === "normal"
        ? CONTRAST_RATIOS.AA_NORMAL
        : CONTRAST_RATIOS.AA_LARGE
      : size === "normal"
        ? CONTRAST_RATIOS.AAA_NORMAL
        : CONTRAST_RATIOS.AAA_LARGE;

  return ratio >= threshold;
}

export function generateAccessibleVariant(
  baseColor: string,
  background: string,
  targetRatio: number = CONTRAST_RATIOS.AA_NORMAL
): string {
  const match = baseColor.match(/hsl\((\d+)\s+(\d+)%\s+(\d+)%\)/);
  if (!match) throw new Error("Invalid HSL format");

  const h = parseInt(match[1]);
  const s = parseInt(match[2]);
  let l = parseInt(match[3]);

  let attempts = 0;
  const maxAttempts = 100;

  while (attempts < maxAttempts) {
    const testColor = `hsl(${h} ${s}% ${l}%)`;
    const ratio = getContrastRatio(testColor, background);

    if (ratio >= targetRatio) {
      return testColor;
    }

    const bgLuminance = getLuminance(hslToRgb(background));
    if (bgLuminance > 0.5) {
      l = Math.max(0, l - 2);
    } else {
      l = Math.min(100, l + 2);
    }

    attempts++;
  }

  return baseColor;
}

export function validateColorPalette() {
  const results = {
    passed: [] as string[],
    failed: [] as string[],
    warnings: [] as string[],
  };

  const combinations = [
    ["hsl(220 20% 8%)", "hsl(0 0% 100%)", "Primary text on light background"],
    ["hsl(220 10% 42%)", "hsl(0 0% 100%)", "Muted text on light background"],
    ["hsl(220 85% 58%)", "hsl(0 0% 100%)", "Primary brand on light background"],
    ["hsl(0 75% 55%)", "hsl(0 0% 100%)", "Critical status on light background"],
    [
      "hsl(140 65% 42%)",
      "hsl(0 0% 100%)",
      "Success status on light background",
    ],
  ];

  combinations.forEach(([fg, bg, description]) => {
    const ratio = getContrastRatio(fg, bg);
    const isAA = ratio >= CONTRAST_RATIOS.AA_NORMAL;
    const isAAA = ratio >= CONTRAST_RATIOS.AAA_NORMAL;

    if (isAAA) {
      results.passed.push(`✅ ${description} (${ratio.toFixed(2)}:1 - AAA)`);
    } else if (isAA) {
      results.passed.push(`✅ ${description} (${ratio.toFixed(2)}:1 - AA)`);
    } else {
      results.failed.push(`❌ ${description} (${ratio.toFixed(2)}:1 - Failed)`);
    }
  });

  return results;
}

export const colorUtilities = {
  status: {
    critical:
      "text-medical-critical bg-medical-critical-light border-medical-critical/20",
    warning:
      "text-medical-warning bg-medical-warning-light border-medical-warning/20",
    success:
      "text-medical-success bg-medical-success-light border-medical-success/20",
    info: "text-medical-info bg-medical-info-light border-medical-info/20",
  },

  trust: {
    verified:
      "text-trust-verified bg-trust-verified/10 border-trust-verified/20",
    secure: "text-trust-secure bg-trust-secure/10 border-trust-secure/20",
    compliant:
      "text-trust-compliant bg-trust-compliant/10 border-trust-compliant/20",
  },

  interactive: {
    hover: "hover:bg-interactive-hover",
    active: "active:bg-interactive-active",
    focus: "focus:ring-2 focus:ring-interactive-focus focus:ring-offset-2",
    disabled: "disabled:bg-interactive-disabled disabled:text-neutral-400",
  },
} as const;

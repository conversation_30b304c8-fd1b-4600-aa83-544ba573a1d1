import {
  PageLoadingState,
  ScrollProgressBar,
} from "@/components/common/LoadingStates";
import PageTransitionController from "@/components/common/PageTransitionController";
import Header from "@/components/layout/Header";
import Hero from "@/components/sections/Hero";
import AuraBranches from "@/components/specialized/AuraBranches";
import AuraStack from "@/components/specialized/AuraStack";
import AuraToSection3Transition from "@/components/transitions/AuraToSection3Transition";
import CrossfadeSection from "@/components/transitions/CrossfadeSection";
import HeroToAuraTransition from "@/components/transitions/HeroToAuraTransition";
import UnifiedScrollContainer from "@/components/UnifiedScrollContainer";
import { AnimatePresence, motion } from "framer-motion";
import React, { useEffect, useState } from "react";

/**
 * Index Page Component
 *
 * Main landing page with hero section, aura stack visualization,
 * and interactive branch components. Includes asset preloading
 * and smooth page transitions.
 */
const Index: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const preloadAssets = async () => {
      const criticalAssets = [
        "/aura-stack/00-base.png",
        "/aura-stack/01-data.png",
        "/aura-stack/02-context.png",
        "/aura-stack/03-insight.png",
        "/aura-stack/04-coaching.png",
        "/aura-stack/05-orchestration.png",
        "/aura-stack/06-aura.png",
        "/aura-stack/07-cap.png",
        "/media/section5/frame-a-rivers.svg",
        "/media/section5/frame-b-mycelium.svg",
        "/media/section5/frame-c-fireflies.svg",
        "/media/section5/frame-d-emblem.svg",
      ];
      const loadPromises = criticalAssets.map((src) => {
        return new Promise((resolve, reject) => {
          const img = new Image();
          img.onload = () => resolve(src);
          img.onerror = () => reject(new Error(`Failed to load ${src}`));
          img.src = src;
          setTimeout(() => reject(new Error(`Timeout loading ${src}`)), 5000);
        });
      });
      try {
        const results = await Promise.allSettled(loadPromises);
        const failed = results.filter((r) => r.status === "rejected").length;
        if (failed > 0) {
          console.warn(`${failed} assets failed to preload`);
        }
      } catch (error) {
        console.warn("Asset preloading error:", error);
      }
      setTimeout(() => {
        setIsLoading(false);
      }, 800);
    };
    preloadAssets();
  }, []);

  return (
    <>
      <AnimatePresence>{isLoading && <PageLoadingState />}</AnimatePresence>
      {!isLoading && (
        <PageTransitionController>
          <ScrollProgressBar />
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="min-h-screen"
          >
            <Header />

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <Hero />
            </motion.div>
            <HeroToAuraTransition />
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <AuraStack />
            </motion.div>
            <AuraToSection3Transition />
            <CrossfadeSection id="s3">
              <AuraBranches />
            </CrossfadeSection>
            <UnifiedScrollContainer />
          </motion.div>
        </PageTransitionController>
      )}
    </>
  );
};

export default Index;

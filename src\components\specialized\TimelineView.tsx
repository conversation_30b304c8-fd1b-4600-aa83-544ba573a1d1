"use client";

import { useAnalytics } from "@/hooks/useAnalytics";
import { motion, useReducedMotion } from "framer-motion";
import React from "react";

/**
 * Timeline View Component
 *
 * Interactive timeline visualization with filtering capabilities.
 * Displays patient history, labs, and imaging data with analytics tracking.
 */

export const TimelineView: React.FC = () => {
  const prefersReduced = useReducedMotion();
  const { trackEvent } = useAnalytics();

  // Filters / state
  const [query, setQuery] = React.useState("");
  const [range, setRange] = React.useState<"30" | "90" | "180" | "since">("90");
  const [modalities, setModalities] = React.useState<{
    history: boolean;
    labs: boolean;
    imaging: boolean;
  }>({ history: true, labs: true, imaging: true });
  const [fullView, setFullView] = React.useState(true);
  const [labsFull, setLabsFull] = React.useState(true);
  const [density, setDensity] = React.useState<"comfortable" | "compact">(
    "comfortable"
  );
  const [openItem, setOpenItem] = React.useState<null | {
    row: "history" | "labs" | "imaging";
    id: string;
  }>(null);
  const [sheetTab, setSheetTab] = React.useState<"summary" | "full" | "prov">(
    "summary"
  );
  const [lastSyncedSec, setLastSyncedSec] = React.useState(18);
  const [pulseUntil, setPulseUntil] = React.useState<number>(0);

  React.useEffect(() => {
    const id = setInterval(() => {
      setLastSyncedSec((s) => (s >= 999 ? 0 : s + 1));
    }, 1000);
    return () => clearInterval(id);
  }, []);

  React.useEffect(() => {
    setLabsFull(fullView);
  }, [fullView]);

  // Sample data (dates in 2025-08)
  type TimelineItem = {
    id: string;
    date: string;
    title: string;
    source: "EMR" | "LIS" | "PACS";
    facility: string;
    specimenSite?: string;
  };
  const historyItems: TimelineItem[] = [
    {
      id: "h2",
      date: "2025-08-24",
      title: "Oncology Consult — Dr R. Mehta",
      source: "EMR",
      facility: "Fortis",
    },
    {
      id: "h1",
      date: "2025-08-12",
      title: "Discharge Summary — MRN AP‑2025‑847",
      source: "EMR",
      facility: "Apollo",
    },
  ];
  const labItems: TimelineItem[] = [
    {
      id: "l2",
      date: "2025-08-20",
      title: "Pathology: Breast Core Biopsy — ER+/PR+, HER2–",
      source: "LIS",
      facility: "SRL",
      specimenSite: "Left breast core",
    },
    {
      id: "l1",
      date: "2025-08-18",
      title: "CBC w/ diff — Hgb 10.4 (L), ANC 1.2 (L)",
      source: "LIS",
      facility: "SRL",
    },
  ];
  const imagingItems: TimelineItem[] = [
    {
      id: "i2",
      date: "2025-08-22",
      title: "CT Thorax/Abdomen — No distant mets; Ax LN 12mm",
      source: "PACS",
      facility: "Max",
    },
    {
      id: "i1",
      date: "2025-08-10",
      title: "Mammogram — Suspicious calcifications BIRADS 4b",
      source: "PACS",
      facility: "Apollo",
    },
  ];

  const Section: React.FC<{
    label: string;
    items: TimelineItem[];
    row: "history" | "labs" | "imaging";
  }> = ({ label, items, row }) => (
    <div className="rounded-lg bg-white ring-1 ring-slate-200">
      <div className="flex items-center justify-between rounded-t-lg bg-slate-50/70 px-4 py-2">
        <div className="text-xs font-semibold uppercase tracking-wider text-slate-700">
          {label}
        </div>
        <div className="text-[11px] text-slate-500">
          {items.length} items • Updated 2m ago
        </div>
      </div>
      <ul className="divide-y divide-slate-200">
        {[...items]
          .sort((a, b) => a.date.localeCompare(b.date))
          .map((it, idx, arr) => {
            const showDate = idx === 0 || it.date !== arr[idx - 1].date;
            return (
              <li
                key={it.id}
                className={`px-4 ${density === "compact" ? "py-2" : "py-3"} flex items-start gap-3`}
              >
                <div className="w-28 shrink-0 text-[13px] text-slate-500">
                  {showDate ? it.date : ""}
                </div>
                <div className="min-w-0 flex-1 truncate text-base text-slate-900">
                  <span className="truncate">{it.title}</span>
                  <div className="mt-0.5 flex flex-wrap gap-1">
                    <span className="rounded-full border border-slate-300 px-1.5 py-0.5 text-[11px] text-slate-700">
                      {it.source} — {it.facility}
                    </span>
                    {it.specimenSite && (
                      <span className="rounded-full border border-slate-300 px-1.5 py-0.5 text-[11px] text-slate-700">
                        Specimen: {it.specimenSite}
                      </span>
                    )}
                  </div>
                </div>
                <button
                  className="rounded-md border border-slate-300 px-2 py-1 text-sm hover:bg-slate-50"
                  onClick={() => setOpenItem({ row, id: it.id })}
                >
                  Open
                </button>
              </li>
            );
          })}
      </ul>
    </div>
  );

  return (
    <motion.div
      initial={prefersReduced ? false : { opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -30 }}
      transition={{ duration: 0.6, ease: [0.22, 1, 0.36, 1] }}
      className="flex h-full flex-col"
    >
      <div className="relative px-8 pb-6 pt-24 md:px-16 md:pb-8 md:pt-32">
        <div className="flex flex-col justify-center md:flex-row md:items-start md:gap-20">
          <h2 className="max-w-[18ch] text-center text-6xl font-light leading-[1.05] text-white md:text-left md:text-7xl lg:text-8xl">
            One Timeline,
            <br />
            <span className="font-light italic">One Patient</span>
          </h2>
          <div className="mt-6 max-w-2xl md:mt-4">
            <p className="text-lg font-light text-white/90">
              Longitudinal view with provenance — history, labs & imaging
              stitched into context.
            </p>
            <div
              role="list"
              aria-label="Capabilities"
              className="mt-3 flex flex-wrap gap-2"
            >
              {["Source badges", "Provenance links", "Real‑time sync"].map(
                (cap) => (
                  <span
                    key={cap}
                    role="listitem"
                    className="inline-flex items-center gap-1 rounded-full border border-white/60 bg-white/90 px-3 py-1 text-xs font-medium text-slate-800 backdrop-blur-md"
                  >
                    {cap}
                  </span>
                )
              )}
            </div>
          </div>
        </div>
        <div className="absolute left-6 top-1/2 hidden -translate-y-1/2 md:left-12 md:block">
          <div className="inline-flex items-center gap-2 rounded-[26px] border border-white/70 bg-white/90 px-7 py-3.5 shadow-[0_10px_28px_rgba(0,0,0,0.12)] backdrop-blur-2xl">
            <div
              className="h-2 w-2 rounded-full"
              style={{ backgroundColor: "#10b981" }}
            />
            <div className="flex items-baseline gap-1">
              <span className="text-[12px] font-bold tracking-wider text-slate-800">
                ONE TIMELINE
              </span>
              <span className="text-[11px] font-medium tracking-wide text-slate-600">
                ONE PATIENT
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="relative min-h-0 w-full flex-1 overflow-hidden rounded-t-2xl bg-white/95 shadow-xl ring-1 ring-slate-200/60 backdrop-blur-sm">
        {/* Toolbar */}
        <div className="border-b border-slate-200/60 bg-white/80 px-6 py-2.5 backdrop-blur-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-2">
                <img src="/logo.png" alt="Entheory" className="h-6 w-auto" />
              </div>
              <nav className="flex items-center gap-6">
                <button className="text-sm text-gray-600 hover:text-gray-900">
                  Records
                </button>
                <button className="text-sm font-medium text-emerald-600">
                  Timeline
                </button>
                <button className="text-sm text-gray-600 hover:text-gray-900">
                  Summary
                </button>
                <button className="text-sm text-gray-600 hover:text-gray-900">
                  Actions
                </button>
              </nav>
            </div>
            <div className="flex items-center gap-2">
              <div className="relative">
                <button className="rounded-l-lg bg-emerald-500 px-3 py-1.5 text-sm font-medium text-white hover:bg-emerald-600 focus:outline-none focus-visible:ring-2 focus-visible:ring-emerald-200">
                  Export
                </button>
                <button
                  className="rounded-r-lg border-l border-white/30 bg-emerald-500 px-2 py-1.5 text-sm font-medium text-white hover:bg-emerald-600"
                  aria-label="More export options"
                >
                  ▾
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Filter bar */}
        <div className="border-b border-slate-200/60 bg-white/70 px-6 py-3 backdrop-blur-sm">
          <div className="grid grid-cols-12 items-center gap-3">
            <input
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Search title, keywords, MRN/Accession..."
              className="col-span-4 rounded-lg border border-gray-300 bg-gray-50 px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500"
            />
            <div className="col-span-3 flex items-center gap-2">
              <label className="text-xs text-slate-600">Time</label>
              <div
                className="inline-flex overflow-hidden rounded-full border border-gray-300"
                role="tablist"
                aria-label="Time window"
              >
                {(
                  [
                    { k: "30", l: "30d" },
                    { k: "90", l: "90d" },
                    { k: "180", l: "6m" },
                    { k: "since", l: "Since Dx" },
                  ] as const
                ).map((opt) => (
                  <button
                    key={opt.k}
                    role="tab"
                    aria-selected={range === opt.k}
                    className={`px-3 py-1 text-xs font-medium ${range === opt.k ? "bg-gray-100 text-gray-900" : "bg-white text-gray-600"}`}
                    onClick={() => {
                      setRange(opt.k as any);
                      try {
                        trackEvent &&
                          trackEvent("time_range_change", { range: opt.k });
                      } catch {}
                    }}
                  >
                    {opt.l}
                  </button>
                ))}
              </div>
            </div>
            <div className="col-span-3 flex items-center gap-2">
              <label className="text-xs text-slate-600">Modality</label>
              {(["history", "labs", "imaging"] as const).map((m) => (
                <label
                  key={m}
                  className="inline-flex items-center gap-1 text-xs text-slate-700"
                >
                  <input
                    type="checkbox"
                    checked={modalities[m]}
                    onChange={() =>
                      setModalities((prev) => ({ ...prev, [m]: !prev[m] }))
                    }
                  />{" "}
                  {m[0].toUpperCase() + m.slice(1)}
                </label>
              ))}
            </div>
            <div className="col-span-2 flex items-center justify-end gap-2">
              <div
                className="inline-flex overflow-hidden rounded-full border border-gray-300"
                role="group"
                aria-label="Density"
              >
                <button
                  className={`px-3 py-1 text-xs font-medium ${density === "comfortable" ? "bg-gray-100 text-gray-900" : "bg-white text-gray-600"}`}
                  onClick={() => setDensity("comfortable")}
                >
                  Comfortable
                </button>
                <button
                  className={`px-3 py-1 text-xs font-medium ${density === "compact" ? "bg-gray-100 text-gray-900" : "bg-white text-gray-600"}`}
                  onClick={() => setDensity("compact")}
                >
                  Compact
                </button>
              </div>
              <div
                className="inline-flex overflow-hidden rounded-full border border-gray-300"
                role="group"
                aria-label="View mode"
              >
                <button
                  className={`px-3 py-1 text-xs font-medium ${fullView ? "bg-gray-100 text-gray-900" : "bg-white text-gray-600"}`}
                  onClick={() => {
                    setFullView(true);
                    try {
                      trackEvent &&
                        trackEvent("view_mode_change", { full: true });
                    } catch {}
                  }}
                >
                  Full
                </button>
                <button
                  className={`px-3 py-1 text-xs font-medium ${!fullView ? "bg-gray-100 text-gray-900" : "bg-white text-gray-600"}`}
                  onClick={() => {
                    setFullView(false);
                    try {
                      trackEvent &&
                        trackEvent("view_mode_change", { full: false });
                    } catch {}
                  }}
                >
                  Summary
                </button>
              </div>
              <button
                className="relative flex items-center"
                onClick={() => {
                  setLastSyncedSec(0);
                  setPulseUntil(Date.now() + 6000);
                  try {
                    trackEvent &&
                      trackEvent("sync_dot_click", { atSec: lastSyncedSec });
                  } catch {}
                }}
                aria-label={`Last synced ${lastSyncedSec}s ago`}
              >
                <span className="relative flex h-2 w-2">
                  <span
                    className={`${Date.now() < pulseUntil && !prefersReduced ? "animate-ping" : ""} absolute inline-flex h-full w-full rounded-full bg-emerald-400 opacity-75`}
                  ></span>
                  <span className="relative inline-flex h-2 w-2 rounded-full bg-emerald-500"></span>
                </span>
                <span className="ml-2 text-xs text-slate-600">
                  Synced {lastSyncedSec}s ago
                </span>
                <span
                  aria-live="polite"
                  style={{
                    position: "absolute",
                    width: 1,
                    height: 1,
                    padding: 0,
                    margin: -1,
                    overflow: "hidden",
                    clip: "rect(0,0,0,0)",
                    whiteSpace: "nowrap",
                    border: 0,
                  }}
                >
                  Synced {lastSyncedSec} seconds ago
                </span>
              </button>
            </div>
            <div className="col-span-1 text-right">
              <button className="rounded-md border border-gray-300 px-3 py-1 text-xs hover:bg-gray-50">
                Export ▾
              </button>
            </div>
          </div>
        </div>

        <div className="flex-1 px-6 py-6">
          <div className="space-y-4">
            <Section label="History" items={historyItems} row="history" />
            <Section label="Labs" items={labItems} row="labs" />
            <Section label="Imaging" items={imagingItems} row="imaging" />
          </div>
        </div>

        {/* Side-sheet */}
        <div
          role="dialog"
          aria-modal={openItem ? true : false}
          aria-labelledby="sheet-title"
          className={`absolute right-0 top-0 h-full w-[480px] max-w-[85vw] border-l border-gray-200 bg-white shadow-2xl transition-transform duration-300 ${openItem ? "translate-x-0" : "translate-x-full"}`}
        >
          <div className="flex items-center justify-between border-b border-gray-200 px-5 py-3">
            <div className="flex items-center gap-2">
              <div className="flex h-7 w-7 items-center justify-center rounded-md bg-emerald-100 text-xs font-bold text-emerald-700">
                {openItem
                  ? openItem.row === "labs"
                    ? "LIS"
                    : openItem.row === "imaging"
                      ? "PACS"
                      : "EMR"
                  : ""}
              </div>
              <h3
                id="sheet-title"
                className="text-sm font-semibold text-slate-900"
              >
                Report & provenance
              </h3>
            </div>
            <button
              className="text-sm text-slate-600 hover:text-slate-900"
              onClick={() => setOpenItem(null)}
              aria-label="Close"
            >
              ✕
            </button>
          </div>

          <div className="flex items-center gap-2 border-b border-gray-100 px-5 py-3">
            <button
              className={`rounded-md border px-3 py-1 text-xs ${sheetTab === "summary" ? "border-emerald-300 bg-emerald-50 text-emerald-700" : "border-gray-300 bg-white"}`}
              onClick={() => setSheetTab("summary")}
            >
              Summary
            </button>
            <button
              className={`rounded-md border px-3 py-1 text-xs ${sheetTab === "full" ? "border-emerald-300 bg-emerald-50 text-emerald-700" : "border-gray-300 bg-white"}`}
              onClick={() => setSheetTab("full")}
            >
              Full report
            </button>
            <button
              className={`rounded-md border px-3 py-1 text-xs ${sheetTab === "prov" ? "border-emerald-300 bg-emerald-50 text-emerald-700" : "border-gray-300 bg-white"}`}
              onClick={() => setSheetTab("prov")}
            >
              Provenance
            </button>
          </div>

          <div className="h-[calc(100%-140px)] space-y-4 overflow-auto p-5">
            {sheetTab === "summary" && (
              <div className="space-y-3">
                <div className="rounded-lg border border-gray-200 p-3">
                  <p className="text-xs text-slate-600">Synopsis</p>
                  <p className="text-sm text-slate-900">
                    Key values summarized with trends where applicable.
                  </p>
                </div>
                <div className="rounded-lg border border-gray-200 p-3">
                  <p className="text-xs text-slate-600">Highlights</p>
                  <p className="text-sm text-slate-900">
                    ER+/PR+, HER2– · Hgb 10.4 (L) · ANC 1.2 (L)
                  </p>
                </div>
              </div>
            )}
            {sheetTab === "full" && (
              <div className="space-y-3">
                <div className="rounded-lg border border-gray-200 p-3">
                  <p className="text-xs text-slate-600">Full report</p>
                  <p className="text-sm text-slate-900">
                    PDF or structured view would render here.
                  </p>
                </div>
              </div>
            )}
            {sheetTab === "prov" && (
              <div className="space-y-3">
                <div className="rounded-lg border border-gray-200 p-3">
                  <p className="text-xs text-slate-600">Provenance</p>
                  <p className="text-sm text-slate-900">
                    DiagnosticReport → Observation[] · Specimen · Device ·
                    timestamps (collection, issued)
                  </p>
                </div>
              </div>
            )}
          </div>

          <div className="flex items-center justify-between border-t border-gray-200 px-5 py-3">
            <div className="flex items-center gap-2">
              <button className="rounded-full border border-gray-300 px-2.5 py-1 text-xs">
                Order follow‑up test
              </button>
              <button className="rounded-full border border-gray-300 px-2.5 py-1 text-xs">
                Add to board pack
              </button>
            </div>
            <div className="flex items-center gap-2">
              <button className="rounded-full border border-gray-300 px-2.5 py-1 text-xs">
                Flag inconsistency
              </button>
              <button className="rounded-full border border-emerald-300 bg-emerald-50 px-2.5 py-1 text-xs text-emerald-700">
                Mark reviewed ✓
              </button>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default TimelineView;

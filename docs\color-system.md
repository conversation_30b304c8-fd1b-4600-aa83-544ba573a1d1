# Healthcare Color System Documentation

## Overview

This document outlines the comprehensive color system designed for healthcare applications, inspired by Microsoft AI and Apollo.io design principles, with a focus on accessibility, trust, and clinical clarity.

## Design Principles

### 1. Accessibility First

- **WCAG AA Compliance**: All color combinations maintain a minimum 4.5:1 contrast ratio for normal text
- **WCAG AAA Support**: Critical information uses 7:1+ contrast ratios
- **Color Independence**: Information is never conveyed through color alone
- **Dark Mode Optimization**: Consistent accessibility across light and dark themes

### 2. Healthcare Context

- **Clinical Clarity**: Colors support medical decision-making without causing confusion
- **Status Communication**: Clear, universally understood color coding for medical statuses
- **Trust Building**: Colors convey professionalism, security, and reliability
- **Stress Reduction**: Soft, organic color palette reduces cognitive load

### 3. Brand Alignment

- **Entheory Identity**: Blue flame-inspired primary palette
- **Microsoft AI Aesthetics**: Soft, organic gradients and sophisticated neutrals
- **Apollo.io Functionality**: Conversion-focused action colors and clear hierarchies

## Color Categories

### Brand Colors

```css
/* Primary Brand - Blue Flame Inspired */
--brand-primary: 220 85% 58%; /* Main Entheory blue */
--brand-primary-light: 220 85% 68%; /* Lighter variant */
--brand-primary-lighter: 220 85% 85%; /* Background tint */
--brand-primary-dark: 220 85% 48%; /* Darker variant */
--brand-primary-darker: 220 85% 35%; /* High contrast */

/* Secondary Brand - Warm Complement */
--brand-secondary: 45 90% 60%; /* Warm orange accent */
```

**Usage Guidelines:**

- Primary blue for main actions, headers, and brand elements
- Use lighter variants for backgrounds and subtle emphasis
- Secondary orange for highlights and warm accents
- Maintain brand consistency across all touchpoints

### Healthcare Semantic Colors

```css
/* Medical Status Colors */
--medical-critical: 0 75% 55%; /* Emergency/Critical - Red */
--medical-warning: 45 100% 50%; /* Caution/Warning - Amber */
--medical-success: 140 65% 42%; /* Success/Safe - Green */
--medical-info: 210 90% 55%; /* Information - Blue */
--medical-neutral: 200 15% 60%; /* Neutral/Pending - Gray */

/* Trust & Security Colors */
--trust-verified: 140 70% 40%; /* Verified status */
--trust-secure: 220 85% 50%; /* Security indicator */
--trust-compliant: 260 60% 50%; /* Compliance badge */
```

**Medical Color Psychology:**

- **Red (Critical)**: Immediate attention, emergency situations
- **Amber (Warning)**: Caution required, potential issues
- **Green (Success)**: Safe, approved, completed actions
- **Blue (Info)**: Informational, non-urgent communications
- **Gray (Neutral)**: Pending, inactive, or baseline states

### Neutral Palette

```css
/* Microsoft AI Inspired Neutrals */
--neutral-50: 220 20% 98%; /* Softest background */
--neutral-100: 220 15% 95%; /* Card backgrounds */
--neutral-200: 220 10% 90%; /* Subtle borders */
--neutral-300: 220 8% 80%; /* Disabled states */
--neutral-400: 220 6% 65%; /* Muted text (4.6:1) */
--neutral-500: 220 5% 50%; /* Secondary text (7.0:1) */
--neutral-600: 220 8% 35%; /* Primary text light (11.9:1) */
--neutral-700: 220 12% 25%; /* Primary text (16.0:1) */
--neutral-800: 220 15% 15%; /* Headings (19.2:1) */
--neutral-900: 220 20% 8%; /* High contrast (21:1) */
```

**Contrast Ratios:**

- All neutral colors tested against white and dark backgrounds
- Numbers in parentheses indicate contrast ratio on white background
- Minimum AA compliance (4.5:1) maintained throughout hierarchy

### Interactive Colors

```css
/* Action & CTA Colors */
--cta-primary: 220 85% 58%; /* Primary actions */
--cta-primary-hover: 220 85% 48%; /* Hover state */
--cta-secondary: 45 85% 55%; /* Secondary actions */
--cta-success: 140 65% 42%; /* Success actions */
--cta-danger: 0 75% 55%; /* Destructive actions */

/* State Colors */
--interactive-hover: 220 85% 95%; /* Hover backgrounds */
--interactive-active: 220 85% 90%; /* Active states */
--interactive-focus: 220 85% 58%; /* Focus rings */
--interactive-disabled: 220 5% 80%; /* Disabled states */
```

## Dark Mode Strategy

### Adaptive Color Relationships

- **Increased Luminance**: Brand colors become lighter in dark mode for better visibility
- **Reduced Contrast**: Softer contrast ratios to reduce eye strain
- **Consistent Hierarchy**: Color relationships maintain semantic meaning
- **Background Depth**: Multiple surface levels for visual hierarchy

### Dark Mode Adjustments

```css
.dark {
  /* Backgrounds */
  --background: 220 15% 8%;
  --surface-elevated: 220 12% 12%;
  --surface-secondary: 220 10% 15%;

  /* Adjusted Brand Colors */
  --brand-primary: 220 85% 68%; /* +10% lightness */
  --medical-success: 140 55% 55%; /* +13% lightness */
}
```

## Accessibility Testing

### Automated Validation

Use the included color utilities to validate accessibility:

```typescript
import {
  isAccessible,
  getContrastRatio,
  validateColorPalette,
} from "@/styles/colors";

// Test individual color combinations
const isValid = isAccessible("hsl(220 20% 8%)", "hsl(0 0% 100%)"); // true

// Get specific contrast ratio
const ratio = getContrastRatio("hsl(220 85% 58%)", "hsl(0 0% 100%)"); // ~5.8:1

// Validate entire palette
const results = validateColorPalette();
console.log(results.passed); // Array of passing combinations
console.log(results.failed); // Array of failing combinations
```

### Manual Testing Checklist

- [ ] All text meets 4.5:1 contrast minimum
- [ ] Large text (18pt+) meets 3:1 contrast minimum
- [ ] Focus indicators are clearly visible
- [ ] Color is not the only way information is conveyed
- [ ] Colors work for users with color blindness
- [ ] Dark mode maintains accessibility standards

## Implementation Guidelines

### Component Usage

```tsx
// Status indicators
<div className="status-critical">Critical Alert</div>
<div className="status-success">Operation Complete</div>

// Trust badges
<div className="trust-badge">Verified Provider</div>

// Interactive elements
<button className="bg-cta-primary hover:bg-cta-primary-hover">
  Primary Action
</button>
```

### CSS Custom Properties

```css
.medical-card {
  background: hsl(var(--surface-elevated));
  border: 1px solid hsl(var(--neutral-200));
  color: hsl(var(--neutral-800));
}

.critical-alert {
  background: hsl(var(--medical-critical-light));
  color: hsl(var(--medical-critical));
  border-color: hsl(var(--medical-critical) / 0.2);
}
```

## Best Practices

### Do's

- ✅ Always test color combinations for accessibility
- ✅ Use semantic color names (success, warning, critical)
- ✅ Maintain consistent color relationships across themes
- ✅ Provide non-color indicators for important information
- ✅ Use the healthcare color palette for medical contexts

### Don'ts

- ❌ Don't rely solely on color to convey information
- ❌ Don't use pure red or green without considering color blindness
- ❌ Don't create custom colors outside the design system
- ❌ Don't ignore dark mode color adjustments
- ❌ Don't use low-contrast combinations

## Color Palette Reference

### Quick Reference

| Color    | Light Mode    | Dark Mode     | Use Case       |
| -------- | ------------- | ------------- | -------------- |
| Primary  | `220 85% 58%` | `220 85% 68%` | Brand, CTAs    |
| Critical | `0 75% 55%`   | `0 75% 65%`   | Errors, alerts |
| Success  | `140 65% 42%` | `140 55% 55%` | Confirmations  |
| Warning  | `45 100% 50%` | `45 100% 60%` | Cautions       |
| Info     | `210 90% 55%` | `210 90% 65%` | Information    |

### Gradient Combinations

```css
/* Healthcare-specific gradients */
--gradient-medical: linear-gradient(
  135deg,
  hsl(210 90% 55%) 0%,
  hsl(140 65% 42%) 100%
);
--gradient-trust: linear-gradient(
  135deg,
  hsl(220 85% 50%) 0%,
  hsl(140 70% 40%) 100%
);
--gradient-hero-medical: linear-gradient(
  135deg,
  hsl(220 85% 58%) 0%,
  hsl(210 90% 55%) 50%,
  hsl(140 65% 42%) 100%
);
```

## Maintenance

### Regular Reviews

- Monthly accessibility audits
- Quarterly color palette reviews
- Semi-annual user testing for color perception
- Annual comprehensive accessibility assessment

### Version Control

- Document all color changes with rationale
- Test impact on existing components
- Update design tokens in design tools
- Communicate changes to development team

## Resources

### Tools

- [WebAIM Contrast Checker](https://webaim.org/resources/contrastchecker/)
- [Colorblinding Simulator](https://www.color-blindness.com/coblis-color-blindness-simulator/)
- [WCAG Guidelines](https://www.w3.org/WAI/WCAG21/Understanding/)

### Related Documentation

- Component Library Guidelines
- Design Token Documentation
- Accessibility Standards
- Brand Guidelines

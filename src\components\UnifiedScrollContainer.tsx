import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import React, { useEffect, useRef } from "react";
import FinalCTA from "./FinalCTA";
import Section5Standards from "./sections/Section5Standards";
import Section6Frames from "./sections/Section6Frames";
import Section4To5Transition from "./transitions/Section4To5Transition";

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

const UnifiedScrollContainer: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const section5Ref = useRef<HTMLDivElement>(null);
  const section6Ref = useRef<HTMLDivElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const prefersReducedMotion = window.matchMedia(
      "(prefers-reduced-motion: reduce)"
    ).matches;
    if (prefersReducedMotion) return;

    // Create seamless transition from Section 5 to Section 6
    const transitionTl = gsap.timeline({
      scrollTrigger: {
        trigger: containerRef.current,
        start: "top top",
        end: "+=100%",
        scrub: 1,
        onUpdate: (self) => {
          const progress = self.progress;

          // Section 5 exit starts at 70% progress
          if (progress > 0.7 && section5Ref.current) {
            const exitProgress = (progress - 0.7) / 0.3;
            gsap.set(section5Ref.current, {
              opacity: 1 - exitProgress * 0.3, // Fade to 0.7
              scale: 1 - exitProgress * 0.02, // Slight scale down
            });
          }

          // Section 6 entry starts at 75% progress
          if (progress > 0.75 && section6Ref.current) {
            const entryProgress = (progress - 0.75) / 0.25;
            gsap.set(section6Ref.current, {
              opacity: entryProgress,
              scale: 0.98 + entryProgress * 0.02,
            });
          }
        },
      },
    });

    // Smooth transition from Section 6 to Final CTA
    ScrollTrigger.create({
      trigger: section6Ref.current,
      start: "bottom 80%",
      end: "bottom 20%",
      onUpdate: (self) => {
        if (ctaRef.current) {
          gsap.set(ctaRef.current, {
            opacity: self.progress,
            y: (1 - self.progress) * 30,
          });
        }
      },
    });

    return () => {
      transitionTl.kill();
      ScrollTrigger.getAll().forEach((st) => st.kill());
    };
  }, []);

  return (
    <div ref={containerRef} className="relative">
      {/* Transition from Section 4 to 5 */}
      <Section4To5Transition />

      {/* Section 5: Standards Connect (Cloud/Mycelium) */}
      <div ref={section5Ref} className="relative">
        <Section5Standards />
      </div>

      {/* Transition buffer */}
      <div className="relative h-[50vh]" aria-hidden="true">
        <div
          className="absolute inset-0"
          style={{
            background: "linear-gradient(180deg, transparent 0%, #f6f7fb 100%)",
          }}
        />
      </div>

      {/* Section 6: Four Frames Scrollytelling */}
      <div ref={section6Ref} className="relative">
        <Section6Frames />
      </div>

      {/* Final CTA */}
      <div ref={ctaRef} className="relative">
        <FinalCTA />
      </div>
    </div>
  );
};

export default UnifiedScrollContainer;

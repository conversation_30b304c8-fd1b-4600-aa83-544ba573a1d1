import React, { useEffect, useRef, useState } from "react";
import { motion, useReducedMotion } from "framer-motion";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Star } from "lucide-react";
import { useAnalytics } from "@/hooks/useAnalytics";

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

type FrameSpec = {
  id: string;
  title: string;
  subtitle?: string;
  bullets?: string[];
  src: string;
  alt: string;
};

const FRAMES: FrameSpec[] = [
  {
    id: "rivers",
    title: "Stop chasing.",
    subtitle: "Start seeing.",
    bullets: [
      "Auto‑fetch with consent",
      "OCR for PDFs",
      "Units & dates harmonized",
    ],
    src: "/media/section5/frame-a-rivers.svg",
    alt: "Rivers converge into one calm channel",
  },
  {
    id: "standards",
    title: "Standards in.",
    subtitle: "Standards out.",
    bullets: [
      "FHIR bundles & DICOM links",
      "ABDM consent artifacts",
      "Export with citations",
    ],
    src: "/media/section5/frame-b-mycelium.svg",
    alt: "Mycelium-like threads connect standards badges",
  },
  {
    id: "care",
    title: "Care,",
    subtitle: "in motion.",
    bullets: [
      "One‑click orders, pre‑filled",
      "Trial matches & safety checks",
      "Board pack in <3 min",
    ],
    src: "/media/section5/frame-c-fireflies.svg",
    alt: "Fireflies gently glowing along a winding path",
  },
  {
    id: "connection",
    title: "Where connection",
    subtitle: "is infinite.",
    bullets: ["The oncology OS for interoperable, efficient care"],
    src: "/media/section5/frame-d-emblem.svg",
    alt: "Human hand holding the blue Entheory emblem",
  },
];

const Section5Enhanced: React.FC = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const framesRef = useRef<HTMLDivElement[]>([]);
  const overlaysRef = useRef<HTMLDivElement[]>([]);
  const [currentFrame, setCurrentFrame] = useState(0);
  const [ctaOpen, setCtaOpen] = useState(false);
  const [pilotEmail, setPilotEmail] = useState("");
  const [pilotName, setPilotName] = useState("");
  const [role, setRole] = useState<"physician" | "administration">("physician");
  const ctaOpenedRef = useRef(false);
  const prefersReduced = useReducedMotion();
  const { trackEvent } = useAnalytics();

  useEffect(() => {
    if (!sectionRef.current) return;

    const section = sectionRef.current;
    const frames = framesRef.current.filter(Boolean);

    // Fallback for reduced motion: show CTA near the end of the section using IO
    if (prefersReduced) {
      const io = new IntersectionObserver(
        (entries) => {
          const entry = entries[0];
          if (
            entry &&
            entry.isIntersecting &&
            entry.intersectionRatio > 0.85 &&
            !ctaOpenedRef.current
          ) {
            ctaOpenedRef.current = true;
            setTimeout(() => setCtaOpen(true), 1200);
          }
        },
        { threshold: [0.85, 0.95, 1] }
      );
      io.observe(section);
      return () => io.disconnect();
    }

    // Initialize all frames as invisible except first
    frames.forEach((frame, i) => {
      if (i === 0) {
        gsap.set(frame, { opacity: 1, visibility: "visible" });
      } else {
        gsap.set(frame, { opacity: 0, visibility: "hidden" });
      }
    });

    // Create main timeline for section
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: section,
        start: "top top",
        end: "+=300%", // 4 frames worth of scrolling
        scrub: 1,
        pin: true,
        pinSpacing: true,
        anticipatePin: 1,
        onUpdate: (self) => {
          const progress = self.progress;
          const frameIndex = Math.min(Math.floor(progress * 4), 3);
          setCurrentFrame(frameIndex);

          // Trigger CTA on final frame
          if (frameIndex === 3 && progress > 0.85 && !ctaOpenedRef.current) {
            ctaOpenedRef.current = true;
            setTimeout(() => setCtaOpen(true), 1200);
          }
        },
      },
    });

    // Animate frames
    frames.forEach((frame, i) => {
      if (i === 0) return; // First frame starts visible

      const startProgress = (i - 1) / 3;
      const endProgress = i / 3;

      // Fade in
      tl.fromTo(
        frame,
        {
          opacity: 0,
          visibility: "hidden",
          scale: 1.02,
          y: 20,
        },
        {
          opacity: 1,
          visibility: "visible",
          scale: 1,
          y: 0,
          duration: 0.25,
          ease: "power2.inOut",
        },
        startProgress
      );

      // Fade out previous frame
      if (i > 0) {
        tl.to(
          frames[i - 1],
          {
            opacity: 0,
            visibility: "hidden",
            scale: 0.98,
            y: -20,
            duration: 0.25,
            ease: "power2.inOut",
          },
          startProgress
        );
      }
    });

    // Add overlay animations
    const overlays = overlaysRef.current.filter(Boolean);
    overlays.forEach((overlay, i) => {
      const startProgress = i / 4;
      const endProgress = (i + 1) / 4;

      tl.fromTo(
        overlay,
        { opacity: 0 },
        {
          opacity: 1,
          duration: 0.15,
          ease: "power2.out",
        },
        startProgress + 0.1
      );

      if (i < 3) {
        // Don't fade out last overlay
        tl.to(
          overlay,
          {
            opacity: 0,
            duration: 0.15,
            ease: "power2.in",
          },
          endProgress - 0.1
        );
      }
    });

    // Extra safety: open when user leaves the pinned section end
    const endTrigger = ScrollTrigger.create({
      trigger: section,
      start: "top top",
      end: "+=300%",
      onLeave: () => {
        if (!ctaOpenedRef.current) {
          ctaOpenedRef.current = true;
          setTimeout(() => setCtaOpen(true), 800);
        }
      },
    });

    return () => {
      tl.kill();
      endTrigger.kill();
    };
  }, [prefersReduced]);

  // Global event to open the pilot dialog from navigation
  useEffect(() => {
    const handler = () => setCtaOpen(true);
    window.addEventListener("early-pilot-open", handler as EventListener);
    return () =>
      window.removeEventListener("early-pilot-open", handler as EventListener);
  }, []);

  // Track frame views
  useEffect(() => {
    if (currentFrame >= 0 && currentFrame < FRAMES.length) {
      const frame = FRAMES[currentFrame];
      trackEvent?.("section5_frame_view", {
        frame: frame.id,
        index: currentFrame,
      });
    }
  }, [currentFrame, trackEvent]);

  const renderFrame = (frameIndex: number) => {
    const frame = FRAMES[frameIndex];

    return (
      <div
        ref={(el) => (framesRef.current[frameIndex] = el!)}
        className="frame-container absolute inset-0 flex items-center justify-center"
        key={frame.id}
      >
        <div className="container mx-auto grid items-center gap-16 px-8 md:px-16 lg:grid-cols-2">
          {/* Left: Text content */}
          <div>
            <h3 className="text-6xl font-light leading-[1.05] text-slate-900 md:text-7xl lg:text-8xl">
              {frame.title}
              {frame.subtitle && (
                <>
                  <br />
                  <em className="font-light italic">{frame.subtitle}</em>
                </>
              )}
            </h3>

            {frame.bullets && frame.bullets.length > 0 && (
              <ul className="mt-8 space-y-3">
                {frame.bullets.map((bullet, i) => (
                  <li
                    key={i}
                    className="flex items-start text-lg text-slate-600"
                  >
                    <span className="mr-3 mt-2 inline-block h-2 w-2 flex-shrink-0 rounded-full bg-indigo-400" />
                    <span>{bullet}</span>
                  </li>
                ))}
              </ul>
            )}

            {/* CTA for final frame: single comprehensive button */}
            {frameIndex === 3 && (
              <div className="mt-8">
                <Button
                  className="h-11 rounded-md bg-black px-6 font-medium text-white hover:bg-neutral-900"
                  onClick={() => {
                    trackEvent?.("section5_cta_click", { type: "early_pilot" });
                    try {
                      window.dispatchEvent(new Event("early-pilot-open"));
                    } catch {}
                  }}
                >
                  Partner for an early pilot →
                </Button>
              </div>
            )}
          </div>

          {/* Right: Artwork */}
          <div className="relative flex h-[500px] items-center justify-center">
            <img
              src={frame.src}
              alt={frame.alt}
              className="h-full w-full object-contain"
              style={{
                filter:
                  frameIndex === 3
                    ? "drop-shadow(0 0 40px rgba(124, 58, 237, 0.3))"
                    : undefined,
              }}
            />
          </div>
        </div>
      </div>
    );
  };

  const renderOverlay = (frameIndex: number) => {
    return (
      <div
        ref={(el) => (overlaysRef.current[frameIndex] = el!)}
        className="pointer-events-none absolute inset-0"
        key={`overlay-${frameIndex}`}
      >
        {frameIndex === 0 && <RiversOverlay />}
        {frameIndex === 1 && <MyceliumOverlay />}
        {frameIndex === 2 && <FirefliesOverlay />}
        {frameIndex === 3 && <EmblemOverlay />}
      </div>
    );
  };

  return (
    <section
      ref={sectionRef}
      className="relative min-h-screen overflow-hidden"
      style={{
        background:
          "linear-gradient(180deg, #f6f7fb 0%, #faf5ff 50%, #ffffff 100%)",
      }}
    >
      {/* Frames */}
      {FRAMES.map((_, i) => renderFrame(i))}

      {/* Overlays */}
      {!prefersReduced && FRAMES.map((_, i) => renderOverlay(i))}

      {/* Progress indicator */}
      <div className="fixed bottom-8 left-1/2 z-50 flex -translate-x-1/2 gap-2">
        {FRAMES.map((_, i) => (
          <div
            key={i}
            className={`h-2 w-2 rounded-full transition-all duration-300 ${
              i === currentFrame ? "w-8 bg-indigo-600" : "bg-gray-300"
            }`}
          />
        ))}
      </div>

      {/* CTA Modal */}
      <Dialog open={ctaOpen} onOpenChange={setCtaOpen}>
        <DialogContent className="overflow-hidden p-0 sm:max-w-[720px]">
          <div className="p-6 md:p-8">
            <DialogHeader className="space-y-2">
              <DialogTitle className="text-2xl tracking-tight md:text-3xl">
                Partner with Entheory: Early Pilot
              </DialogTitle>
              <DialogDescription className="text-slate-600">
                Book a personalized walkthrough to explore a limited early pilot
                with your oncology team.
              </DialogDescription>
            </DialogHeader>

            <form
              className="mt-6 grid gap-4"
              onSubmit={(e) => {
                e.preventDefault();
                if (!/[^\s@]+@[^\s@]+\.[^\s@]+/.test(pilotEmail)) return;
                trackEvent?.("section5_cta_submit", {
                  email: pilotEmail,
                  role,
                  name: pilotName,
                });
                setCtaOpen(false);
              }}
            >
              <div className="grid gap-3">
                <label
                  htmlFor="pilot-name"
                  className="sr-only text-sm font-medium"
                >
                  Full name
                </label>
                <input
                  id="pilot-name"
                  type="text"
                  required
                  value={pilotName}
                  onChange={(e) => setPilotName(e.target.value)}
                  placeholder="Full name"
                  className="h-12 w-full rounded-md border border-slate-300 px-3 text-[15px]"
                />
                <label
                  htmlFor="pilot-email"
                  className="sr-only text-sm font-medium"
                >
                  Company email
                </label>
                <input
                  id="pilot-email"
                  type="email"
                  required
                  value={pilotEmail}
                  onChange={(e) => setPilotEmail(e.target.value)}
                  placeholder="Company email"
                  className="h-12 w-full rounded-md border border-slate-300 px-3 text-[15px]"
                />
                <div className="grid gap-2 sm:grid-cols-2">
                  <div className="grid gap-2">
                    <label
                      htmlFor="pilot-hospital-inline"
                      className="sr-only text-sm font-medium"
                    >
                      Hospital / Institution
                    </label>
                    <input
                      id="pilot-hospital-inline"
                      type="text"
                      placeholder="Hospital / Institution"
                      className="h-11 w-full rounded-md border border-slate-300 px-3 text-[14px]"
                    />
                  </div>
                  <div className="grid gap-2">
                    <label
                      htmlFor="pilot-city-inline"
                      className="sr-only text-sm font-medium"
                    >
                      City
                    </label>
                    <input
                      id="pilot-city-inline"
                      type="text"
                      placeholder="City"
                      className="h-11 w-full rounded-md border border-slate-300 px-3 text-[14px]"
                    />
                  </div>
                </div>
                <div className="grid gap-2 sm:grid-cols-2">
                  <div className="grid gap-2">
                    <span className="text-xs text-slate-600">Your role</span>
                    <div className="flex gap-2">
                      <button
                        type="button"
                        onClick={() => setRole("physician")}
                        className={`h-10 rounded-md border px-3 ${role === "physician" ? "border-indigo-600 bg-indigo-600 text-white" : "border-slate-300 bg-white text-slate-700 hover:bg-slate-50"}`}
                      >
                        Physician
                      </button>
                      <button
                        type="button"
                        onClick={() => setRole("administration")}
                        className={`h-10 rounded-md border px-3 ${role === "administration" ? "border-indigo-600 bg-indigo-600 text-white" : "border-slate-300 bg-white text-slate-700 hover:bg-slate-50"}`}
                      >
                        Hospital administration
                      </button>
                    </div>
                  </div>
                  <div className="grid gap-2">
                    <label
                      htmlFor="pilot-phone-inline"
                      className="sr-only text-sm font-medium"
                    >
                      Phone
                    </label>
                    <input
                      id="pilot-phone-inline"
                      type="tel"
                      placeholder="Phone (optional)"
                      className="h-11 w-full rounded-md border border-slate-300 px-3 text-[14px]"
                    />
                  </div>
                </div>
                <label
                  htmlFor="pilot-notes-inline"
                  className="sr-only text-sm font-medium"
                >
                  Key context
                </label>
                <textarea
                  id="pilot-notes-inline"
                  placeholder="Key context (eg. tumor board workflow, bed count, vendors)"
                  className="min-h-[90px] w-full rounded-md border border-slate-300 px-3 py-2 text-[14px]"
                />
                <div className="flex gap-2">
                  <button
                    type="button"
                    onClick={() => setRole("physician")}
                    className={`h-10 rounded-md border px-3 ${role === "physician" ? "border-indigo-600 bg-indigo-600 text-white" : "border-slate-300 bg-white text-slate-700 hover:bg-slate-50"}`}
                    aria-pressed={role === "physician"}
                  >
                    Physician
                  </button>
                  <button
                    type="button"
                    onClick={() => setRole("administration")}
                    className={`h-10 rounded-md border px-3 ${role === "administration" ? "border-indigo-600 bg-indigo-600 text-white" : "border-slate-300 bg-white text-slate-700 hover:bg-slate-50"}`}
                    aria-pressed={role === "administration"}
                  >
                    Hospital administration
                  </button>
                </div>
                <Button
                  type="submit"
                  className="h-12 rounded-md bg-black font-medium text-white hover:bg-neutral-900"
                >
                  Book an early pilot →
                </Button>
              </div>

              <p className="mt-4 text-[12px] text-slate-500">
                By submitting, you agree to be contacted about a pilot. No spam.
                We respect your privacy.
              </p>

              <div className="mt-4 flex items-center gap-2 text-[12px] text-slate-600">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Star
                    key={i}
                    className="h-4 w-4 fill-current text-amber-400"
                  />
                ))}
                <span>4.7/5 from clinical users | GDPR friendly</span>
              </div>
            </form>
          </div>
        </DialogContent>
      </Dialog>
    </section>
  );
};

// Overlay Components
const RiversOverlay: React.FC = () => (
  <svg
    className="absolute inset-0 h-full w-full"
    viewBox="0 0 800 600"
    style={{ maxWidth: "100%", height: "100%" }}
  >
    <defs>
      <linearGradient id="riverGrad" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stopColor="#60a5fa" stopOpacity="0.3">
          <animate
            attributeName="stop-opacity"
            values="0.3;0.6;0.3"
            dur="4s"
            repeatCount="indefinite"
          />
        </stop>
        <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.1" />
      </linearGradient>
    </defs>

    {/* Animated river paths */}
    <path
      d="M 100 200 Q 300 180 500 300 T 700 300"
      stroke="url(#riverGrad)"
      strokeWidth="3"
      fill="none"
      opacity="0.6"
    >
      <animate
        attributeName="d"
        values="M 100 200 Q 300 180 500 300 T 700 300;
                M 100 200 Q 300 220 500 300 T 700 300;
                M 100 200 Q 300 180 500 300 T 700 300"
        dur="6s"
        repeatCount="indefinite"
      />
    </path>
  </svg>
);

const MyceliumOverlay: React.FC = () => (
  <svg
    className="absolute inset-0 h-full w-full"
    viewBox="0 0 800 600"
    style={{ maxWidth: "100%", height: "100%" }}
  >
    {/* Animated connection threads */}
    <g stroke="#a5b4fc" strokeWidth="1" fill="none" opacity="0.4">
      <path d="M 200 300 Q 400 250 600 300">
        <animate
          attributeName="stroke-width"
          values="1;2;1"
          dur="3s"
          repeatCount="indefinite"
        />
      </path>
    </g>
  </svg>
);

const FirefliesOverlay: React.FC = () => (
  <div className="absolute inset-0">
    {/* Animated firefly dots */}
    {[...Array(5)].map((_, i) => (
      <div
        key={i}
        className="absolute h-2 w-2 rounded-full bg-yellow-400"
        style={{
          left: `${20 + i * 15}%`,
          top: `${40 + Math.sin(i) * 20}%`,
          boxShadow: "0 0 20px rgba(250, 204, 21, 0.8)",
          animation: `float ${3 + i}s ease-in-out infinite`,
        }}
      />
    ))}
    <style>{`
      @keyframes float {
        0%, 100% { transform: translateY(0px); opacity: 0.6; }
        50% { transform: translateY(-20px); opacity: 1; }
      }
    `}</style>
  </div>
);

const EmblemOverlay: React.FC = () => (
  <div className="absolute inset-0 flex items-center justify-end pr-16">
    <div
      className="h-96 w-96 rounded-full"
      style={{
        background:
          "radial-gradient(circle, rgba(124, 58, 237, 0.2) 0%, transparent 70%)",
        animation: "pulse 4s ease-in-out infinite",
      }}
    />
    <style>{`
      @keyframes pulse {
        0%, 100% { transform: scale(1); opacity: 0.6; }
        50% { transform: scale(1.1); opacity: 0.8; }
      }
    `}</style>
  </div>
);

export default Section5Enhanced;

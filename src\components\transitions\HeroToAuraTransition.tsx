import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import React, { useEffect, useRef } from "react";

/**
 * Hero to Aura Transition Component
 *
 * Manages the transition from the hero section to the aura section
 * with smooth gradient blending and card animations.
 */

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

const HeroToAuraTransition: React.FC = () => {
  const transitionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!transitionRef.current) return;

    // Create a smooth blend from hero gradient to AuraStack gradient
    ScrollTrigger.create({
      trigger: transitionRef.current,
      start: "top bottom",
      end: "bottom top",
      scrub: true,
      onUpdate: (self) => {
        const progress = self.progress;

        // Fade hero cards if they exist
        const heroCards = document.querySelectorAll(".flow-hero-card");
        heroCards.forEach((card) => {
          gsap.set(card, {
            opacity: 1 - progress,
            y: progress * -30,
            scale: 1 - progress * 0.05,
          });
        });

        // Prepare AuraStack entry
        const auraStack = document.querySelector(".aura-stack-section");
        if (auraStack && progress > 0.5) {
          const entryProgress = (progress - 0.5) / 0.5;
          gsap.set(auraStack, {
            opacity: entryProgress,
            scale: 0.95 + entryProgress * 0.05,
          });
        }
      },
    });

    return () => {
      ScrollTrigger.getAll().forEach((st) => {
        if (st.trigger === transitionRef.current) {
          st.kill();
        }
      });
    };
  }, []);

  return (
    <div
      ref={transitionRef}
      className="relative h-[40vh] overflow-hidden"
      aria-hidden="true"
    >
      {/* Gradient transition from hero colors to AuraStack colors */}
      <div
        className="absolute inset-0"
        style={{
          background: `linear-gradient(180deg,
            rgba(251,207,232,0.55) 0%,
            rgba(255,224,209,0.45) 50%,
            rgba(255,224,209,0.55) 100%)`,
        }}
      />

      {/* Animated particles for smooth transition */}
      <div className="absolute inset-0">
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full"
            style={{
              width: `${Math.random() * 200 + 100}px`,
              height: `${Math.random() * 200 + 100}px`,
              background: `radial-gradient(circle, rgba(255,255,255,${0.2 + Math.random() * 0.3}) 0%, transparent 70%)`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animation: `float ${10 + Math.random() * 10}s ease-in-out infinite`,
              animationDelay: `${Math.random() * 5}s`,
            }}
          />
        ))}
      </div>

      <style>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0) translateX(0) scale(1);
            opacity: 0.3;
          }
          25% {
            transform: translateY(-20px) translateX(10px) scale(1.05);
            opacity: 0.5;
          }
          50% {
            transform: translateY(-10px) translateX(-10px) scale(0.95);
            opacity: 0.4;
          }
          75% {
            transform: translateY(-30px) translateX(5px) scale(1.02);
            opacity: 0.6;
          }
        }
      `}</style>
    </div>
  );
};

export default HeroToAuraTransition;

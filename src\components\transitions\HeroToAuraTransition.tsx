import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import React, { memo, useCallback, useEffect, useRef, useState } from "react";

/**
 * Hero to Aura Transition Component
 *
 * Optimized transition from the hero section to the aura section
 * with performance improvements and accessibility features.
 */

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

interface HeroToAuraTransitionProps {
  /** Optional CSS class name */
  className?: string;
  /** Whether to enable animations */
  enableAnimations?: boolean;
  /** Callback when transition completes */
  onTransitionComplete?: () => void;
}

const HeroToAuraTransition: React.FC<HeroToAuraTransitionProps> = memo(
  ({ className = "", enableAnimations = true, onTransitionComplete }) => {
    const transitionRef = useRef<HTMLDivElement>(null);
    const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
    const [isIntersecting, setIsIntersecting] = useState(false);

    // Check for reduced motion preference
    useEffect(() => {
      const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
      setPrefersReducedMotion(mediaQuery.matches);
      const handleChange = (e: MediaQueryListEvent) =>
        setPrefersReducedMotion(e.matches);
      mediaQuery.addEventListener("change", handleChange);
      return () => mediaQuery.removeEventListener("change", handleChange);
    }, []);

    // Intersection observer for performance
    useEffect(() => {
      if (!transitionRef.current) return;

      const observer = new IntersectionObserver(
        ([entry]) => setIsIntersecting(entry.isIntersecting),
        { threshold: 0.1, rootMargin: "50px" }
      );

      observer.observe(transitionRef.current);
      return () => observer.disconnect();
    }, []);

    const handleTransitionUpdate = useCallback(
      (progress: number) => {
        if (!enableAnimations || prefersReducedMotion) return;

        // Optimized hero cards fade
        const heroCards = document.querySelectorAll(".flow-hero-card");
        heroCards.forEach((card) => {
          gsap.set(card, {
            opacity: 1 - progress,
            y: progress * -20, // Reduced movement
            scale: 1 - progress * 0.03, // Reduced scale change
            force3D: true,
          });
        });

        // Optimized AuraStack entry
        const auraStack = document.querySelector(".aura-stack-section");
        if (auraStack && progress > 0.6) {
          // Later trigger for smoother transition
          const entryProgress = (progress - 0.6) / 0.4;
          gsap.set(auraStack, {
            opacity: entryProgress,
            scale: 0.98 + entryProgress * 0.02, // Reduced scale change
            force3D: true,
          });
        }

        // Call completion callback
        if (progress >= 1 && onTransitionComplete) {
          onTransitionComplete();
        }
      },
      [enableAnimations, prefersReducedMotion, onTransitionComplete]
    );

    useEffect(() => {
      if (
        !transitionRef.current ||
        !isIntersecting ||
        prefersReducedMotion ||
        !enableAnimations
      )
        return;

      // Create optimized scroll trigger
      const scrollTrigger = ScrollTrigger.create({
        trigger: transitionRef.current,
        start: "top bottom",
        end: "bottom top",
        scrub: 0.8, // Slightly smoother scrubbing
        refreshPriority: -1, // Lower priority for better performance
        onUpdate: (self) => handleTransitionUpdate(self.progress),
      });

      return () => scrollTrigger.kill();
    }, [
      isIntersecting,
      prefersReducedMotion,
      enableAnimations,
      handleTransitionUpdate,
    ]);

    return (
      <div
        ref={transitionRef}
        className={`relative h-[32vh] overflow-hidden ${className}`} // Reduced height
        aria-hidden="true"
      >
        {/* Optimized gradient transition */}
        <div
          className="absolute inset-0"
          style={{
            background: `linear-gradient(180deg,
            rgba(251,207,232,0.4) 0%,
            rgba(255,224,209,0.35) 50%,
            rgba(255,224,209,0.4) 100%)`, // Reduced opacity for better performance
          }}
        />

        {/* Simplified animated particles - only if animations enabled */}
        {enableAnimations && !prefersReducedMotion && (
          <>
            <div className="absolute inset-0" aria-hidden="true">
              {[...Array(4)].map(
                (
                  _,
                  i // Reduced particle count
                ) => (
                  <div
                    key={i}
                    className="absolute rounded-full"
                    style={{
                      width: `${80 + i * 20}px`, // Fixed sizes for better performance
                      height: `${80 + i * 20}px`,
                      background: `radial-gradient(circle, rgba(255,255,255,${0.15 + i * 0.05}) 0%, transparent 70%)`,
                      left: `${20 + i * 20}%`, // Fixed positions
                      top: `${15 + i * 15}%`,
                      animation: `float ${12 + i * 2}s ease-in-out infinite`, // Slower animations
                      animationDelay: `${i * 1.5}s`,
                    }}
                  />
                )
              )}
            </div>

            <style>{`
            @keyframes float {
              0%, 100% {
                transform: translate3d(0, 0, 0) scale(1);
                opacity: 0.2;
              }
              25% {
                transform: translate3d(8px, -12px, 0) scale(1.02);
                opacity: 0.35;
              }
              50% {
                transform: translate3d(-6px, -8px, 0) scale(0.98);
                opacity: 0.25;
              }
              75% {
                transform: translate3d(4px, -16px, 0) scale(1.01);
                opacity: 0.4;
              }
            }
          `}</style>
          </>
        )}
      </div>
    );
  }
);

HeroToAuraTransition.displayName = "HeroToAuraTransition";

export default HeroToAuraTransition;
export type { HeroToAuraTransitionProps };

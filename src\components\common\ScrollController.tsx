import React, { useEffect, useRef } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

interface ScrollControllerProps {
  children: React.ReactNode;
}

export const ScrollControllerContext = React.createContext<{
  timeline: gsap.core.Timeline | null;
  prefersReducedMotion: boolean;
}>({
  timeline: null,
  prefersReducedMotion: false,
});

const ScrollController: React.FC<ScrollControllerProps> = ({ children }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<gsap.core.Timeline | null>(null);
  const [prefersReducedMotion, setPrefersReducedMotion] = React.useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) =>
      setPrefersReducedMotion(e.matches);
    mediaQuery.addEventListener("change", handleChange);

    return () => mediaQuery.removeEventListener("change", handleChange);
  }, []);

  useEffect(() => {
    if (!containerRef.current || prefersReducedMotion) return;

    // Kill any existing scroll triggers
    ScrollTrigger.getAll().forEach((st) => {
      if (
        st.vars.trigger &&
        (st.vars.trigger as Element).closest("[data-scroll-container]")
      ) {
        st.kill();
      }
    });

    // Create master timeline for sections 4-5
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: containerRef.current,
        start: "top top",
        end: "+=400%", // Adjust based on total scroll distance needed
        scrub: 1,
        pin: false, // We'll handle pinning per section
        invalidateOnRefresh: true,
        anticipatePin: 1,
      },
    });

    timelineRef.current = tl;

    return () => {
      tl.kill();
      ScrollTrigger.getAll().forEach((st) => {
        if (
          st.vars.trigger &&
          (st.vars.trigger as Element).closest("[data-scroll-container]")
        ) {
          st.kill();
        }
      });
    };
  }, [prefersReducedMotion]);

  return (
    <ScrollControllerContext.Provider
      value={{
        timeline: timelineRef.current,
        prefersReducedMotion,
      }}
    >
      <div ref={containerRef} data-scroll-container className="relative">
        {children}
      </div>
    </ScrollControllerContext.Provider>
  );
};

export default ScrollController;

export const useScrollController = () => {
  const context = React.useContext(ScrollControllerContext);
  if (!context) {
    throw new Error("useScrollController must be used within ScrollController");
  }
  return context;
};

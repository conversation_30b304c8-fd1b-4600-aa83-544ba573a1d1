"use client";

import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

/**
 * Aura Stack Component
 *
 * Optimized interactive stack visualization with GSAP scroll animations.
 * Features performance optimizations, accessibility improvements, and responsive design.
 */

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

interface AuraStackProps {
  /** Optional CSS class name */
  className?: string;
  /** Callback when stage changes */
  onStageChange?: (stage: number) => void;
  /** Whether to enable scroll animations */
  enableAnimations?: boolean;
}

const STAGES: Array<{ title: string; copy: string; id: string }> = [
  {
    id: "data",
    title: "DATA",
    copy: "Aggregate EMR, LIS, and PACS data into a unified pipeline.",
  },
  {
    id: "context",
    title: "CONTEXT",
    copy: "Normalize to FHIR, map entities, and establish provenance.",
  },
  {
    id: "insight",
    title: "INSIGHT",
    copy: "Summarize longitudinal signals into actionable insights.",
  },
  {
    id: "coaching",
    title: "COACHING",
    copy: "Surface quick wins and cohort-based guidance for clinicians.",
  },
  {
    id: "orchestration",
    title: "ORCHESTRATION",
    copy: "Automate follow-ups and task routing across the care team.",
  },
  {
    id: "aura-guardian",
    title: "AURA GUARDIAN",
    copy: "Always-on safety checks and guidance—human-in-the-loop by design.",
  },
];

const AuraStack: React.FC<AuraStackProps> = ({
  className = "",
  onStageChange,
  enableAnimations = true,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const stackRef = useRef<HTMLDivElement>(null);
  const platesRef = useRef<(HTMLDivElement | null)[]>([]);
  const [currentStage, setCurrentStage] = useState(-1);
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
  const [isIntersecting, setIsIntersecting] = useState(false);

  const visualLayers = useMemo(
    () => [
      { id: "base", title: "Foundation", path: "/aura-stack/00-base.png" },
      { id: "data", title: "DATA", path: "/aura-stack/01-data.png" },
      { id: "context", title: "CONTEXT", path: "/aura-stack/02-context.png" },
      { id: "insight", title: "INSIGHT", path: "/aura-stack/03-insight.png" },
      {
        id: "coaching",
        title: "COACHING",
        path: "/aura-stack/04-coaching.png",
      },
      {
        id: "orchestration",
        title: "ORCHESTRATION",
        path: "/aura-stack/05-orchestration.png",
      },
      { id: "aura", title: "AURA GUARDIAN", path: "/aura-stack/06-aura.png" },
      { id: "cap", title: "Entheory", path: "/aura-stack/07-cap.png" },
    ],
    []
  );

  const CALIB: Record<string, { scale: number; y: number }> = useMemo(
    () => ({
      base: { scale: 1, y: 0 },
      data: { scale: 1, y: 0 },
      context: { scale: 1, y: 0 },
      insight: { scale: 1, y: 0 },
      coaching: { scale: 1, y: 0 },
      orchestration: { scale: 1, y: 0 },
      aura: { scale: 1, y: 0 },
      cap: { scale: 1, y: 0 },
    }),
    []
  );

  // Handle stage changes with callback
  const handleStageChange = useCallback(
    (stage: number) => {
      setCurrentStage(stage);
      onStageChange?.(stage);
    },
    [onStageChange]
  );

  // Intersection observer for performance
  useEffect(() => {
    if (!containerRef.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
      },
      { threshold: 0.1, rootMargin: "50px" }
    );

    observer.observe(containerRef.current);
    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
    setPrefersReducedMotion(mediaQuery.matches);
    const handleChange = (e: MediaQueryListEvent) =>
      setPrefersReducedMotion(e.matches);
    mediaQuery.addEventListener("change", handleChange);

    // Optimize for mobile performance
    const isMobile = window.matchMedia("(max-width: 768px)").matches;
    const isTouch = "ontouchstart" in window;

    if (isMobile || isTouch || !enableAnimations) {
      setPrefersReducedMotion(true);
    }

    return () => mediaQuery.removeEventListener("change", handleChange);
  }, [enableAnimations]);

  useEffect(() => {
    if (!stackRef.current || !prefersReducedMotion || !isIntersecting) return;
    const plates = platesRef.current.filter(Boolean) as HTMLDivElement[];
    const plateSpacing = 16; // Reduced spacing for better mobile experience
    const firstPlateY = 0 - plateSpacing;
    const lastPlateY = 0 - 6 * plateSpacing;

    plates.forEach((plate, index) => {
      let y = 0;
      let z = 0;
      if (index === 0) {
        y = firstPlateY + plateSpacing;
        z = 0;
      } else if (index === 7) {
        y = lastPlateY - plateSpacing;
        z = 7 * 20; // Reduced z-spacing
      } else {
        y = 0 - index * plateSpacing + 2;
        z = index * 20;
      }
      gsap.set(plate, {
        opacity: 1,
        y,
        scale: 1,
        rotateX: -15, // Reduced rotation for better visibility
        z,
        force3D: true, // Hardware acceleration
      });
    });
    handleStageChange(0);
  }, [prefersReducedMotion, isIntersecting, handleStageChange]);

  useEffect(() => {
    if (
      !containerRef.current ||
      !stackRef.current ||
      prefersReducedMotion ||
      !isIntersecting ||
      !enableAnimations
    )
      return;
    const container = containerRef.current;
    const plates = platesRef.current.filter(Boolean) as HTMLDivElement[];
    ScrollTrigger.getAll().forEach((trigger) => trigger.kill());

    plates.forEach((plate, index) => {
      if (index === 0) {
        gsap.set(plate, {
          opacity: 0,
          y: 400, // Reduced initial offset
          scale: 0.95,
          rotateX: -20,
          z: 0,
          force3D: true,
        });
      } else if (index === 7) {
        gsap.set(plate, {
          opacity: 0,
          y: -400, // Reduced initial offset
          scale: 0.95,
          rotateX: -20,
          z: 200, // Reduced z-depth
          force3D: true,
        });
      } else {
        gsap.set(plate, {
          opacity: 0,
          y: 280 + index * 16, // Reduced spacing
          scale: 0.9,
          rotateX: -20,
          z: 0,
          force3D: true,
        });
      }
    });

    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: container,
        start: "top top",
        end: "+=300%", // Reduced scroll distance
        scrub: 0.8, // Slightly smoother scrubbing
        pin: true,
        pinSpacing: true,
        anticipatePin: 1,
        invalidateOnRefresh: true,
        refreshPriority: -1, // Lower priority for better performance
        onUpdate: (self) => {
          const progress = self.progress;
          const stageIndex = Math.min(Math.floor(progress * 7), 6);
          if (stageIndex !== currentStage) {
            handleStageChange(stageIndex);
          }
        },
      },
    });

    const segmentDuration = 1 / 8;
    for (let i = 1; i <= 6; i++) {
      const plate = plates[i];
      const segmentStart = (i - 1) * segmentDuration * 1.1; // Slightly faster
      const plateSpacing = 16; // Consistent with earlier change
      const stackY = 0 - i * plateSpacing;

      // Main plate animation - simplified
      tl.to(
        plate,
        {
          opacity: 1,
          y: stackY,
          scale: 1,
          rotateX: -15, // Consistent with earlier change
          z: i * 20, // Consistent with earlier change
          duration: segmentDuration * 1.1,
          ease: "power2.inOut",
          force3D: true,
        },
        segmentStart
      );

      // Simplified bounce effect
      tl.to(
        plate,
        {
          y: stackY - 2,
          duration: segmentDuration * 0.1,
          ease: "power2.out",
        },
        segmentStart + segmentDuration * 0.9
      );
      tl.to(
        plate,
        {
          y: stackY,
          duration: segmentDuration * 0.1,
          ease: "power2.inOut",
        },
        segmentStart + segmentDuration * 1.0
      );

      // Simplified previous plates adjustment
      if (i > 1) {
        for (let j = 1; j < i; j++) {
          const prevY = 0 - j * plateSpacing;
          tl.to(
            plates[j],
            {
              y: prevY + 1,
              duration: segmentDuration * 0.3,
              ease: "power2.inOut",
              force3D: true,
            },
            segmentStart
          );
        }
      }
    }

    const finalSegmentStart = 6 * segmentDuration * 1.1 + 0.1;
    const plateSpacing = 16; // Consistent spacing
    const firstPlateY = 0 - plateSpacing;
    const lastPlateY = 0 - 6 * plateSpacing;
    const baseY = firstPlateY + plateSpacing;
    const capY = lastPlateY - plateSpacing;

    // Final assembly animation - simplified
    tl.to(
      plates[0],
      {
        opacity: 1,
        y: baseY,
        scale: 1,
        rotateX: -15,
        z: 0,
        duration: segmentDuration * 1.2,
        ease: "power2.inOut",
        force3D: true,
      },
      finalSegmentStart
    ).to(
      plates[7],
      {
        opacity: 1,
        y: capY,
        scale: 1,
        rotateX: -15,
        z: 7 * 20, // Consistent z-spacing
        duration: segmentDuration * 1.2,
        ease: "power2.inOut",
        force3D: true,
      },
      finalSegmentStart
    );

    // Tighten the stack
    for (let j = 1; j <= 6; j++) {
      const tightY = 0 - j * plateSpacing + 2;
      tl.to(
        plates[j],
        {
          y: tightY,
          duration: segmentDuration * 0.6,
          ease: "power2.inOut",
          force3D: true,
        },
        finalSegmentStart
      );
    }

    // Simplified glow effect
    tl.to(
      [plates[0], plates[7]],
      {
        filter: "drop-shadow(0 0 24px rgba(124, 58, 237, 0.5))",
        duration: segmentDuration * 0.3,
        ease: "power2.inOut",
      },
      finalSegmentStart + segmentDuration * 1.0
    );

    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    };
  }, [
    prefersReducedMotion,
    isIntersecting,
    enableAnimations,
    currentStage,
    handleStageChange,
  ]);

  return (
    <section
      id="aura-stack"
      ref={containerRef}
      className={`relative overflow-hidden ${className}`}
      style={{ minHeight: "100vh" }}
      aria-label="Aura Stack visualization"
    >
      {/* Optimized background image */}
      <div className="absolute inset-0 -z-10" aria-hidden="true">
        <picture>
          <source
            media="(max-width: 768px)"
            srcSet="/media/aura/aura-bg-mobile.png"
            type="image/png"
          />
          <img
            src="/media/aura/aura-bg-desktop.png"
            alt=""
            className="h-full w-full object-cover"
            style={{
              backgroundSize: "cover",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat",
            }}
            loading="lazy"
            decoding="async"
          />
        </picture>
      </div>
      {/* Optimized animated background layers */}
      {!prefersReducedMotion && (
        <>
          <style>
            {`
              @keyframes gradientShift {
                0%, 100% { transform: translate3d(0,0,0) scale(1); }
                50% { transform: translate3d(-1%, 0.5%, 0) scale(1.01); }
              }
              @keyframes driftSlow {
                0%, 100% { transform: translate3d(0,0,0) rotate(0deg); }
                50% { transform: translate3d(2%, -1%, 0) rotate(4deg); }
              }
            `}
          </style>
          <div
            className="pointer-events-none absolute inset-0 overflow-hidden"
            aria-hidden="true"
          >
            {/* Simplified aurora panel */}
            <div
              className="absolute -inset-16 opacity-70"
              style={{
                background:
                  "radial-gradient(800px 600px at 20% 20%, rgba(255,183,135,0.3), transparent 60%), radial-gradient(700px 500px at 80% 80%, rgba(255,176,193,0.25), transparent 60%)",
                filter: "blur(1px)",
                animation: enableAnimations
                  ? "gradientShift 20s ease-in-out infinite"
                  : "none",
              }}
            />
            {/* Simplified floating orbs */}
            <div
              className="absolute -left-20 -top-32 h-[40vw] w-[40vw] rounded-full blur-2xl"
              style={{
                background:
                  "radial-gradient(circle at 40% 40%, rgba(255,176,193,0.4), transparent 60%)",
                opacity: 0.5,
                animation: enableAnimations
                  ? "driftSlow 30s ease-in-out infinite"
                  : "none",
              }}
            />
            <div
              className="absolute -bottom-32 -right-20 h-[35vw] w-[35vw] rounded-full blur-2xl"
              style={{
                background:
                  "radial-gradient(circle at 60% 40%, rgba(255,236,153,0.35), transparent 60%)",
                opacity: 0.45,
                animation: enableAnimations
                  ? "driftSlow 25s ease-in-out infinite reverse"
                  : "none",
              }}
            />
            {/* Subtle vignette */}
            <div
              className="absolute inset-0"
              style={{
                background:
                  "radial-gradient(100% 70% at 50% 50%, transparent 50%, rgba(0,0,0,0.04) 100%)",
              }}
            />
          </div>
        </>
      )}

      <div className="relative z-10 flex min-h-screen items-center py-8 sm:py-12 lg:py-0">
        <div className="container mx-auto w-full px-4 sm:px-6 lg:px-8 xl:px-12">
          <div className="grid items-center gap-6 lg:grid-cols-2 lg:gap-16">
            {/* Text Content - Optimized Typography */}
            <div className="relative z-10 order-2 text-center lg:order-1 lg:text-left">
              <h2 className="mb-4 text-3xl font-light leading-tight text-stone-900 sm:text-4xl md:text-5xl lg:mb-6 lg:text-6xl xl:text-7xl">
                The Aura
                <br />
                <em className="font-light italic">Stack.</em>
              </h2>
              <p className="mx-auto max-w-lg text-base font-light leading-relaxed text-stone-700/90 sm:text-lg lg:mx-0 lg:text-lg xl:text-xl">
                Orchestrating oncology care with AI. From scattered signals to
                steady care—one intelligent system.
              </p>
            </div>

            {/* Visual Stack - Optimized for Performance */}
            <div className="relative order-1 flex min-h-[320px] items-center justify-center sm:min-h-[400px] lg:order-2 lg:min-h-[480px]">
              <div
                ref={stackRef}
                className="sm:scale-85 relative scale-75 lg:scale-95 xl:scale-100"
                style={{
                  transformStyle: "preserve-3d",
                  perspective: "1200px", // Reduced for better mobile performance
                  perspectiveOrigin: "50% 50%",
                }}
                role="img"
                aria-label="Interactive 3D stack visualization"
              >
                {visualLayers.map((layer, index) => (
                  <div
                    key={layer.id}
                    ref={(el) => (platesRef.current[index] = el)}
                    className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
                    style={{
                      width: "320px", // Reduced size for better mobile performance
                      height: "320px",
                      transformStyle: "preserve-3d",
                      willChange: prefersReducedMotion
                        ? "auto"
                        : "transform, opacity",
                    }}
                  >
                    <img
                      src={layer.path}
                      alt={layer.title}
                      className="h-full w-full origin-bottom object-contain"
                      style={{
                        transform: `scale(${CALIB[layer.id]?.scale ?? 1}) translateY(${CALIB[layer.id]?.y ?? 0}px)`,
                        filter: `drop-shadow(0 ${8 + index * 1}px ${16 + index * 2}px rgba(0,0,0,${0.08 + index * 0.015}))`, // Reduced shadow intensity
                      }}
                      loading="lazy"
                      decoding="async"
                    />
                  </div>
                ))}
              </div>

              {/* Stage Information - Desktop */}
              <div className="absolute left-full top-1/2 z-20 ml-6 hidden w-72 -translate-y-1/2 lg:block xl:ml-8 xl:w-80">
                {currentStage >= 0 && currentStage < 6 && (
                  <div
                    className="transition-all duration-300"
                    role="region"
                    aria-live="polite"
                    aria-label={`Current stage: ${STAGES[currentStage].title}`}
                  >
                    <div className="relative flex items-center">
                      <div className="absolute -left-12 flex items-center xl:-left-16">
                        <div className="relative h-8 w-10 xl:h-10 xl:w-12">
                          <div className="absolute left-0 top-1/2 h-px w-6 bg-gray-900 xl:w-8" />
                          <div className="absolute left-6 top-1/2 h-1.5 w-1.5 -translate-y-1/2 rounded-full bg-gray-900 xl:left-8" />
                        </div>
                      </div>
                      <div className="pl-2">
                        <h3 className="mb-2 text-lg font-light text-stone-900 xl:mb-3 xl:text-xl">
                          {STAGES[currentStage].title}
                        </h3>
                        <p className="max-w-sm text-sm font-light leading-relaxed text-stone-700/90 xl:max-w-md xl:text-base">
                          {STAGES[currentStage].copy}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Mobile Stage Information - Optimized */}
              <div className="absolute -bottom-12 left-1/2 w-full max-w-sm -translate-x-1/2 px-4 lg:hidden">
                {currentStage >= 0 && currentStage < 6 && (
                  <div
                    className="rounded-lg bg-white/95 p-3 text-center shadow-lg backdrop-blur-sm transition-all duration-300"
                    role="region"
                    aria-live="polite"
                    aria-label={`Current stage: ${STAGES[currentStage].title}`}
                  >
                    <h3 className="mb-1 text-base font-medium text-stone-900">
                      {STAGES[currentStage].title}
                    </h3>
                    <p className="text-xs leading-relaxed text-stone-700/90">
                      {STAGES[currentStage].copy}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

AuraStack.displayName = "AuraStack";

export default AuraStack;
export type { AuraStackProps };

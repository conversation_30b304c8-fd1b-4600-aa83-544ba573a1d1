"use client";

import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import React, { useEffect, useRef, useState } from "react";

/**
 * Aura Stack Component
 *
 * Interactive stack visualization with GSAP scroll animations.
 * Displays data processing stages with smooth transitions and hover effects.
 */

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

const STAGES: Array<{ title: string; copy: string }> = [
  {
    title: "DATA",
    copy: "Aggregate EMR, LIS, and PACS data into a unified pipeline.",
  },
  {
    title: "CONTEXT",
    copy: "Normalize to FHIR, map entities, and establish provenance.",
  },
  {
    title: "INSIGHT",
    copy: "Summarize longitudinal signals into actionable insights.",
  },
  {
    title: "COACHING",
    copy: "Surface quick wins and cohort-based guidance for clinicians.",
  },
  {
    title: "ORCHESTRATION",
    copy: "Automate follow-ups and task routing across the care team.",
  },
  {
    title: "AURA GUARDIAN",
    copy: "Always-on safety checks and guidance—human-in-the-loop by design.",
  },
];

const AuraStack: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const stackRef = useRef<HTMLDivElement>(null);
  const platesRef = useRef<(HTMLDivElement | null)[]>([]);
  const [currentStage, setCurrentStage] = useState(-1);
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  const visualLayers = [
    { id: "base", title: "Foundation", path: "/aura-stack/00-base.png" },
    { id: "data", title: "DATA", path: "/aura-stack/01-data.png" },
    { id: "context", title: "CONTEXT", path: "/aura-stack/02-context.png" },
    { id: "insight", title: "INSIGHT", path: "/aura-stack/03-insight.png" },
    { id: "coaching", title: "COACHING", path: "/aura-stack/04-coaching.png" },
    {
      id: "orchestration",
      title: "ORCHESTRATION",
      path: "/aura-stack/05-orchestration.png",
    },
    { id: "aura", title: "AURA GUARDIAN", path: "/aura-stack/06-aura.png" },
    { id: "cap", title: "Entheory", path: "/aura-stack/07-cap.png" },
  ];

  const CALIB: Record<string, { scale: number; y: number }> = {
    base: { scale: 1, y: 0 },
    data: { scale: 1, y: 0 },
    context: { scale: 1, y: 0 },
    insight: { scale: 1, y: 0 },
    coaching: { scale: 1, y: 0 },
    orchestration: { scale: 1, y: 0 },
    aura: { scale: 1, y: 0 },
    cap: { scale: 1, y: 0 },
  };

  useEffect(() => {
    const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
    setPrefersReducedMotion(mediaQuery.matches);
    const handleChange = (e: MediaQueryListEvent) =>
      setPrefersReducedMotion(e.matches);
    mediaQuery.addEventListener("change", handleChange);

    // Optimize for mobile performance
    const isMobile = window.matchMedia("(max-width: 768px)").matches;
    const isTouch = "ontouchstart" in window;

    if (isMobile || isTouch) {
      setPrefersReducedMotion(true);
    }

    return () => mediaQuery.removeEventListener("change", handleChange);
  }, []);

  useEffect(() => {
    if (!stackRef.current || !prefersReducedMotion) return;
    const plates = platesRef.current.filter(Boolean) as HTMLDivElement[];
    const plateSpacing = 20;
    const firstPlateY = 0 - plateSpacing;
    const lastPlateY = 0 - 6 * plateSpacing;
    plates.forEach((plate, index) => {
      let y = 0;
      let z = 0;
      if (index === 0) {
        y = firstPlateY + plateSpacing;
        z = 0;
      } else if (index === 7) {
        y = lastPlateY - plateSpacing;
        z = 7 * 25;
      } else {
        y = 0 - index * plateSpacing + 2;
        z = index * 25;
      }
      gsap.set(plate, { opacity: 1, y, scale: 1, rotateX: -20, z });
    });
    setCurrentStage(0);
  }, [prefersReducedMotion]);

  useEffect(() => {
    if (!containerRef.current || !stackRef.current || prefersReducedMotion)
      return;
    const container = containerRef.current;
    const plates = platesRef.current.filter(Boolean) as HTMLDivElement[];
    ScrollTrigger.getAll().forEach((trigger) => trigger.kill());

    plates.forEach((plate, index) => {
      if (index === 0) {
        gsap.set(plate, { opacity: 0, y: 500, scale: 0.9, rotateX: -25, z: 0 });
      } else if (index === 7) {
        gsap.set(plate, {
          opacity: 0,
          y: -500,
          scale: 0.9,
          rotateX: -25,
          z: 240,
        });
      } else {
        gsap.set(plate, {
          opacity: 0,
          y: 350 + index * 20,
          scale: 0.85,
          rotateX: -25,
          z: 0,
        });
      }
    });

    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: container,
        start: "top top",
        end: "+=400%",
        scrub: 1,
        pin: true,
        pinSpacing: true,
        anticipatePin: 1,
        invalidateOnRefresh: true,
        onUpdate: (self) => {
          const progress = self.progress;
          const stageIndex = Math.min(Math.floor(progress * 7), 6);
          setCurrentStage(stageIndex);
        },
      },
    });

    const segmentDuration = 1 / 8;
    for (let i = 1; i <= 6; i++) {
      const plate = plates[i];
      const segmentStart = (i - 1) * segmentDuration * 1.2;
      const plateSpacing = 20;
      const stackY = 0 - i * plateSpacing;
      tl.to(
        plate,
        {
          opacity: 1,
          y: stackY,
          scale: 1,
          rotateX: -20,
          z: i * 25,
          duration: segmentDuration * 1.2,
          ease: "power2.inOut",
        },
        segmentStart
      );
      tl.to(
        plate,
        { y: stackY - 3, duration: segmentDuration * 0.15, ease: "power2.out" },
        segmentStart + segmentDuration * 1.0
      );
      tl.to(
        plate,
        {
          y: stackY,
          duration: segmentDuration * 0.15,
          ease: "elastic.out(1, 0.5)",
        },
        segmentStart + segmentDuration * 1.15
      );
      if (i > 1) {
        for (let j = 1; j < i; j++) {
          const prevY = 0 - j * plateSpacing;
          tl.to(
            plates[j],
            {
              y: prevY + 1,
              duration: segmentDuration * 0.4,
              ease: "power2.inOut",
            },
            segmentStart
          );
        }
      }
    }

    const finalSegmentStart = 6 * segmentDuration * 1.2 + 0.1;
    const plateSpacing = 20;
    const firstPlateY = 0 - plateSpacing;
    const lastPlateY = 0 - 6 * plateSpacing;
    const baseY = firstPlateY + plateSpacing;
    const capY = lastPlateY - plateSpacing;

    tl.to(
      plates[0],
      {
        opacity: 1,
        y: baseY,
        scale: 1,
        rotateX: -20,
        z: 0,
        duration: segmentDuration * 1.5,
        ease: "power3.inOut",
      },
      finalSegmentStart
    ).to(
      plates[7],
      {
        opacity: 1,
        y: capY,
        scale: 1,
        rotateX: -20,
        z: 7 * 25,
        duration: segmentDuration * 1.5,
        ease: "power3.inOut",
      },
      finalSegmentStart
    );
    for (let j = 1; j <= 6; j++) {
      const tightY = 0 - j * plateSpacing + 2;
      tl.to(
        plates[j],
        { y: tightY, duration: segmentDuration * 0.8, ease: "power2.inOut" },
        finalSegmentStart
      );
    }

    tl.to(
      [plates[0], plates[7]],
      {
        filter: "drop-shadow(0 0 40px rgba(124, 58, 237, 0.6))",
        duration: segmentDuration * 0.2,
        ease: "power2.inOut",
      },
      finalSegmentStart + segmentDuration * 1.3
    ).to(
      [plates[0], plates[7]],
      {
        filter: "drop-shadow(0 0 20px rgba(124, 58, 237, 0.4))",
        duration: segmentDuration * 0.2,
        ease: "power2.out",
      },
      finalSegmentStart + segmentDuration * 1.5
    );

    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    };
  }, [prefersReducedMotion]);

  return (
    <section
      id="s2"
      ref={containerRef}
      className="relative overflow-hidden"
      style={{ minHeight: "100vh" }}
    >
      {/* Background image fill */}
      <div
        className="absolute inset-0 -z-10"
        aria-hidden
        style={{
          backgroundImage: "url(/media/aura/aura-bg-desktop.png)",
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      />
      {/* Animated background layers */}
      <style>
        {`
          @keyframes gradientShift {
            0% { transform: translate3d(0,0,0) scale(1); }
            50% { transform: translate3d(-2%, 1%, 0) scale(1.02); }
            100% { transform: translate3d(0,0,0) scale(1); }
          }
          @keyframes driftSlow {
            0% { transform: translate3d(0,0,0) rotate(0deg); }
            50% { transform: translate3d(3%, -2%, 0) rotate(8deg); }
            100% { transform: translate3d(0,0,0) rotate(0deg); }
          }
        `}
      </style>
      <div className="pointer-events-none absolute inset-0 overflow-hidden">
        {/* Soft aurora panel (peach/cream theme) */}
        <div
          className="absolute -inset-24 opacity-85"
          style={{
            background:
              "radial-gradient(1200px 820px at 14% 18%, rgba(255,183,135,0.42), transparent 62%), radial-gradient(1100px 760px at 86% 86%, rgba(255,176,193,0.38), transparent 62%), radial-gradient(980px 680px at 52% 95%, rgba(255,236,153,0.34), transparent 68%)",
            filter: "saturate(1.12) blur(0.5px)",
            animation: "gradientShift 18s ease-in-out infinite",
          }}
        />
        {/* Floating glow orbs (soft) */}
        <div
          className="absolute -left-28 -top-44 h-[64vw] w-[64vw] rounded-full blur-3xl"
          style={{
            background:
              "radial-gradient(circle at 40% 40%, rgba(255,176,193,0.50), transparent 60%)",
            opacity: 0.65,
            animation: "driftSlow 26s ease-in-out infinite",
          }}
        />
        <div
          className="absolute -bottom-52 -right-28 h-[58vw] w-[58vw] rounded-full blur-3xl"
          style={{
            background:
              "radial-gradient(circle at 60% 40%, rgba(255,236,153,0.45), transparent 60%)",
            opacity: 0.62,
            animation: "driftSlow 22s ease-in-out infinite reverse",
          }}
        />
        {/* Soft conic glow (no grids/lines) */}
        <div
          className="absolute inset-0"
          style={{
            background:
              "conic-gradient(from 160deg at 50% 50%, rgba(255,183,135,0.20), rgba(255,176,193,0.20), rgba(255,236,153,0.20), rgba(255,183,135,0.20))",
            mixBlendMode: "screen",
            opacity: 0.22,
          }}
        />
        {/* Vignette for depth */}
        <div
          className="absolute inset-0"
          style={{
            background:
              "radial-gradient(120% 80% at 50% 50%, transparent 60%, rgba(0,0,0,0.06) 100%)",
            pointerEvents: "none",
          }}
        />
      </div>

      <div className="relative z-10 flex min-h-screen items-center py-12 sm:py-16 lg:py-0">
        <div className="container mx-auto w-full px-4 sm:px-6 lg:px-8 xl:px-12">
          <div className="grid items-center gap-8 lg:grid-cols-2 lg:gap-20">
            {/* Text Content - Mobile First */}
            <div className="relative z-10 order-2 text-center lg:order-1 lg:text-left">
              <h2 className="mb-6 text-4xl font-light leading-none text-stone-900 sm:text-5xl md:text-6xl lg:mb-8 lg:text-7xl xl:text-8xl">
                The Aura
                <br />
                <em className="font-light italic">Stack.</em>
              </h2>
              <p className="mx-auto max-w-lg text-lg font-light leading-relaxed text-stone-700/90 sm:text-xl lg:mx-0 lg:text-xl xl:text-2xl">
                Orchestrating oncology care with AI. From scattered signals to
                steady care—one intelligent system.
              </p>
            </div>

            {/* Visual Stack - Mobile Friendly */}
            <div className="relative order-1 flex min-h-[400px] items-center justify-center sm:min-h-[500px] lg:order-2 lg:min-h-[600px]">
              <div
                ref={stackRef}
                className="relative scale-75 sm:scale-90 lg:scale-100"
                style={{
                  transformStyle: "preserve-3d",
                  perspective: "1500px",
                  perspectiveOrigin: "50% 50%",
                }}
              >
                {visualLayers.map((layer, index) => (
                  <div
                    key={layer.id}
                    ref={(el) => (platesRef.current[index] = el)}
                    className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
                    style={{
                      width: "400px",
                      height: "400px",
                      transformStyle: "preserve-3d",
                      willChange: "transform, opacity",
                    }}
                  >
                    <img
                      src={layer.path}
                      alt={layer.title}
                      className="h-full w-full origin-bottom object-contain"
                      style={{
                        transform: `scale(${CALIB[layer.id]?.scale ?? 1}) translateY(${CALIB[layer.id]?.y ?? 0}px)`,
                        filter: `drop-shadow(0 ${10 + index * 2}px ${20 + index * 3}px rgba(0,0,0,${0.1 + index * 0.02}))`,
                      }}
                    />
                  </div>
                ))}
              </div>

              {/* Stage Information - Mobile Responsive */}
              <div className="absolute left-full top-1/2 z-20 ml-8 hidden w-80 -translate-y-1/2 lg:block xl:ml-12 xl:w-96">
                {currentStage >= 0 && currentStage < 6 && (
                  <div className="transition-all duration-500">
                    <div className="relative flex items-center">
                      <div className="absolute -left-16 flex items-center xl:-left-20">
                        <div className="relative h-10 w-12 xl:h-12 xl:w-16">
                          <div className="absolute left-0 top-1/2 h-0.5 w-8 bg-gray-900 xl:w-12" />
                          <div className="absolute left-8 top-1/2 h-2 w-2 -translate-y-1/2 rounded-full bg-gray-900 xl:left-12" />
                        </div>
                      </div>
                      <div className="pl-2">
                        <h3 className="mb-2 text-xl font-light text-stone-900 xl:mb-3 xl:text-2xl">
                          {STAGES[currentStage].title}
                        </h3>
                        <p className="max-w-sm text-base font-light leading-relaxed text-stone-700/90 xl:max-w-md xl:text-lg">
                          {STAGES[currentStage].copy}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Mobile Stage Information - Below stack */}
              <div className="absolute -bottom-16 left-1/2 w-full max-w-md -translate-x-1/2 px-4 lg:hidden">
                {currentStage >= 0 && currentStage < 6 && (
                  <div className="rounded-xl bg-white/90 p-4 text-center shadow-lg backdrop-blur-sm transition-all duration-500">
                    <h3 className="mb-2 text-lg font-medium text-stone-900">
                      {STAGES[currentStage].title}
                    </h3>
                    <p className="text-sm leading-relaxed text-stone-700/90">
                      {STAGES[currentStage].copy}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AuraStack;

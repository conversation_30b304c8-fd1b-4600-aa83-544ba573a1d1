import React, { useEffect, useRef } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

const Section3To4Transition: React.FC = () => {
  const transitionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!transitionRef.current) return;

    const section3 = document.querySelector("#s3");
    const section4 = document.querySelector('[data-section="4"]');

    if (!section3 || !section4) return;

    // Create smooth crossfade transition
    ScrollTrigger.create({
      trigger: transitionRef.current,
      start: "top bottom",
      end: "bottom top",
      scrub: 1,
      onUpdate: (self) => {
        const progress = self.progress;

        // Fade out Section 3 modules
        const modules = section3.querySelectorAll(".bg-white");
        modules.forEach((module, i) => {
          gsap.set(module, {
            opacity: 1 - progress * 0.8,
            y: progress * -20,
            scale: 1 - progress * 0.05,
          });
        });

        // Start revealing Section 4
        if (progress > 0.3) {
          const revealProgress = (progress - 0.3) / 0.7;
          gsap.set(section4, {
            opacity: revealProgress,
            visibility: revealProgress > 0 ? "visible" : "hidden",
          });
        }
      },
    });

    return () => {
      ScrollTrigger.getAll().forEach((st) => {
        if (st.trigger === transitionRef.current) {
          st.kill();
        }
      });
    };
  }, []);

  return (
    <div ref={transitionRef} className="relative h-[30vh]" aria-hidden="true">
      {/* Gradient transition overlay */}
      <div
        className="absolute inset-0"
        style={{
          background: "linear-gradient(180deg, transparent 0%, #faf7f2 100%)",
        }}
      />
    </div>
  );
};

export default Section3To4Transition;

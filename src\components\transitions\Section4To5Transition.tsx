import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import React, { memo, useCallback, useEffect, useRef, useState } from "react";

/**
 * Section 4 to 5 Transition Component
 *
 * Optimized transition between sections with performance improvements
 * and accessibility features.
 */

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

interface Section4To5TransitionProps {
  /** Optional CSS class name */
  className?: string;
  /** Whether to enable animations */
  enableAnimations?: boolean;
  /** Callback when transition completes */
  onTransitionComplete?: () => void;
}

const Section4To5Transition: React.FC<Section4To5TransitionProps> = memo(
  ({ className = "", enableAnimations = true, onTransitionComplete }) => {
    const transitionRef = useRef<HTMLDivElement>(null);
    const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
    const [isIntersecting, setIsIntersecting] = useState(false);

    // Check for reduced motion preference
    useEffect(() => {
      const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
      setPrefersReducedMotion(mediaQuery.matches);
      const handleChange = (e: MediaQueryListEvent) =>
        setPrefersReducedMotion(e.matches);
      mediaQuery.addEventListener("change", handleChange);
      return () => mediaQuery.removeEventListener("change", handleChange);
    }, []);

    // Intersection observer for performance
    useEffect(() => {
      if (!transitionRef.current) return;

      const observer = new IntersectionObserver(
        ([entry]) => setIsIntersecting(entry.isIntersecting),
        { threshold: 0.1, rootMargin: "50px" }
      );

      observer.observe(transitionRef.current);
      return () => observer.disconnect();
    }, []);

    const handleTransitionUpdate = useCallback(
      (progress: number) => {
        if (!enableAnimations || prefersReducedMotion) return;

        const section4 = document.querySelector('[data-section="4"]');
        const section5 = document.querySelector('[data-section="5"]');

        if (!section4 || !section5) return;

        // Optimized fade out Section 4 modules
        const modules = section4.querySelectorAll(".bg-white");
        modules.forEach((module, i) => {
          gsap.set(module, {
            opacity: 1 - progress * 0.7, // Gentler fade
            y: progress * -12, // Reduced movement
            scale: 1 - progress * 0.03, // Reduced scale change
            force3D: true,
          });
        });

        // Optimized Section 5 reveal
        if (progress > 0.4) {
          // Later trigger
          const revealProgress = (progress - 0.4) / 0.6;
          gsap.set(section5, {
            opacity: revealProgress,
            visibility: revealProgress > 0 ? "visible" : "hidden",
            force3D: true,
          });
        }

        // Call completion callback
        if (progress >= 1 && onTransitionComplete) {
          onTransitionComplete();
        }
      },
      [enableAnimations, prefersReducedMotion, onTransitionComplete]
    );

    useEffect(() => {
      if (
        !transitionRef.current ||
        !isIntersecting ||
        prefersReducedMotion ||
        !enableAnimations
      )
        return;

      const scrollTrigger = ScrollTrigger.create({
        trigger: transitionRef.current,
        start: "top bottom",
        end: "bottom top",
        scrub: 0.8, // Smoother scrubbing
        refreshPriority: -1,
        onUpdate: (self) => handleTransitionUpdate(self.progress),
      });

      return () => scrollTrigger.kill();
    }, [
      isIntersecting,
      prefersReducedMotion,
      enableAnimations,
      handleTransitionUpdate,
    ]);

    return (
      <div
        ref={transitionRef}
        className={`relative h-[24vh] ${className}`} // Reduced height
        aria-hidden="true"
      >
        {/* Optimized gradient transition overlay */}
        <div
          className="absolute inset-0"
          style={{
            background:
              "linear-gradient(180deg, transparent 0%, rgba(250,247,242,0.8) 100%)", // Reduced opacity
          }}
        />
      </div>
    );
  }
);

Section4To5Transition.displayName = "Section4To5Transition";

export default Section4To5Transition;
export type { Section4To5TransitionProps };

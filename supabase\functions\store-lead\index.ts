import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
    );

    const { source, email, name, role, hospital, city, phone, notes } =
      await req.json();

    console.log("Storing lead:", { source, email, name, hospital, city, role });

    // Validate required fields
    if (!email || !source) {
      return new Response(
        JSON.stringify({ error: "Email and source are required" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Insert lead into database
    const { data, error } = await supabaseClient
      .from("leads")
      .insert({
        source,
        email,
        name: name || null,
        role: role || null,
        hospital: hospital || null,
        city: city || null,
        phone: phone || null,
        notes: notes || null,
      })
      .select();

    if (error) {
      console.error("Database error:", error);
      return new Response(JSON.stringify({ error: "Failed to store lead" }), {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    console.log("Lead stored successfully:", data);

    return new Response(JSON.stringify({ success: true, data }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error("Error in store-lead function:", error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }
});

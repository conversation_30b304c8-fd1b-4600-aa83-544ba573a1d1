import { motion, useReducedMotion } from "framer-motion";
import {
  Clip<PERSON><PERSON>heck,
  Microscope,
  ShieldCheck,
  Stethoscope,
} from "lucide-react";
import React, { memo, useCallback, useMemo } from "react";

/**
 * Hero Flowchart Component
 *
 * Optimized interactive flowchart visualization for the hero section.
 * Features performance optimizations, accessibility improvements, and responsive design.
 */

interface FlowCard {
  id: number;
  label: string;
  title: string;
  chips: string[];
  icon: React.ReactNode;
  color: string;
  position: { top: string; right: string };
  branch?: "TRUE" | "FALSE";
}

interface HeroFlowchartProps {
  /** Optional CSS class name */
  className?: string;
  /** Callback when a card is clicked */
  onCardClick?: (cardId: number) => void;
  /** Whether to show animations */
  animated?: boolean;
}

const HeroFlowchart: React.FC<HeroFlowchartProps> = memo(
  ({ className = "", onCardClick, animated = true }) => {
    const prefersReducedMotion = useReducedMotion();
    const shouldAnimate = animated && !prefersReducedMotion;

    const cards: FlowCard[] = useMemo(
      () => [
        {
          id: 1,
          label: "PATIENT INTAKE & CONSENT",
          title: "Patient verified & consented",
          chips: ["ABDM linked", "Outside records attached"],
          icon: (
            <ClipboardCheck className="h-4 w-4 text-white" aria-hidden="true" />
          ),
          color: "bg-blue-500",
          position: { top: "0%", right: "8%" },
        },
        {
          id: 2,
          label: "RECONCILE RECORDS",
          title: "Unify history, labs & imaging into one view",
          chips: ["Latest regimen & lines", "Key labs & imaging summary"],
          icon: (
            <Stethoscope className="h-4 w-4 text-white" aria-hidden="true" />
          ),
          color: "bg-purple-500",
          position: { top: "25%", right: "4%" },
        },
        {
          id: 3,
          label: "TUMOR BOARD PREP",
          title: "Clinical summary ready",
          chips: ["Stage & biomarkers", "Performance status"],
          icon: (
            <Microscope className="h-4 w-4 text-white" aria-hidden="true" />
          ),
          color: "bg-blue-400",
          position: { top: "50%", right: "0%" },
          branch: "TRUE",
        },
        {
          id: 4,
          label: "PLAN & SAFETY",
          title: "Proposed regimen with checks",
          chips: ["DDI/allergy alerts", "Trial matches"],
          icon: (
            <ShieldCheck className="h-4 w-4 text-white" aria-hidden="true" />
          ),
          color: "bg-pink-500",
          position: { top: "75%", right: "8%" },
          branch: "FALSE",
        },
      ],
      []
    );

    const handleCardClick = useCallback(
      (cardId: number) => {
        onCardClick?.(cardId);
      },
      [onCardClick]
    );

    return (
      <div
        className={`relative h-[480px] w-full ${className}`}
        role="img"
        aria-label="Patient care workflow diagram"
      >
        {/* Vertical connector line */}
        <div
          className="absolute bottom-[15%] left-1/2 top-[15%] w-px bg-gray-300/50"
          aria-hidden="true"
        />

        {/* Branch indicator */}
        <div className="absolute left-1/2 top-[45%] -translate-x-1/2 -translate-y-1/2">
          <div
            className="flex items-center gap-6"
            role="group"
            aria-label="Decision branches"
          >
            <span
              className="rounded-full bg-gray-800/80 px-2.5 py-1 text-xs font-medium text-white shadow-sm"
              role="img"
              aria-label="True branch"
            >
              TRUE
            </span>
            <span
              className="rounded-full bg-gray-400/60 px-2.5 py-1 text-xs font-medium text-white shadow-sm"
              role="img"
              aria-label="False branch"
            >
              FALSE
            </span>
          </div>
        </div>

        {/* Cards */}
        {cards.map((card, index) => (
          <motion.div
            key={card.id}
            initial={shouldAnimate ? { opacity: 0, x: 32 } : false}
            animate={{ opacity: 1, x: 0 }}
            transition={{
              delay: shouldAnimate ? index * 0.08 : 0,
              duration: shouldAnimate ? 0.4 : 0,
              ease: [0.22, 1, 0.36, 1],
            }}
            className="absolute w-[320px] sm:w-[360px]"
            style={card.position}
          >
            <button
              className="w-full rounded-xl border border-white/20 bg-white/95 p-3 shadow-lg backdrop-blur transition-all duration-200 hover:scale-[1.02] hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2"
              onClick={() => handleCardClick(card.id)}
              aria-label={`${card.label}: ${card.title}`}
            >
              <div className="flex items-start gap-3">
                {/* Icon */}
                <div
                  className={`${card.color} flex-shrink-0 rounded-lg p-2 shadow-sm`}
                >
                  {card.icon}
                </div>

                {/* Content */}
                <div className="flex-1 text-left">
                  <div className="mb-1 text-[9px] font-semibold uppercase tracking-wider text-gray-500">
                    {card.label}
                  </div>
                  <div className="mb-2 text-sm font-medium leading-tight text-gray-900">
                    {card.title}
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {card.chips.map((chip) => (
                      <span
                        key={chip}
                        className="rounded bg-gray-100 px-2 py-0.5 text-xs text-gray-600"
                      >
                        {chip}
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              {/* Branch label if exists */}
              {card.branch && (
                <div className="absolute -left-12 top-1/2 -translate-y-1/2">
                  <span
                    className={`text-xs font-medium ${
                      card.branch === "TRUE" ? "text-gray-800" : "text-gray-500"
                    }`}
                    aria-label={`${card.branch} branch`}
                  >
                    {card.branch}
                  </span>
                </div>
              )}
            </button>
          </motion.div>
        ))}
      </div>
    );
  }
);

HeroFlowchart.displayName = "HeroFlowchart";

export default HeroFlowchart;

import { motion } from "framer-motion";
import {
  ClipboardCheck,
  Microscope,
  ShieldCheck,
  Stethoscope,
} from "lucide-react";
import React from "react";

/**
 * Hero Flowchart Component
 *
 * Interactive flowchart visualization for the hero section.
 * Displays patient care workflow with animated cards and icons.
 */

interface FlowCard {
  id: number;
  label: string;
  title: string;
  chips: string[];
  icon: React.ReactNode;
  color: string;
  position: { top: string; right: string };
}

const HeroFlowchart: React.FC = () => {
  const cards: FlowCard[] = [
    {
      id: 1,
      label: "PATIENT INTAKE & CONSENT",
      title: "Patient verified & consented",
      chips: ["ABDM linked", "Outside records attached"],
      icon: <ClipboardCheck className="h-5 w-5 text-white" />,
      color: "bg-blue-500",
      position: { top: "0%", right: "10%" },
    },
    {
      id: 2,
      label: "RECONCILE RECORDS",
      title: "Unify history, labs & imaging into one view",
      chips: ["Latest regimen & lines", "Key labs & imaging summary"],
      icon: <Stethoscope className="h-5 w-5 text-white" />,
      color: "bg-purple-500",
      position: { top: "25%", right: "5%" },
    },
    {
      id: 3,
      label: "TUMOR BOARD PREP",
      title: "Clinical summary ready",
      chips: ["Stage & biomarkers", "Performance status"],
      icon: <Microscope className="h-5 w-5 text-white" />,
      color: "bg-blue-400",
      position: { top: "50%", right: "0%" },
      branch: "TRUE",
    },
    {
      id: 4,
      label: "PLAN & SAFETY",
      title: "Proposed regimen with checks",
      chips: ["DDI/allergy alerts", "Trial matches"],
      icon: <ShieldCheck className="h-5 w-5 text-white" />,
      color: "bg-pink-500",
      position: { top: "75%", right: "10%" },
      branch: "FALSE",
    },
  ];

  return (
    <div className="relative h-[600px] w-full">
      {/* Vertical connector line */}
      <div className="absolute bottom-[15%] left-1/2 top-[15%] w-[1px] bg-gray-300/50"></div>

      {/* Branch indicator */}
      <div className="absolute left-1/2 top-[45%] -translate-x-1/2 -translate-y-1/2">
        <div className="flex items-center gap-8">
          <span className="rounded-full bg-gray-800/80 px-3 py-1 text-xs font-medium text-white">
            TRUE
          </span>
          <span className="rounded-full bg-gray-400/60 px-3 py-1 text-xs font-medium text-white">
            FALSE
          </span>
        </div>
      </div>

      {/* Cards */}
      {cards.map((card, index) => (
        <motion.div
          key={card.id}
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{
            delay: index * 0.1,
            duration: 0.5,
            ease: [0.22, 1, 0.36, 1],
          }}
          className="absolute w-[380px]"
          style={card.position}
        >
          <div className="rounded-xl border border-white/20 bg-white/95 p-4 shadow-lg backdrop-blur">
            <div className="flex items-start gap-3">
              {/* Icon */}
              <div className={`${card.color} rounded-lg p-2.5 shadow-sm`}>
                {card.icon}
              </div>

              {/* Content */}
              <div className="flex-1">
                <div className="mb-1 text-[10px] font-semibold uppercase tracking-wider text-gray-500">
                  {card.label}
                </div>
                <div className="mb-2 text-sm font-medium text-gray-900">
                  {card.title}
                </div>
                <div className="flex flex-wrap gap-1.5">
                  {card.chips.map((chip) => (
                    <span
                      key={chip}
                      className="rounded bg-gray-100 px-2 py-0.5 text-xs text-gray-600"
                    >
                      {chip}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* Branch label if exists */}
            {card.branch && (
              <div className="absolute -left-16 top-1/2 -translate-y-1/2">
                <span
                  className={`text-xs font-medium ${
                    card.branch === "TRUE" ? "text-gray-800" : "text-gray-500"
                  }`}
                >
                  {card.branch}
                </span>
              </div>
            )}
          </div>
        </motion.div>
      ))}
    </div>
  );
};

export default HeroFlowchart;

import { motion } from "framer-motion";
import React from "react";

/**
 * Loading States Components
 *
 * Collection of loading state components including skeletons, spinners, and lazy loading utilities.
 * Provides consistent loading experiences across the application.
 */

export const SectionSkeleton: React.FC = () => (
  <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
    <div className="container-optimized">
      <div className="grid items-center gap-8 lg:grid-cols-2">
        {/* Text skeleton */}
        <div className="space-y-3">
          <div className="h-8 w-3/4 animate-pulse rounded-lg bg-gray-200" />
          <div className="h-8 w-2/3 animate-pulse rounded-lg bg-gray-200" />
          <div className="h-5 w-full animate-pulse rounded-lg bg-gray-200" />
          <div className="h-5 w-5/6 animate-pulse rounded-lg bg-gray-200" />
          <div className="mt-4 flex gap-3">
            <div className="h-10 w-28 animate-pulse rounded-lg bg-gray-200" />
            <div className="h-10 w-28 animate-pulse rounded-lg bg-gray-200" />
          </div>
        </div>

        {/* Image skeleton */}
        <div className="h-80 animate-pulse rounded-xl bg-gray-200" />
      </div>
    </div>
  </div>
);

export const ImageSkeleton: React.FC<{ className?: string }> = ({
  className = "",
}) => (
  <div className={`animate-pulse bg-gray-200 ${className}`}>
    <div className="flex h-full w-full items-center justify-center">
      <svg
        className="h-8 w-8 text-gray-400"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
        />
      </svg>
    </div>
  </div>
);

export const CardSkeleton: React.FC = () => (
  <div className="animate-pulse rounded-xl border border-gray-200 bg-white p-6">
    <div className="mb-4 flex items-start justify-between">
      <div className="h-6 w-32 rounded bg-gray-200" />
      <div className="h-6 w-20 rounded-full bg-gray-200" />
    </div>
    <div className="space-y-3">
      <div className="h-4 w-full rounded bg-gray-200" />
      <div className="h-4 w-3/4 rounded bg-gray-200" />
      <div className="h-4 w-5/6 rounded bg-gray-200" />
    </div>
    <div className="mt-4 border-t border-gray-100 pt-4">
      <div className="h-4 w-24 rounded bg-gray-200" />
    </div>
  </div>
);

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg";
  className?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = "md",
  className = "",
}) => {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-8 h-8",
    lg: "w-12 h-12",
  };

  return (
    <motion.div
      className={`${sizeClasses[size]} ${className}`}
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
    >
      <svg className="h-full w-full" fill="none" viewBox="0 0 24 24">
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    </motion.div>
  );
};

export const PageLoadingState: React.FC = () => (
  <motion.div
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    exit={{ opacity: 0 }}
    className="fixed inset-0 z-50 flex items-center justify-center bg-white"
  >
    <div className="text-center">
      <LoadingSpinner size="lg" className="mx-auto mb-4 text-indigo-600" />
      <p className="text-gray-600">Loading experience...</p>
    </div>
  </motion.div>
);

interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  onLoad?: () => void;
}

export const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  className = "",
  onLoad,
}) => {
  const [loaded, setLoaded] = React.useState(false);
  const [error, setError] = React.useState(false);

  return (
    <div className={`relative ${className}`}>
      {!loaded && !error && <ImageSkeleton className="absolute inset-0" />}

      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <p className="text-sm text-gray-500">Failed to load image</p>
        </div>
      )}

      <img
        src={src}
        alt={alt}
        className={`${className} ${loaded ? "opacity-100" : "opacity-0"} transition-opacity duration-300`}
        onLoad={() => {
          setLoaded(true);
          onLoad?.();
        }}
        onError={() => setError(true)}
      />
    </div>
  );
};

// Progress bar for scroll
export const ScrollProgressBar: React.FC = () => {
  const [progress, setProgress] = React.useState(0);

  React.useEffect(() => {
    const updateProgress = () => {
      const scrollHeight =
        document.documentElement.scrollHeight - window.innerHeight;
      const scrolled = window.scrollY;
      const progress = (scrolled / scrollHeight) * 100;
      setProgress(progress);
    };

    window.addEventListener("scroll", updateProgress);
    updateProgress();

    return () => window.removeEventListener("scroll", updateProgress);
  }, []);

  return (
    <div className="fixed left-0 right-0 top-0 z-50 h-1 bg-gray-200">
      <motion.div
        className="h-full bg-gradient-to-r from-indigo-500 to-purple-500"
        style={{ width: `${progress}%` }}
        transition={{ duration: 0.1 }}
      />
    </div>
  );
};

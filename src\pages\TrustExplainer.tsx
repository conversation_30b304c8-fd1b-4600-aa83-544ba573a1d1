import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>he<PERSON> } from "lucide-react";
import React from "react";
import { Link, useParams } from "react-router-dom";

/**
 * Trust Explainer Page Component
 *
 * Detailed explanation page for trust and security features.
 * Dynamically displays content based on URL slug parameter.
 */

const TITLES: Record<string, string> = {
  consent: "Consent-First Data Model",
  provenance: "Data Provenance & Traceability",
  audit: "Comprehensive Auditing & RBAC",
  standards: "Adherence to Industry Standards",
};

const TrustExplainer: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const title = TITLES[slug || ""] || "Our Commitment to Trust";

  return (
    <div className="bg-neutral-50 dark:bg-neutral-900">
      <div className="mx-auto min-h-screen max-w-prose px-4 py-12 md:py-20">
        <header className="mb-10">
          <Link
            to="/"
            className="text-nav-item mb-8 inline-flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Home
          </Link>
          <div className="flex items-center gap-4">
            <ShieldCheck className="h-10 w-10 text-trust-verified" />
            <h1 className="text-display-small">{title}</h1>
          </div>
        </header>

        <article className="prose prose-slate dark:prose-invert max-w-none">
          <p className="text-body-lead text-muted-foreground">
            This page provides a detailed explanation of our approach to {title}
            . Our framework is built on a foundation of transparency, security,
            and user control, ensuring that your data is handled with the utmost
            care.
          </p>

          <h2 className="text-heading-subsection">Core Principles</h2>
          <p className="text-body-default">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed non
            risus. Suspendisse lectus tortor, dignissim sit amet, adipiscing
            nec, ultricies sed, dolor. Cras elementum ultrices diam. Maecenas
            ligula massa, varius a, semper congue, euismod non, mi.
          </p>

          <h2 className="text-heading-subsection">
            Implementation in Practice
          </h2>
          <p className="text-body-default">
            Proin porttitor, orci nec nonummy molestie, enim est eleifend mi,
            non fermentum diam nisl sit amet erat. Duis semper. Duis arcu massa,
            scelerisque vitae, consequat in, pretium a, enim. Pellentesque
            congue. Ut in risus volutpat libero pharetra tempor.
          </p>

          <ul className="text-body-default">
            <li>End-to-end encryption for all data in transit and at rest.</li>
            <li>
              Strict access controls based on the principle of least privilege.
            </li>
            <li>
              Regular third-party security audits and penetration testing.
            </li>
          </ul>
        </article>
      </div>
    </div>
  );
};

export default TrustExplainer;

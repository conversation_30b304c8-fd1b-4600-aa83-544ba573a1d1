import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Utility Functions
 *
 * Collection of utility functions for the application.
 */

/**
 * Combines class names using clsx and tailwind-merge
 *
 * @param inputs - Class values to combine
 * @returns Merged class string with Tailwind conflicts resolved
 */
export function cn(...inputs: ClassValue[]): string {
  return twMerge(clsx(inputs));
}

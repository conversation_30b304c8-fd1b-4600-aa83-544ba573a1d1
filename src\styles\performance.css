.will-change-transform {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}
.will-change-opacity {
  will-change: opacity;
}
.will-change-transform-opacity {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}
[data-scroll-container] {
  will-change: scroll-position;
}
[data-section] {
  will-change: transform, opacity;
  transform: translateZ(0);
}
.frame-container {
  will-change: transform, opacity, visibility;
  transform: translateZ(0);
  backface-visibility: hidden;
}
.animated-svg {
  will-change: transform;
  transform: translateZ(0);
}
.animated-path {
  will-change: stroke-dashoffset, opacity;
}
.badge {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}
.overlay-animation {
  will-change: opacity;
  pointer-events: none;
}
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  .will-change-transform,
  .will-change-opacity,
  .will-change-transform-opacity {
    will-change: auto;
  }
}
.perf-indicator {
  position: fixed;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  font-size: 12px;
  font-family: monospace;
  border-radius: 4px;
  z-index: 9999;
  pointer-events: none;
}
html {
  scroll-behavior: smooth;
}
@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}
.promote-layer {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
}

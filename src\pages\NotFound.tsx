import { Alert<PERSON>riangle } from "lucide-react";
import React, { useEffect } from "react";
import { Link, useLocation } from "react-router-dom";

/**
 * Not Found Page Component
 *
 * 404 error page with user-friendly messaging and navigation back to home.
 * Includes error logging for debugging purposes.
 */
const NotFound: React.FC = () => {
  const location = useLocation();
  useEffect(() => {
    console.error(
      `404 Error: User attempted to access non-existent route: ${location.pathname}`
    );
  }, [location.pathname]);

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-neutral-50 to-surface-cool p-4">
      <div className="w-full max-w-md rounded-2xl bg-card p-8 text-center shadow-medium transition-all hover:shadow-organic md:p-12">
        <div className="mb-6 flex justify-center">
          <div className="flex h-16 w-16 items-center justify-center rounded-full bg-destructive/10">
            <AlertTriangle className="h-8 w-8 text-destructive" />
          </div>
        </div>

        <h1 className="text-heading-major text-foreground">Page Not Found</h1>

        <p className="text-body-large mt-2 text-muted-foreground">
          The page you're looking for doesn't exist or has been moved.
        </p>

        <Link
          to="/"
          className="text-button mt-8 inline-flex h-12 items-center justify-center rounded-lg bg-primary px-6 font-medium text-primary-foreground transition-colors hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
        >
          Return to Home
        </Link>
      </div>
    </div>
  );
};

export default NotFound;

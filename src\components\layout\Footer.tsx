import { Linkedin, Mail, MapPin, Phone, Twitter } from "lucide-react";
import React from "react";

/**
 * Footer Component
 *
 * Main site footer with company information, navigation links, and contact details.
 * Includes social media links and legal information.
 */
const Footer: React.FC = () => {
  return (
    <footer className="bg-primary text-primary-foreground">
      <div className="container mx-auto px-6 py-16">
        <div className="grid gap-8 lg:grid-cols-4">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-white/20">
                <span className="text-sm font-bold text-white">OC</span>
              </div>
              <span className="text-xl font-bold">OncoConnect</span>
            </div>
            <p className="leading-relaxed text-primary-foreground/80">
              Revolutionizing cancer care through AI-powered healthcare
              interoperability, enabling evidence-based decisions that save
              lives across India.
            </p>
            <div className="flex space-x-4">
              <a
                href="https://linkedin.com/company/oncoconnect"
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary-foreground/60 transition-colors hover:text-white"
                aria-label="Follow us on LinkedIn"
              >
                <Linkedin className="h-5 w-5" />
              </a>
              <a
                href="https://twitter.com/oncoconnect"
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary-foreground/60 transition-colors hover:text-white"
                aria-label="Follow us on Twitter"
              >
                <Twitter className="h-5 w-5" />
              </a>
              <a
                href="mailto:<EMAIL>"
                className="text-primary-foreground/60 transition-colors hover:text-white"
                aria-label="Send us an email"
              >
                <Mail className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Solutions */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">Solutions</h4>
            <ul className="space-y-2 text-primary-foreground/80">
              <li>
                <a href="#" className="transition-colors hover:text-white">
                  Data Aggregation
                </a>
              </li>
              <li>
                <a href="#" className="transition-colors hover:text-white">
                  Longitudinal Profiles
                </a>
              </li>
              <li>
                <a href="#" className="transition-colors hover:text-white">
                  AI-Generated Reports
                </a>
              </li>
              <li>
                <a href="#" className="transition-colors hover:text-white">
                  FHIR Compliance
                </a>
              </li>
              <li>
                <a href="#" className="transition-colors hover:text-white">
                  ABDM Integration
                </a>
              </li>
            </ul>
          </div>

          {/* Resources */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">Resources</h4>
            <ul className="space-y-2 text-primary-foreground/80">
              <li>
                <a href="#" className="transition-colors hover:text-white">
                  White Paper
                </a>
              </li>
              <li>
                <a href="#" className="transition-colors hover:text-white">
                  Documentation
                </a>
              </li>
              <li>
                <a href="#" className="transition-colors hover:text-white">
                  API Reference
                </a>
              </li>
              <li>
                <a href="#" className="transition-colors hover:text-white">
                  Case Studies
                </a>
              </li>
              <li>
                <a href="#" className="transition-colors hover:text-white">
                  Support
                </a>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">Contact</h4>
            <div className="space-y-3 text-primary-foreground/80">
              <div className="flex items-start space-x-3">
                <MapPin className="mt-0.5 h-5 w-5 flex-shrink-0" />
                <span className="text-sm">
                  Bangalore, Karnataka
                  <br />
                  India
                </span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 flex-shrink-0" />
                <span className="text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 flex-shrink-0" />
                <span className="text-sm">+91 XXX XXX XXXX</span>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-12 border-t border-white/20 pt-8">
          <div className="flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0">
            <p className="text-sm text-primary-foreground/60">
              © 2025 OncoConnect. All rights reserved.
            </p>
            <div className="flex space-x-6 text-sm text-primary-foreground/60">
              <a href="#" className="transition-colors hover:text-white">
                Privacy Policy
              </a>
              <a href="#" className="transition-colors hover:text-white">
                Terms of Service
              </a>
              <a href="#" className="transition-colors hover:text-white">
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

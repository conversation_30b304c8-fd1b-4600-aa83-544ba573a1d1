"use client";

import { motion } from "framer-motion";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Microscope,
  ShieldCheck,
  Stethoscope,
} from "lucide-react";
import React from "react";

/**
 * Oncology Flow Component
 *
 * Interactive flowchart visualization for oncology workflow.
 * Apollo-style design centered on a vertical axis with responsive behavior.
 * Axis aligns to CSS var --hero-axis-x for consistent spacing control.
 */

// ---------- Config ----------
// Using CSS vars for responsive behavior
const BRANCH_OFFSET = "var(--branch-offset, 140px)";
const LEFT_CARD_OFFSET = "var(--left-card-offset, -280px)";

// ---------- Types ----------
interface FlowItemBase {
  id: string;
}
interface FlowCardItem extends FlowItemBase {
  k: string;
  title: string;
  chips: string[];
  gradient: string;
  icon: React.ReactNode;
  offset?: number; // horizontal offset in px (negative = left)
  branch?: "left" | "right" | "center";
}
interface FlowGateItem extends FlowItemBase {
  gate: "TRUE_FALSE_SPLIT";
}
export type FlowItem = FlowCardItem | FlowGateItem;

// ---------- Component ----------
export default function OncologyFlow({
  className = "",
}: {
  className?: string;
}) {
  const AXIS_X = "var(--hero-axis-x, 50vw)";

  // Responsive card offset for mobile
  const getCardOffset = () => {
    if (typeof window !== "undefined" && window.innerWidth < 768) {
      return -120; // Smaller offset for mobile
    }
    return -280; // Default offset
  };

  const steps: FlowItem[] = [
    {
      id: "enroll",
      k: "PATIENT INTAKE & CONSENT",
      title: "Patient verified & consented",
      chips: ["ABDM linked", "Outside records attached"],
      icon: (
        <ClipboardCheck
          className="h-5 w-5"
          aria-hidden="true"
          strokeWidth={2}
        />
      ),
      gradient: "from-cyan-400 via-blue-400 to-violet-400",
      branch: "center",
    },
    {
      id: "research",
      k: "RECONCILE RECORDS",
      title: "Unify history, labs & imaging into one view",
      chips: ["Latest regimen & lines", "Key labs & imaging summary"],
      icon: (
        <Stethoscope className="h-5 w-5" aria-hidden="true" strokeWidth={2} />
      ),
      gradient: "from-fuchsia-400 via-violet-400 to-cyan-400",
      branch: "center",
    },
    { id: "gate", gate: "TRUE_FALSE_SPLIT" },
    {
      id: "enrich",
      k: "TUMOR BOARD PREP",
      title: "Clinical summary ready",
      chips: ["Stage & biomarkers", "Performance status"],
      icon: (
        <Microscope className="h-5 w-5" aria-hidden="true" strokeWidth={2} />
      ),
      gradient: "from-indigo-400 via-blue-400 to-cyan-400",
      offset:
        typeof window !== "undefined" && window.innerWidth < 768 ? -120 : -280,
      branch: "left",
    },
    {
      id: "sequence",
      k: "PLAN & SAFETY", // oncologist-facing wording
      title: "Proposed regimen with checks",
      chips: ["DDI/allergy alerts", "Trial matches"],
      icon: (
        <ShieldCheck className="h-5 w-5" aria-hidden="true" strokeWidth={2} />
      ),
      gradient: "from-pink-400 via-fuchsia-400 to-violet-400",
      offset:
        typeof window !== "undefined" && window.innerWidth < 768 ? -120 : -280,
      branch: "left",
    },
  ];

  // Lightweight runtime checks (dev "tests")
  if (process.env.NODE_ENV !== "production") {
    console.assert(
      steps.length === 5,
      "Expected 5 flow items (2 cards, split gate, 2 cards)."
    );
    console.assert(
      steps.some((s) => (s as FlowGateItem).gate === "TRUE_FALSE_SPLIT"),
      "Expected a split gate present."
    );
  }

  return (
    <section
      className={`relative isolate overflow-visible ${className}`}
      style={{ height: "var(--flow-h, 720px)", width: "100%" }}
    >
      {/* Center rail behind cards (aligned to the hero axis) */}
      <div
        className="absolute bottom-0 top-0 z-0 w-px bg-slate-300/45"
        style={{ left: AXIS_X }}
      />

      {/* Flow stack centered on axis */}
      <div
        className="relative z-10"
        style={{
          position: "absolute",
          left: AXIS_X,
          top: "50%",
          transform: "translate(-50%, -50%)",
          width: "max-content",
        }}
      >
        <div className="mx-auto flex max-w-3xl flex-col gap-8 sm:gap-9">
          {steps.map((s, i) => (
            <motion.div
              key={s.id}
              initial={{ opacity: 0, y: 14 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.5 }}
              transition={{
                duration: 0.45,
                ease: [0.22, 1, 0.36, 1],
                delay: i * 0.04,
              }}
              className="relative"
              style={
                s.id !== "gate" && (s as FlowCardItem).offset !== undefined
                  ? { marginLeft: (s as FlowCardItem).offset }
                  : undefined
              }
            >
              {s.id === "gate" ? (
                <SplitGate axisX={AXIS_X} />
              ) : (
                <>
                  {/* Elbows from the appropriate rail to the card */}
                  {(s as FlowCardItem).branch === "left" && (
                    <ElbowFrom
                      origin="left"
                      axisX={AXIS_X}
                      branchOffset={BRANCH_OFFSET}
                      cardOffset={(s as FlowCardItem).offset ?? 0}
                    />
                  )}
                  {(s as FlowCardItem).branch === "right" && (
                    <ElbowFrom
                      origin="right"
                      axisX={AXIS_X}
                      branchOffset={BRANCH_OFFSET}
                      cardOffset={(s as FlowCardItem).offset ?? 0}
                    />
                  )}
                  <FlowCard
                    k={(s as FlowCardItem).k}
                    title={(s as FlowCardItem).title}
                    chips={(s as FlowCardItem).chips}
                    gradient={(s as FlowCardItem).gradient}
                    icon={(s as FlowCardItem).icon}
                  />
                </>
              )}
            </motion.div>
          ))}
        </div>
      </div>

      {/* Continuous branch rails beneath the split */}
      <BranchRails axisX={AXIS_X} />
    </section>
  );
}

// ---------- Presentational bits ----------
function SplitGate({ axisX }: { axisX: string }) {
  return (
    <div className="relative h-20">
      {/* center rail segments */}
      <div
        className="absolute top-0 h-1/2 w-px bg-slate-300/60"
        style={{ left: "50%", transform: "translateX(-50%)" }}
      />
      <div
        className="absolute bottom-0 h-1/2 w-px bg-slate-300/25"
        style={{ left: "50%", transform: "translateX(-50%)" }}
      />

      {/* left + right arms to branch rails */}
      <div
        className="absolute top-1/2"
        style={{ left: "50%", transform: "translate(-50%, -50%)" }}
      >
        {/* left arm */}
        <div
          className="absolute left-0 top-0 h-px bg-slate-300/60"
          style={{ width: "var(--branch-offset, 140px)" }}
        />
        {/* right arm */}
        <div
          className="absolute right-0 top-0 h-px bg-slate-300/60"
          style={{ width: "var(--branch-offset, 140px)" }}
        />
      </div>

      {/* TRUE pill on left arm */}
      <div
        className="absolute top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-slate-800/95 px-3 py-1 text-[11px] font-semibold tracking-wide text-white shadow-sm ring-1 ring-black/20"
        style={{ left: "calc(50% - 80px)" }}
      >
        TRUE
      </div>
      {/* FALSE pill on right arm */}
      <div
        className="absolute top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-slate-800/60 px-3 py-1 text-[11px] font-semibold tracking-wide text-white/90 shadow-sm ring-1 ring-black/10"
        style={{ left: "calc(50% + 80px)" }}
      >
        FALSE
      </div>
    </div>
  );
}

function BranchRails({ axisX }: { axisX: string }) {
  // Vertical rails that continue below the gate
  return (
    <>
      {/* left branch rail */}
      <div
        className="pointer-events-none absolute z-0 bg-slate-300/40"
        style={{
          left: `calc(${axisX} - ${BRANCH_OFFSET})`,
          top: 0,
          bottom: 0,
          width: 1,
        }}
      />
      {/* right branch rail */}
      <div
        className="pointer-events-none absolute z-0 bg-slate-300/40"
        style={{
          left: `calc(${axisX} + ${BRANCH_OFFSET})`,
          top: 0,
          bottom: 0,
          width: 1,
        }}
      />
    </>
  );
}

function ElbowFrom({
  origin,
  axisX,
  branchOffset,
  cardOffset,
}: {
  origin: "left" | "right";
  axisX: string;
  branchOffset: any;
  cardOffset: number;
}) {
  // Horizontal connector from a branch rail to a shifted card
  const fromLeft =
    origin === "left"
      ? `calc(50% - ${branchOffset})`
      : `calc(50% + ${branchOffset})`;
  // Responsive width calculation - use a fixed reasonable width for mobile
  const connectionWidth =
    typeof window !== "undefined" && window.innerWidth < 768 ? 80 : 120;

  return (
    <div
      className="pointer-events-none absolute top-1/2 -translate-y-1/2"
      style={{ left: fromLeft, width: connectionWidth }}
    >
      <div className="h-px w-full bg-slate-300/60" />
    </div>
  );
}

function FlowCard({
  k,
  title,
  chips,
  icon,
  gradient,
}: {
  k: string;
  title: string;
  chips: string[];
  icon: React.ReactNode;
  gradient: string;
}) {
  return (
    <div className="relative mx-auto" style={{ width: "var(--card-w, 620px)" }}>
      <div className="rounded-2xl bg-white/95 p-5 shadow-[0_12px_34px_rgba(2,6,23,0.12)] ring-1 ring-black/5 backdrop-blur">
        <div className="flex items-start gap-4">
          <IconGrad className={`bg-gradient-to-br ${gradient}`}>
            {icon}
          </IconGrad>
          <div className="flex-1">
            <div className="text-[11px] font-semibold tracking-[0.18em] text-slate-400">
              {k}
            </div>
            <div className="mt-1 text-[16px] font-medium text-slate-800">
              {title}
            </div>
            <div className="mt-2 flex flex-wrap gap-2">
              {chips.map((t) => (
                <Chip key={t}>{t}</Chip>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function IconGrad({
  children,
  className = "",
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <div
      className={`grid h-11 w-11 place-items-center rounded-full text-white shadow-inner shadow-black/20 ring-1 ring-white/40 ${className}`}
    >
      {children}
    </div>
  );
}

function Chip({ children }: { children: React.ReactNode }) {
  return (
    <span className="rounded-md bg-white/80 px-2 py-1 text-[13px] font-medium text-slate-800 shadow-sm ring-1 ring-black/10">
      {children}
    </span>
  );
}

// ---------- Simple visual tests (dev aid) ----------
export function OncologyFlowTestPage() {
  return (
    <div className="space-y-12 p-8">
      <h2 className="text-sm font-semibold text-slate-600">Default preview</h2>
      <OncologyFlow />
      <h2 className="text-sm font-semibold text-slate-600">
        Narrow container preview
      </h2>
      <div className="max-w-3xl">
        <OncologyFlow />
      </div>
    </div>
  );
}

"use client";

import { motion, useReducedMotion } from "framer-motion";
import {
  C<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Microscope,
  ShieldCheck,
  Stethoscope,
} from "lucide-react";
import React, { memo, useCallback, useMemo } from "react";

/**
 * Oncology Flow Component
 *
 * Optimized interactive flowchart visualization for oncology workflow.
 * Features performance optimizations, accessibility improvements, and responsive design.
 */

// ---------- Config ----------
// Optimized responsive values
const BRANCH_OFFSET = "var(--branch-offset, 120px)"; // Reduced for mobile
const LEFT_CARD_OFFSET = "var(--left-card-offset, -240px)"; // Reduced for mobile

// ---------- Types ----------
interface FlowItemBase {
  id: string;
}
interface FlowCardItem extends FlowItemBase {
  k: string;
  title: string;
  chips: string[];
  gradient: string;
  icon: React.ReactNode;
  offset?: number; // horizontal offset in px (negative = left)
  branch?: "left" | "right" | "center";
}
interface FlowGateItem extends FlowItemBase {
  gate: "TRUE_FALSE_SPLIT";
}
export type FlowItem = FlowCardItem | FlowGateItem;

interface OncologyFlowProps {
  /** Optional CSS class name */
  className?: string;
  /** Callback when a card is clicked */
  onCardClick?: (cardId: string) => void;
  /** Whether to enable animations */
  enableAnimations?: boolean;
}

// ---------- Component ----------
const OncologyFlow: React.FC<OncologyFlowProps> = memo(({
  className = "",
  onCardClick,
  enableAnimations = true
}) => {
  const prefersReducedMotion = useReducedMotion();
  const shouldAnimate = enableAnimations && !prefersReducedMotion;
  const AXIS_X = "var(--hero-axis-x, 50vw)";

  // Optimized responsive card offset
  const getCardOffset = useCallback(() => {
    if (typeof window !== "undefined" && window.innerWidth < 768) {
      return -100; // Smaller offset for mobile
    }
    return -240; // Reduced default offset
  }, []);

  const handleCardClick = useCallback((cardId: string) => {
    onCardClick?.(cardId);
  }, [onCardClick]);

  const steps: FlowItem[] = useMemo(() => [
    {
      id: "enroll",
      k: "PATIENT INTAKE & CONSENT",
      title: "Patient verified & consented",
      chips: ["ABDM linked", "Outside records attached"],
      icon: (
        <ClipboardCheck
          className="h-4 w-4"
          aria-hidden="true"
          strokeWidth={2}
        />
      ),
      gradient: "from-cyan-400 via-blue-400 to-violet-400",
      branch: "center",
    },
    {
      id: "research",
      k: "RECONCILE RECORDS",
      title: "Unify history, labs & imaging into one view",
      chips: ["Latest regimen & lines", "Key labs & imaging summary"],
      icon: (
        <Stethoscope className="h-4 w-4" aria-hidden="true" strokeWidth={2} />
      ),
      gradient: "from-fuchsia-400 via-violet-400 to-cyan-400",
      branch: "center",
    },
    { id: "gate", gate: "TRUE_FALSE_SPLIT" },
    {
      id: "enrich",
      k: "TUMOR BOARD PREP",
      title: "Clinical summary ready",
      chips: ["Stage & biomarkers", "Performance status"],
      icon: (
        <Microscope className="h-4 w-4" aria-hidden="true" strokeWidth={2} />
      ),
      gradient: "from-indigo-400 via-blue-400 to-cyan-400",
      offset: getCardOffset(),
      branch: "left",
    },
    {
      id: "sequence",
      k: "PLAN & SAFETY",
      title: "Proposed regimen with checks",
      chips: ["DDI/allergy alerts", "Trial matches"],
      icon: (
        <ShieldCheck className="h-4 w-4" aria-hidden="true" strokeWidth={2} />
      ),
      gradient: "from-pink-400 via-fuchsia-400 to-violet-400",
      offset: getCardOffset(),
      branch: "left",
    },
  ], [getCardOffset]);

  // Lightweight runtime checks (dev "tests")
  if (process.env.NODE_ENV !== "production") {
    console.assert(
      steps.length === 5,
      "Expected 5 flow items (2 cards, split gate, 2 cards)."
    );
    console.assert(
      steps.some((s) => (s as FlowGateItem).gate === "TRUE_FALSE_SPLIT"),
      "Expected a split gate present."
    );
  }

  return (
    <section
      className={`relative isolate overflow-visible ${className}`}
      style={{ height: "var(--flow-h, 600px)", width: "100%" }} // Reduced height
      role="img"
      aria-label="Oncology workflow diagram"
    >
      {/* Center rail behind cards (aligned to the hero axis) */}
      <div
        className="absolute bottom-0 top-0 z-0 w-px bg-slate-300/45"
        style={{ left: AXIS_X }}
        aria-hidden="true"
      />

      {/* Flow stack centered on axis */}
      <div
        className="relative z-10"
        style={{
          position: "absolute",
          left: AXIS_X,
          top: "50%",
          transform: "translate(-50%, -50%)",
          width: "max-content",
        }}
      >
        <div className="mx-auto flex max-w-2xl flex-col gap-6 sm:gap-7"> {/* Reduced spacing */}
          {steps.map((s, i) => (
            <motion.div
              key={s.id}
              initial={shouldAnimate ? { opacity: 0, y: 12 } : false}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.5 }}
              transition={{
                duration: shouldAnimate ? 0.35 : 0,
                ease: [0.22, 1, 0.36, 1],
                delay: shouldAnimate ? i * 0.03 : 0,
              }}
              className="relative"
              style={
                s.id !== "gate" && (s as FlowCardItem).offset !== undefined
                  ? { marginLeft: (s as FlowCardItem).offset }
                  : undefined
              }
            >
              {s.id === "gate" ? (
                <SplitGate axisX={AXIS_X} />
              ) : (
                <>
                  {/* Elbows from the appropriate rail to the card */}
                  {(s as FlowCardItem).branch === "left" && (
                    <ElbowFrom
                      origin="left"
                      axisX={AXIS_X}
                      branchOffset={BRANCH_OFFSET}
                      cardOffset={(s as FlowCardItem).offset ?? 0}
                    />
                  )}
                  {(s as FlowCardItem).branch === "right" && (
                    <ElbowFrom
                      origin="right"
                      axisX={AXIS_X}
                      branchOffset={BRANCH_OFFSET}
                      cardOffset={(s as FlowCardItem).offset ?? 0}
                    />
                  )}
                  <FlowCard
                    k={(s as FlowCardItem).k}
                    title={(s as FlowCardItem).title}
                    chips={(s as FlowCardItem).chips}
                    gradient={(s as FlowCardItem).gradient}
                    icon={(s as FlowCardItem).icon}
                    onClick={() => handleCardClick(s.id)}
                  />
                </>
              )}
            </motion.div>
          ))}
        </div>
      </div>

      {/* Continuous branch rails beneath the split */}
      <BranchRails axisX={AXIS_X} />
    </section>
  );
}

// ---------- Presentational bits ----------
const SplitGate = memo(({ axisX }: { axisX: string }) => {
  return (
    <div className="relative h-16" role="group" aria-label="Decision split"> {/* Reduced height */}
      {/* center rail segments */}
      <div
        className="absolute top-0 h-1/2 w-px bg-slate-300/60"
        style={{ left: "50%", transform: "translateX(-50%)" }}
        aria-hidden="true"
      />
      <div
        className="absolute bottom-0 h-1/2 w-px bg-slate-300/25"
        style={{ left: "50%", transform: "translateX(-50%)" }}
        aria-hidden="true"
      />

      {/* left + right arms to branch rails */}
      <div
        className="absolute top-1/2"
        style={{ left: "50%", transform: "translate(-50%, -50%)" }}
        aria-hidden="true"
      >
        {/* left arm */}
        <div
          className="absolute left-0 top-0 h-px bg-slate-300/60"
          style={{ width: "var(--branch-offset, 120px)" }} // Reduced width
        />
        {/* right arm */}
        <div
          className="absolute right-0 top-0 h-px bg-slate-300/60"
          style={{ width: "var(--branch-offset, 120px)" }} // Reduced width
        />
      </div>

      {/* TRUE pill on left arm */}
      <div
        className="absolute top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-slate-800/95 px-2.5 py-1 text-[10px] font-semibold tracking-wide text-white shadow-sm ring-1 ring-black/20"
        style={{ left: "calc(50% - 60px)" }} // Reduced offset
        role="img"
        aria-label="True branch"
      >
        TRUE
      </div>
      {/* FALSE pill on right arm */}
      <div
        className="absolute top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-slate-800/60 px-2.5 py-1 text-[10px] font-semibold tracking-wide text-white/90 shadow-sm ring-1 ring-black/10"
        style={{ left: "calc(50% + 60px)" }} // Reduced offset
        role="img"
        aria-label="False branch"
      >
        FALSE
      </div>
    </div>
  );
});

const BranchRails = memo(({ axisX }: { axisX: string }) => {
  // Vertical rails that continue below the gate
  return (
    <>
      {/* left branch rail */}
      <div
        className="pointer-events-none absolute z-0 bg-slate-300/40"
        style={{
          left: `calc(${axisX} - ${BRANCH_OFFSET})`,
          top: 0,
          bottom: 0,
          width: 1,
        }}
        aria-hidden="true"
      />
      {/* right branch rail */}
      <div
        className="pointer-events-none absolute z-0 bg-slate-300/40"
        style={{
          left: `calc(${axisX} + ${BRANCH_OFFSET})`,
          top: 0,
          bottom: 0,
          width: 1,
        }}
        aria-hidden="true"
      />
    </>
  );
});

const ElbowFrom = memo(({
  origin,
  axisX,
  branchOffset,
  cardOffset,
}: {
  origin: "left" | "right";
  axisX: string;
  branchOffset: any;
  cardOffset: number;
}) => {
  // Horizontal connector from a branch rail to a shifted card
  const fromLeft =
    origin === "left"
      ? `calc(50% - ${branchOffset})`
      : `calc(50% + ${branchOffset})`;

  // Optimized responsive width calculation
  const connectionWidth = useMemo(() => {
    if (typeof window !== "undefined" && window.innerWidth < 768) {
      return 60; // Smaller width for mobile
    }
    return 100; // Reduced default width
  }, []);

  return (
    <div
      className="pointer-events-none absolute top-1/2 -translate-y-1/2"
      style={{ left: fromLeft, width: connectionWidth }}
      aria-hidden="true"
    >
      <div className="h-px w-full bg-slate-300/60" />
    </div>
  );
});

const FlowCard = memo(({
  k,
  title,
  chips,
  icon,
  gradient,
  onClick,
}: {
  k: string;
  title: string;
  chips: string[];
  icon: React.ReactNode;
  gradient: string;
  onClick?: () => void;
}) => {
  return (
    <div className="relative mx-auto" style={{ width: "var(--card-w, 520px)" }}> {/* Reduced width */}
      <button
        className="w-full rounded-xl bg-white/95 p-4 shadow-[0_8px_24px_rgba(2,6,23,0.1)] ring-1 ring-black/5 backdrop-blur transition-all duration-200 hover:shadow-[0_12px_32px_rgba(2,6,23,0.15)] hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2"
        onClick={onClick}
        aria-label={`${k}: ${title}`}
      >
        <div className="flex items-start gap-3"> {/* Reduced gap */}
          <IconGrad className={`bg-gradient-to-br ${gradient}`}>
            {icon}
          </IconGrad>
          <div className="flex-1 text-left">
            <div className="text-[10px] font-semibold tracking-[0.15em] text-slate-400">
              {k}
            </div>
            <div className="mt-1 text-[14px] font-medium text-slate-800 leading-tight">
              {title}
            </div>
            <div className="mt-2 flex flex-wrap gap-1.5"> {/* Reduced gap */}
              {chips.map((t) => (
                <Chip key={t}>{t}</Chip>
              ))}
            </div>
          </div>
        </div>
      </button>
    </div>
  );
});

const IconGrad = memo(({
  children,
  className = "",
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <div
      className={`grid h-9 w-9 place-items-center rounded-full text-white shadow-inner shadow-black/20 ring-1 ring-white/40 flex-shrink-0 ${className}`} // Reduced size
    >
      {children}
    </div>
  );
});

const Chip = memo(({ children }: { children: React.ReactNode }) => {
  return (
    <span className="rounded-md bg-white/80 px-2 py-0.5 text-[12px] font-medium text-slate-800 shadow-sm ring-1 ring-black/10">
      {children}
    </span>
  );
});

// Set display names for debugging
SplitGate.displayName = 'SplitGate';
BranchRails.displayName = 'BranchRails';
ElbowFrom.displayName = 'ElbowFrom';
FlowCard.displayName = 'FlowCard';
IconGrad.displayName = 'IconGrad';
Chip.displayName = 'Chip';

OncologyFlow.displayName = 'OncologyFlow';

export default OncologyFlow;
export type { OncologyFlowProps };

// ---------- Simple visual tests (dev aid) ----------
export const OncologyFlowTestPage = memo(() => {
  return (
    <div className="space-y-8 p-6"> {/* Reduced spacing */}
      <h2 className="text-sm font-semibold text-slate-600">Default preview</h2>
      <OncologyFlow />
      <h2 className="text-sm font-semibold text-slate-600">
        Narrow container preview
      </h2>
      <div className="max-w-2xl"> {/* Reduced width */}
        <OncologyFlow />
      </div>
    </div>
  );
});

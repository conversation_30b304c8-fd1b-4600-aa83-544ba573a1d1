"use client";

import React from "react";
import { Button } from "@/components/ui/button";

const Chip = ({ children }: { children: React.ReactNode }) => (
  <span className="inline-flex items-center gap-1 rounded-full border border-white bg-white/90 px-3 py-1 text-xs font-medium text-slate-800">
    {children}
  </span>
);

const Card = ({
  title,
  subtitle,
  chips,
}: {
  title: string;
  subtitle: string;
  chips: string[];
}) => (
  <div className="w-[520px] max-w-[86vw] rounded-2xl border border-slate-200 bg-white px-6 py-5 shadow-xl">
    <div className="mb-1 text-[11px] font-semibold uppercase tracking-wider text-slate-500">
      {subtitle}
    </div>
    <div className="font-medium text-slate-900">{title}</div>
    <div className="mt-3 flex flex-wrap gap-2">
      {chips.map((c) => (
        <Chip key={c}>{c}</Chip>
      ))}
    </div>
  </div>
);

const FlowHero: React.FC = () => {
  return (
    <section className="relative min-h-[100dvh] overflow-hidden bg-[radial-gradient(ellipse_at_top_left,_rgba(251,207,232,0.55),_transparent_50%),radial-gradient(ellipse_at_bottom_right,_rgba(165,243,252,0.55),_transparent_50%),radial-gradient(ellipse_at_center,_rgba(196,181,253,0.45),_transparent_40%)] md:min-h-screen">
      <div className="pointer-events-none absolute inset-0">
        <div className="absolute -top-20 left-1/3 h-[40vh] w-[50vw] rounded-full bg-white/40 blur-3xl" />
        <div className="absolute bottom-0 right-1/4 h-[35vh] w-[40vw] rounded-full bg-white/30 blur-3xl" />
      </div>

      <div className="container relative z-10 mx-auto px-6 pb-24 pt-28">
        <div className="grid items-start gap-16 lg:grid-cols-2">
          {/* Left copy */}
          <div>
            <h1 className="text-6xl font-light leading-[1.05] text-slate-900 md:text-7xl lg:text-8xl">
              Stop hunting for
              <br />
              <em className="font-light italic">patient data</em>
              <br />
              across systems.
            </h1>
            <p className="mt-6 max-w-xl text-lg text-slate-600">
              Get complete clinical context in 60 seconds. AI‑powered summaries
              from all your systems, ready for tumor board decisions.
            </p>
            <div className="mt-6 flex items-center gap-3">
              <input
                placeholder="Enter email"
                className="h-10 w-56 rounded-md border border-slate-300 px-3 text-sm"
              />
              <Button className="h-10 border-0 bg-yellow-400 px-5 text-black hover:bg-yellow-500">
                Watch the demo
              </Button>
            </div>
          </div>

          {/* Right flow cards */}
          <div className="relative mt-4 flex flex-col items-start gap-6">
            <div className="flex flex-col gap-6">
              <Card
                subtitle="Patient intake & consent"
                title="Patient verified & consented"
                chips={["ABDM linked", "Outside records attached"]}
              />
              <Card
                subtitle="Reconcile records"
                title="Unify history, labs & imaging into one view"
                chips={["Latest regimen & lines", "Key labs & imaging summary"]}
              />
            </div>
            <div className="relative h-10 w-[520px] max-w-[86vw]">
              <div className="absolute left-1/2 top-0 h-10 w-px -translate-x-1/2 bg-slate-300" />
              <div className="absolute left-1/2 top-1/2 -mt-3 flex -translate-x-1/2 items-center gap-16">
                <span className="rounded-full bg-white px-3 py-1 text-[10px] font-semibold tracking-wider text-slate-600 shadow-sm">
                  TRUE
                </span>
                <span className="rounded-full bg-white px-3 py-1 text-[10px] font-semibold tracking-wider text-slate-600 shadow-sm">
                  FALSE
                </span>
              </div>
            </div>
            <div className="flex flex-col gap-6">
              <Card
                subtitle="Tumor board prep"
                title="Clinical summary ready"
                chips={["Stage & biomarkers", "Performance status"]}
              />
              <Card
                subtitle="Plan & safety"
                title="Proposed regimen with checks"
                chips={["DDI/allergy alerts", "Trial matches"]}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FlowHero;
